{"dependencies": {"@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@polar-sh/sdk": "^0.34.3", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/logs": "workspace:*", "chargebee-typescript": "^2.48.0", "stripe": "^18.3.0", "ufo": "^1.6.1"}, "devDependencies": {"@biomejs/biome": "2.1.0", "@repo/tsconfig": "workspace:*", "@types/node": "^24.0.10"}, "main": "./index.ts", "exports": {".": {"types": "./index.ts", "default": "./index.ts"}, "./types": "./types.ts", "./lib/helper": "./src/lib/helper.ts"}, "name": "@repo/payments", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.ts", "version": "0.0.0"}