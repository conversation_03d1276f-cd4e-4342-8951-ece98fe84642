{"admin": {"menu": {"organizations": "Organizações", "users": "Usuários"}, "organizations": {"backToList": "Voltar para organizações", "confirmDelete": {"confirm": "Excluir", "message": "Tem certeza de que deseja excluir esta organização? Esta ação não pode ser desfeita.", "title": "Excluir organização"}, "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "deleteOrganization": {"deleted": "Organização foi excluída com sucesso!", "deleting": "Excluindo organização...", "notDeleted": "Organização não pôde ser excluída. Tente novamente."}, "edit": "<PERSON><PERSON>", "form": {"createTitle": "Criar uma organização", "name": "Nome da organização", "notifications": {"error": "Não foi possível salvar a organização. Tente novamente mais tarde.", "success": "Organização foi salva."}, "save": "<PERSON><PERSON>", "updateTitle": "Editar organização"}, "loading": "Carregando organizações...", "membersCount": "{count} {count, plural, one {membro} other {membros}}", "search": "Pesquisar por uma organização...", "title": "Gerenciar organizações"}, "title": "Administração", "users": {"confirmDelete": {"confirm": "Excluir", "message": "Tem certeza de que deseja excluir este usuário? Esta ação não pode ser desfeita.", "title": "Excluir usuário"}, "delete": "Excluir", "deleteUser": {"deleted": "Usuário foi excluído com sucesso!", "deleting": "Excluindo usuário...", "notDeleted": "Usuário não pôde ser excluído. Tente novamente."}, "emailVerified": {"verified": "<PERSON>ail verificado", "waiting": "Email aguardando verificação"}, "impersonate": "Personificar", "impersonation": {"impersonating": "Personificando como {name}..."}, "loading": "Carregando usuários...", "resendVerificationMail": {"error": "Não foi possível reenviar o email de verificação. Tente novamente.", "submitting": "Reenviando email de verificação...", "success": "Email de verificação foi enviado.", "title": "Reenviar email de verificação"}, "search": "Pesquisar por nome ou email...", "title": "Gerenciar usuários", "assignAdminRole": "Atribuir função de administrador", "removeAdminRole": "Remover função de administrador"}, "description": "Gerencie sua aplicação."}, "app": {"menu": {"accountSettings": "<PERSON><PERSON>", "admin": "Admin", "aiChatbot": "Chatbot IA", "financial": "Financeiro", "organizationSettings": "Organização", "start": "Iniciar"}, "userMenu": {"accountSettings": "<PERSON><PERSON>", "colorMode": "<PERSON><PERSON> de <PERSON>", "documentation": "Documentação", "home": "Início", "logout": "<PERSON><PERSON>"}}, "auth": {"errors": {"invalidEmailOrPassword": "As credenciais que você inseriu são inválidas. Verifique-as e tente novamente.", "unknown": "Algo deu errado. Tente novamente.", "userNotFound": "Este usuário não existe", "failedToCreateUser": "Não foi possível criar o usuário. Tente novamente.", "failedToCreateSession": "Não foi possível criar uma sessão. Tente novamente.", "failedToUpdateUser": "Não foi possível atualizar o usuário. Tente novamente.", "failedToGetSession": "Não foi possível obter a sessão.", "invalidPassword": "A senha inserida está incorreta.", "invalidEmail": "O email inserido é inválido.", "invalidToken": "O token que você inseriu é inválido ou expirou.", "credentialAccountNotFound": "Conta não encontrada.", "emailCanNotBeUpdated": "Email não pôde ser atualizado. Tente novamente.", "emailNotVerified": "Por favor, verifique seu email primeiro antes de fazer login.", "failedToGetUserInfo": "Não foi possível carregar as informações do usuário.", "idTokenNotSupported": "Token de ID não é suportado.", "passwordTooLong": "Senha é muito longa.", "passwordTooShort": "Senha é muito curta.", "providerNotFound": "Este provedor não é suportado.", "socialAccountAlreadyLinked": "Esta conta já está vinculada a um usuário.", "userEmailNotFound": "Email não encontrado.", "userAlreadyExists": "Este usuário já existe.", "invalidInvitation": "O convite é inválido ou expirou.", "sessionExpired": "A sessão expirou.", "failedToUnlinkLastAccount": "Falha ao desvincular conta", "accountNotFound": "Conta não encontrada"}, "forgotPassword": {"backToSignin": "Voltar para login", "email": "Email", "hints": {"linkNotSent": {"message": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não conseguimos enviar um link para redefinir sua senha. Tente novamente mais tarde.", "title": "Link não enviado"}, "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}}, "message": "Digite seu endereço de email e enviaremos um link para redefinir sua senha.", "submit": "Enviar link", "title": "Esqueceu sua senha?"}, "login": {"continueWith": "Ou continue com", "createAnAccount": "Criar uma conta", "dontHaveAnAccount": "Ainda não tem uma conta?", "forgotPassword": "Esque<PERSON>u a senha?", "hints": {"invalidCredentials": "O email ou senha que você inseriu são inválidos. Tente novamente.", "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}}, "loginWithPasskey": "Entrar com chave de acesso", "modes": {"magicLink": "<PERSON>", "password": "<PERSON><PERSON>"}, "submit": "Entrar", "subtitle": "Digite suas credenciais para entrar.", "title": "Bem-vindo de volta", "sendMagicLink": "Enviar link mágico"}, "resetPassword": {"backToSignin": "Voltar para login", "hints": {"error": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não conseguimos redefinir sua senha. Tente novamente.", "success": "Sua senha foi redefinida com sucesso."}, "message": "Digite uma nova senha.", "newPassword": "Nova senha", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Redefina sua senha"}, "signup": {"alreadyHaveAccount": "Já tem uma conta?", "email": "Email", "hints": {"signupFailed": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não conseguimos criar sua conta. Tente novamente mais tarde.", "verifyEmail": "Enviamos um link para verificar seu email. Verifique sua caixa de entrada."}, "message": "Ficamos felizes que você queira se juntar a nós. Preencha o formulário abaixo para criar sua conta.", "name": "Nome", "password": "<PERSON><PERSON>", "signIn": "Entrar", "submit": "C<PERSON><PERSON> conta", "title": "Criar uma conta"}, "verify": {"title": "Verifique sua conta", "code": "Senha de uso único", "submit": "Verificar", "backToSignin": "Voltar para login", "message": "Digite a senha de uso único do seu aplicativo autenticador para continuar."}}, "blog": {"description": "<PERSON><PERSON> as últimas notícias da nossa empresa", "title": "Meu blog incrível", "back": "Voltar para o blog"}, "changelog": {"description": "Mantenha-se atualizado com as últimas mudanças em nosso produto.", "title": "Registro de mudanças"}, "common": {"confirmation": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Excluir", "message": "Tem certeza de que deseja continuar? Esta ação não pode ser desfeita.", "title": "Confirmação"}, "errors": {"somethingWentWrong": "Algo deu errado. Tente novamente.", "notFound": "Não encontrado", "unauthorized": "Não autorizado", "forbidden": "Proibido", "internalServerError": "Erro interno do servidor"}, "loading": "Carregando...", "noData": "<PERSON>enhum dado encontrado", "search": "Pesquisar...", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "submit": "Enviar", "reset": "Redefinir", "close": "<PERSON><PERSON><PERSON>", "open": "Abrir", "yes": "<PERSON>m", "no": "Não", "ok": "OK", "apply": "Aplicar", "clear": "Limpar", "select": "Selecionar", "all": "Todos", "none": "<PERSON><PERSON><PERSON>", "required": "Obrigatório", "optional": "Opcional"}, "dashboard": {"title": "Painel de controle", "description": "Gerencie sua aplicação e visualize estatísticas importantes.", "stats": {"totalUsers": "Total de usuários", "totalOrganizations": "Total de organizações", "activeUsers": "Usuários ativos", "totalRevenue": "Receita total"}, "recentActivity": "Atividade recente", "quickActions": "Ações <PERSON>", "welcome": "Bem-vindo ao seu painel de controle"}, "errors": {"404": {"title": "Página não encontrada", "description": "A página que você está procurando não existe.", "backHome": "Voltar para o início"}, "500": {"title": "Erro interno do servidor", "description": "Algo deu errado no servidor. Tente novamente mais tarde.", "backHome": "Voltar para o início"}}, "features": {"title": "Recursos", "description": "Descubra todos os recursos incríveis que nossa plataforma oferece.", "list": {"ai": "Inteligência Artificial", "analytics": "Analytics", "security": "Segurança", "scalability": "Escalabilidade", "integration": "Integração"}}, "footer": {"description": "Plataforma moderna e escalável para suas necessidades de negócio.", "links": {"about": "Sobre", "privacy": "Privacidade", "terms": "Termos", "contact": "Contato"}, "copyright": "© 2024 Todos os direitos reservados."}, "header": {"navigation": {"home": "Início", "features": "Recursos", "pricing": "Preços", "about": "Sobre", "contact": "Contato"}, "actions": {"login": "Entrar", "signup": "Cadastrar"}}, "home": {"hero": {"title": "Plataforma moderna para suas necessidades de negócio", "subtitle": "Construa, escale e gerencie suas aplicações com facilidade usando nossa plataforma de última geração.", "cta": "<PERSON><PERSON><PERSON>ra", "secondaryCta": "<PERSON><PERSON> mais"}, "features": {"title": "Recursos principais", "subtitle": "Tudo que você precisa para construir aplicações incríveis", "list": {"ai": {"title": "IA Integrada", "description": "Aproveite o poder da inteligência artificial em suas aplicações."}, "analytics": {"title": "Analytics Avançado", "description": "Obtenha insights profundos sobre o comportamento dos usuários."}, "security": {"title": "Segurança de Nível Empresarial", "description": "Proteja seus dados com as melhores práticas de segurança."}, "scalability": {"title": "Escalabilidade Automática", "description": "Sua aplicação cresce automaticamente conforme a demanda."}}}, "pricing": {"title": "Preços simples e transparentes", "subtitle": "Escolha o plano que melhor se adapta às suas necessidades", "plans": {"starter": {"name": "Iniciante", "price": "<PERSON><PERSON><PERSON><PERSON>", "description": "Perfeito para começar", "features": ["Até 100 usuários", "5GB de armazenamento", "Suporte por email"]}, "pro": {"name": "Profissional", "price": "$29/mês", "description": "Para equipes em crescimento", "features": ["Até 1000 usuários", "50GB de armazenamento", "Suporte prioritário", "Analytics avançado"]}, "enterprise": {"name": "Empresarial", "price": "Sob consulta", "description": "Para grandes organizações", "features": ["Usuários ilimitados", "Armazenamento ilimitado", "Suporte 24/7", "Personalização completa"]}}}, "testimonials": {"title": "O que nossos clientes dizem", "subtitle": "Empresas confiam em nossa plataforma para crescer seus negócios", "list": [{"name": "<PERSON>", "company": "TechCorp", "text": "Esta plataforma transformou completamente nossa forma de trabalhar. Altamente recomendado!"}, {"name": "<PERSON>", "company": "InnovateLab", "text": "A escalabilidade e facilidade de uso são impressionantes. Exatamente o que precisávamos."}, {"name": "<PERSON>", "company": "DataFlow", "text": "Suporte excepcional e recursos poderosos. Não conseguimos imaginar usar outra plataforma."}]}}, "mail": {"welcome": {"subject": "Bem-vindo à nossa plataforma!", "title": "Bem-vindo!", "message": "Estamos muito felizes que você tenha se juntado a nós. Sua conta foi criada com sucesso.", "cta": "<PERSON><PERSON><PERSON>ra"}, "verification": {"subject": "Verifique seu email", "title": "Verificação de email", "message": "Clique no botão abaixo para verificar seu endereço de email.", "cta": "Verificar email"}, "resetPassword": {"subject": "Redefinir sua senha", "title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Clique no botão abaixo para redefinir sua senha.", "cta": "<PERSON><PERSON><PERSON><PERSON>"}, "invitation": {"subject": "Convite para organização", "title": "<PERSON><PERSON><PERSON>", "message": "Você foi convidado para se juntar a uma organização.", "cta": "Aceitar convite"}}, "newsletter": {"title": "Mantenha-se atualizado", "subtitle": "Receba as últimas notícias e atualizações diretamente em sua caixa de entrada.", "placeholder": "Seu email", "submit": "Inscrever-se", "success": "Inscrição realizada com sucesso!", "error": "Algo deu errado. Tente novamente."}, "onboarding": {"welcome": {"title": "Bem-vindo à sua nova conta!", "subtitle": "Vamos configurar tudo para você começar rapidamente.", "next": "Próximo", "skip": "<PERSON><PERSON>"}, "profile": {"title": "Complete seu perfil", "subtitle": "Adicione algumas informações sobre você para personalizar sua experiência.", "name": "Nome completo", "bio": "Biografia", "avatar": "Foto de perfil", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "organization": {"title": "Crie sua organização", "subtitle": "Organize seu trabalho em equipes e projetos.", "name": "Nome da organização", "description": "Descrição", "create": "Criar organização"}, "complete": {"title": "Tudo pronto!", "subtitle": "Sua conta está configurada e pronta para uso.", "start": "<PERSON><PERSON><PERSON> a usar"}}, "organizations": {"title": "Organizações", "description": "Gerencie suas organizações e equipes.", "create": "Criar organização", "list": {"empty": "Nenhuma organização encontrada", "loading": "Carregando organizações...", "search": "Pesquisar organizações..."}, "form": {"name": "Nome da organização", "description": "Descrição", "logo": "Logo", "website": "Website", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "members": {"title": "Me<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> membro", "invite": "<PERSON><PERSON><PERSON>", "remove": "Remover", "role": "Função", "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrador", "member": "Membro"}}, "settings": {"title": "Configurações", "general": "G<PERSON>", "billing": "Cobrança", "security": "Segurança", "integrations": "Integrações"}, "organizationSelect": {"createNewOrganization": "Criar nova organização", "organizations": "Organizações", "personalAccount": "<PERSON>ta pessoal"}}, "pricing": {"title": "Preços", "subtitle": "Escolha o plano que melhor se adapta às suas necessidades", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "save": "Economize {percentage}%", "plans": {"starter": {"name": "Iniciante", "price": "<PERSON><PERSON><PERSON><PERSON>", "period": "para sempre", "description": "Perfeito para começar", "features": ["Até 100 usuários", "5GB de armazenamento", "Suporte por email", "Recursos básicos"], "cta": "<PERSON><PERSON><PERSON>"}, "pro": {"name": "Profissional", "price": "$29", "period": "por mês", "description": "Para equipes em crescimento", "features": ["Até 1000 usuários", "50GB de armazenamento", "Suporte prioritário", "Analytics avançado", "Integrações personalizadas"], "cta": "<PERSON><PERSON><PERSON> teste gratuito"}, "enterprise": {"name": "Empresarial", "price": "Sob consulta", "period": "personalizado", "description": "Para grandes organizações", "features": ["Usuários ilimitados", "Armazenamento ilimitado", "Suporte 24/7", "Personalização completa", "SLA garantido"], "cta": "Falar com vendas"}}, "faq": {"title": "<PERSON><PERSON><PERSON> frequentes", "questions": [{"question": "Posso mudar de plano a qualquer momento?", "answer": "<PERSON><PERSON>, você pode fazer upgrade ou downgrade do seu plano a qualquer momento."}, {"question": "Há um período de teste gratuito?", "answer": "<PERSON><PERSON>, oferecemos um período de teste gratuito de 14 dias para todos os planos pagos."}, {"question": "Que tipos de suporte estão disponíveis?", "answer": "Oferecemos suporte por email, chat e telefone dependendo do seu plano."}]}}, "profile": {"title": "Perfil", "description": "Gerencie suas informações pessoais e configurações de conta.", "personal": {"title": "Informações pessoais", "name": "Nome completo", "email": "Email", "bio": "Biografia", "avatar": "Foto de perfil", "save": "<PERSON><PERSON>"}, "security": {"title": "Segurança", "password": "<PERSON><PERSON><PERSON><PERSON>", "twoFactor": "Autenticação de dois fatores", "sessions": "Sessõ<PERSON> at<PERSON>", "apiKeys": "<PERSON><PERSON> da <PERSON>"}, "preferences": {"title": "Preferências", "language": "Idioma", "timezone": "<PERSON><PERSON>", "notifications": "Notificações", "theme": "<PERSON><PERSON>"}}, "settings": {"title": "Configurações", "description": "Personalize sua experiência e gerencie suas preferências.", "general": {"title": "G<PERSON>", "language": "Idioma", "timezone": "<PERSON><PERSON>", "dateFormat": "Formato de data", "timeFormat": "Formato de hora"}, "notifications": {"title": "Notificações", "email": "Notificações por email", "push": "Notificações push", "sms": "Notificações SMS", "frequency": "Frequência"}, "privacy": {"title": "Privacidade", "dataSharing": "Compartilhamento de dados", "analytics": "Analytics", "marketing": "Marketing", "export": "Exportar dados"}, "billing": {"title": "Cobrança", "plan": "Plano atual", "paymentMethod": "Método de pagamento", "invoices": "<PERSON><PERSON><PERSON>", "usage": "<PERSON><PERSON>"}}, "start": {"title": "<PERSON><PERSON><PERSON>", "description": "Configure sua conta e comece a usar nossa plataforma.", "steps": [{"title": "Verificar email", "description": "Confirme seu endereço de email para ativar sua conta."}, {"title": "Complete seu perfil", "description": "Adicione informações básicas sobre você."}, {"title": "Criar organização", "description": "Organize seu trabalho em equipes e projetos."}, {"title": "Primeiro projeto", "description": "Crie seu primeiro projeto para começar a trabalhar."}], "getStarted": "<PERSON><PERSON><PERSON>ra", "skip": "Pular para depois"}, "validation": {"required": "Este campo é obrigatório", "email": "Digite um email válido", "minLength": "<PERSON><PERSON><PERSON> de {min} caracteres", "maxLength": "<PERSON><PERSON><PERSON><PERSON> de {max} caracteres", "password": "A senha deve ter pelo menos 8 caracteres", "passwordMatch": "As senhas devem coincidir", "url": "Digite uma URL válida", "phone": "Digite um telefone válido"}, "zod": {"errors": {"invalid_type": "E<PERSON><PERSON> {expected}, recebido {received}", "invalid_literal": "Valor literal inválido, esperado {expected}", "unrecognized_keys": "Chave(s) não reconhecida(s) no objeto: {keys}", "invalid_union": "Entrada inválida", "invalid_union_discriminator": "Valor discriminador inválido. Esperado {options}", "invalid_enum_value": "Valor enum inválido. Esperado {options}, recebido '{received}'", "invalid_arguments": "Argumentos de função inválidos", "invalid_return_type": "Tipo de retorno de função inválido", "invalid_date": "Data inválida", "invalid_string": "Validação inválida", "too_small": "<PERSON><PERSON> peque<PERSON>", "too_big": "<PERSON>ito grande", "invalid_intersection_types": "Resultados de interseção não puderam ser mesclados", "not_multiple_of": "Número deve ser múltiplo de {multipleOf}", "not_finite": "Número deve ser finito", "custom": "Entrada inválida"}, "validations": {"email": "<PERSON><PERSON>", "url": "URL inválida", "emoji": "<PERSON><PERSON><PERSON>", "uuid": "UUID inválido", "cuid": "CUID inválido", "cuid2": "CUID2 inválido", "ulid": "ULID inválido", "regex": "<PERSON>v<PERSON><PERSON><PERSON>", "includes": "<PERSON><PERSON> incluir \"{includes}\"", "startsWith": "<PERSON><PERSON> começar com \"{startsWith}\"", "endsWith": "Deve terminar com \"{endsWith}\"", "min": "<PERSON><PERSON> conter pelo menos {min} caractere(s)", "max": "Deve conter no máximo {max} caractere(s)", "length": "Deve conter exatamente {length} caractere(s)", "min_1": "<PERSON><PERSON> conter pelo menos {min} caractere", "max_1": "Deve conter no máximo {max} caractere", "length_1": "Deve conter exatamente {length} caractere", "trim": "<PERSON><PERSON> ser aparado", "toLowerCase": "Deve estar em minúsculas", "toUpperCase": "Deve estar em maiúsculas"}}, "currency": {"brl": {"name": "Real Brasileiro", "symbol": "R$", "code": "BRL"}, "usd": {"name": "<PERSON><PERSON><PERSON>", "symbol": "US$", "code": "USD"}, "eur": {"name": "Euro", "symbol": "€", "code": "EUR"}, "gbp": {"name": "Libra Esterlina", "symbol": "£", "code": "GBP"}, "validation": {"unsupported": "Moeda não suportada: {currency}", "invalid": "Código de moeda inválido", "required": "Moeda é obrigatória"}, "conversion": {"failed": "Falha na conversão de moeda", "unavailable": "Conversão não disponível para {from} -> {to}", "loading": "Convertendo moeda...", "updated": "Taxa de câmbio atualizada"}, "format": {"compact": {"thousand": "mil", "million": "mi", "billion": "bi"}}}}