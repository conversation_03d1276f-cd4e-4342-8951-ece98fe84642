import { config } from "@repo/config";

interface ChatwootConfig {
  baseUrl: string;
  accountId: string;
  apiAccessToken: string;
}

interface ConversationFilters {
  page: number;
  limit: number;
  status?: string;
  channel?: string;
  accountId: string;
}

interface CreateConversationData {
  contactId: string;
  message?: string;
  channel: "whatsapp" | "sms" | "email";
  source?: string;
  accountId: string;
  agentId: string;
}

interface UpdateConversationData {
  status?: "open" | "pending" | "resolved" | "closed";
  priority?: "low" | "medium" | "high" | "urgent";
  assigneeId?: string;
  tags?: string[];
}

interface ChatwootConversation {
  id: string;
  inbox_id: number;
  conversation_id: number;
  status: string;
  priority: string;
  assignee_id?: number;
  tags: string[];
  created_at: string;
  updated_at: string;
  contact: {
    id: number;
    name: string;
    email?: string;
    phone_number?: string;
    avatar_url?: string;
  };
  messages: Array<{
    id: number;
    content: string;
    message_type: number;
    created_at: string;
    sender: {
      id: number;
      name: string;
      type: string;
    };
  }>;
}

export class ChatwootService {
  private config: ChatwootConfig | null = null;
  private isEnabled: boolean = false;

  constructor() {
    try {
      const baseUrl = config.chatwoot?.baseUrl || process.env.CHATWOOT_BASE_URL || "";
      const accountId = config.chatwoot?.accountId || process.env.CHATWOOT_ACCOUNT_ID || "";
      const apiAccessToken = config.chatwoot?.apiAccessToken || process.env.CHATWOOT_API_ACCESS_TOKEN || "";

      if (baseUrl && accountId && apiAccessToken) {
        this.config = {
          baseUrl,
          accountId,
          apiAccessToken,
        };
        this.isEnabled = true;
      } else {
        console.warn("Chatwoot não configurado - funcionalidades de chat desabilitadas");
        this.isEnabled = false;
      }
    } catch (error) {
      console.warn("Erro ao configurar Chatwoot:", error);
      this.isEnabled = false;
    }
  }

  private checkEnabled(): void {
    if (!this.isEnabled || !this.config) {
      throw new Error("Chatwoot não está configurado ou habilitado");
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    this.checkEnabled();

    const url = `${this.config!.baseUrl}/api/v1/accounts/${this.config!.accountId}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        "api_access_token": this.config!.apiAccessToken,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Chatwoot API error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  async listConversations(filters: ConversationFilters): Promise<ChatwootConversation[]> {
    try {
      const queryParams = new URLSearchParams({
        page: filters.page.toString(),
        per_page: filters.limit.toString(),
      });

      if (filters.status) {
        queryParams.append("status", filters.status);
      }

      if (filters.channel) {
        queryParams.append("inbox_id", this.getInboxIdByChannel(filters.channel));
      }

      return await this.makeRequest<ChatwootConversation[]>(`/conversations?${queryParams}`);
    } catch (error) {
      console.warn("Erro ao listar conversas do Chatwoot:", error);
      return [];
    }
  }

  async getConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}`);
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao buscar conversa do Chatwoot:", error);
      return null;
    }
  }

  async createConversation(data: CreateConversationData): Promise<ChatwootConversation | null> {
    try {
      const payload = {
        contact_id: data.contactId,
        inbox_id: this.getInboxIdByChannel(data.channel),
        source: data.source || "api",
        message: data.message,
        assignee_id: data.agentId,
      };

      return await this.makeRequest<ChatwootConversation>("/conversations", {
        method: "POST",
        body: JSON.stringify(payload),
      });
    } catch (error) {
      console.warn("Erro ao criar conversa no Chatwoot:", error);
      return null;
    }
  }

  async updateConversation(
    id: string,
    data: UpdateConversationData,
    accountId: string
  ): Promise<ChatwootConversation | null> {
    try {
      const payload: any = {};

      if (data.status) {
        payload.status = data.status;
      }

      if (data.priority) {
        payload.priority = data.priority;
      }

      if (data.assigneeId) {
        payload.assignee_id = data.assigneeId;
      }

      if (data.tags) {
        payload.tags = data.tags;
      }

      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}`, {
        method: "PATCH",
        body: JSON.stringify(payload),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao atualizar conversa no Chatwoot:", error);
      return null;
    }
  }

  async closeConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_status`, {
        method: "POST",
        body: JSON.stringify({ status: "closed" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao fechar conversa no Chatwoot:", error);
      return null;
    }
  }

  async reopenConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_status`, {
        method: "POST",
        body: JSON.stringify({ status: "open" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao reabrir conversa no Chatwoot:", error);
      return null;
    }
  }

  async assignConversation(
    id: string,
    assigneeId: string,
    accountId: string
  ): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/assign`, {
        method: "POST",
        body: JSON.stringify({ assignee_id: assigneeId }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao atribuir conversa no Chatwoot:", error);
      return null;
    }
  }

  async starConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_priority`, {
        method: "POST",
        body: JSON.stringify({ priority: "urgent" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao destacar conversa no Chatwoot:", error);
      return null;
    }
  }

  async unstarConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_priority`, {
        method: "POST",
        body: JSON.stringify({ priority: "low" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      console.warn("Erro ao remover destaque da conversa no Chatwoot:", error);
      return null;
    }
  }

  async sendMessage(
    conversationId: string,
    message: string,
    messageType: "incoming" | "outgoing" = "outgoing"
  ): Promise<any> {
    try {
      const payload = {
        content: message,
        message_type: messageType === "outgoing" ? 0 : 1, // 0 = outgoing, 1 = incoming
      };

      return await this.makeRequest(`/conversations/${conversationId}/messages`, {
        method: "POST",
        body: JSON.stringify(payload),
      });
    } catch (error) {
      console.warn("Erro ao enviar mensagem no Chatwoot:", error);
      return null;
    }
  }

  private getInboxIdByChannel(channel: string): string {
    // Mapeamento de canais para inbox IDs do Chatwoot
    const channelMap: Record<string, string> = {
      whatsapp: process.env.CHATWOOT_WHATSAPP_INBOX_ID || "1",
      sms: process.env.CHATWOOT_SMS_INBOX_ID || "2",
      email: process.env.CHATWOOT_EMAIL_INBOX_ID || "3",
    };

    return channelMap[channel] || "1";
  }

  // Método para sincronizar dados do Chatwoot com o banco local
  async syncConversations(accountId: string): Promise<void> {
    try {
      if (!this.isEnabled) {
        console.log("Chatwoot não configurado - pulando sincronização");
        return;
      }

      const conversations = await this.listConversations({
        page: 1,
        limit: 100,
        accountId,
      });

      // Aqui você implementaria a lógica para sincronizar com seu banco de dados
      // Por exemplo, usando o Drizzle para inserir/atualizar registros
      console.log(`Sincronizando ${conversations.length} conversas`);

      // TODO: Implementar sincronização com banco local

    } catch (error) {
      console.error("Erro ao sincronizar conversas:", error);
      // Não re-throw para não falhar o build
    }
  }

  // Método para verificar se o Chatwoot está habilitado
  isChatwootEnabled(): boolean {
    return this.isEnabled;
  }
}

// Instância singleton do serviço
export const chatwootService = new ChatwootService();
