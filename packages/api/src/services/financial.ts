import { db } from "@repo/database";
import {
  TransactionType,
  TransactionStatus,
  LedgerEntryType,
  CashFlowType,
  CashFlowCategory,
  ReportType,
  ReportPeriod
} from "@repo/database/prisma/generated/client";
import type { Decimal } from "@prisma/client/runtime/library";

export interface CreateTransactionInput {
  organizationId: string;
  type: TransactionType;
  amount: number;
  currency?: string;
  description?: string;
  fromUserId?: string;
  toUserId?: string;
  orderId?: string;
  paymentMethod?: string;
  externalId?: string;
  gatewayId?: string;
  metadata?: Record<string, any>;
}

export interface CreateLedgerEntryInput {
  organizationId: string;
  userId: string;
  type: LedgerEntryType;
  amount: number;
  description?: string;
  orderId?: string;
  transactionId?: string;
  referenceId?: string;
  availableAt?: Date;
  metadata?: Record<string, any>;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalCommissions: number;
  totalFees: number;
  availableBalance: number;
  pendingBalance: number;
  totalTransactions: number;
  period: {
    startDate: Date;
    endDate: Date;
  };
}

export class FinancialService {
  static async createTransaction(input: CreateTransactionInput) {
    const transaction = await db.transaction.create({
      data: {
        organizationId: input.organizationId,
        type: input.type,
        amount: input.amount,
        currency: input.currency || "BRL",
        description: input.description,
        fromUserId: input.fromUserId,
        toUserId: input.toUserId,
        orderId: input.orderId,
        paymentMethod: input.paymentMethod,
        externalId: input.externalId,
        gatewayId: input.gatewayId,
        metadata: input.metadata,
        status: TransactionStatus.PENDING,
      },
      include: {
        fromUser: true,
        toUser: true,
        order: true,
        organization: true,
      },
    });

    await this.createCashFlowEntry({
      organizationId: input.organizationId,
      transactionId: transaction.id,
      type: input.type === TransactionType.CREDIT ? CashFlowType.INFLOW : CashFlowType.OUTFLOW,
      category: this.getCashFlowCategory(input.type),
      amount: input.amount,
      description: input.description,
      userId: input.fromUserId || input.toUserId,
      orderId: input.orderId,
      date: new Date(),
    });

    return transaction;
  }

  static async createLedgerEntry(input: CreateLedgerEntryInput) {
    return await db.ledgerEntry.create({
      data: {
        organizationId: input.organizationId,
        userId: input.userId,
        type: input.type,
        amount: input.amount,
        description: input.description,
        orderId: input.orderId,
        transactionId: input.transactionId,
        referenceId: input.referenceId,
        availableAt: input.availableAt,
        metadata: input.metadata,
      },
      include: {
        user: true,
        order: true,
        transaction: true,
        organization: true,
      },
    });
  }

  static async processOrderPayment(orderId: string) {
    const order = await db.order.findUnique({
      where: { id: orderId },
      include: {
        product: {
          include: {
            creator: true,
          },
        },
        buyer: true,
        affiliate: true,
      },
    });

    if (!order) {
      throw new Error("Order not found");
    }

    const grossAmount = Number(order.total);
    const platformFeePercentage = 0.0699;
    const platformFeeFixed = 1.49;
    const affiliateCommissionPercentage = 0.4;

    const platformFee = grossAmount * platformFeePercentage + platformFeeFixed;
    const affiliateCommission = order.affiliateId ? grossAmount * affiliateCommissionPercentage : 0;
    const creatorNetAmount = grossAmount - platformFee - affiliateCommission;

    const settlementDate = new Date();
    settlementDate.setDate(settlementDate.getDate() + 30);

    const transaction = await this.createTransaction({
      organizationId: order.organizationId,
      type: TransactionType.CREDIT,
      amount: grossAmount,
      description: `Payment for order ${order.id}`,
      toUserId: order.product.creatorId,
      orderId: order.id,
      paymentMethod: order.paymentMethod || "unknown",
    });

    const ledgerEntries = [];

    ledgerEntries.push(
      await this.createLedgerEntry({
        organizationId: order.organizationId,
        userId: order.product.creatorId,
        type: LedgerEntryType.SALE_GROSS,
        amount: grossAmount,
        description: `Gross sale for order ${order.id}`,
        orderId: order.id,
        transactionId: transaction.id,
        availableAt: settlementDate,
      })
    );

    ledgerEntries.push(
      await this.createLedgerEntry({
        organizationId: order.organizationId,
        userId: order.product.creatorId,
        type: LedgerEntryType.PLATFORM_FEE,
        amount: -platformFee,
        description: `Platform fee for order ${order.id}`,
        orderId: order.id,
        transactionId: transaction.id,
        availableAt: settlementDate,
      })
    );

    if (order.affiliateId && affiliateCommission > 0) {
      ledgerEntries.push(
        await this.createLedgerEntry({
          organizationId: order.organizationId,
          userId: order.product.creatorId,
          type: LedgerEntryType.AFFILIATE_COMMISSION_DEBIT,
          amount: -affiliateCommission,
          description: `Affiliate commission debit for order ${order.id}`,
          orderId: order.id,
          transactionId: transaction.id,
          availableAt: settlementDate,
        })
      );

      ledgerEntries.push(
        await this.createLedgerEntry({
          organizationId: order.organizationId,
          userId: order.affiliateId,
          type: LedgerEntryType.AFFILIATE_COMMISSION_CREDIT,
          amount: affiliateCommission,
          description: `Affiliate commission for order ${order.id}`,
          orderId: order.id,
          transactionId: transaction.id,
          availableAt: settlementDate,
        })
      );
    }

    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: TransactionStatus.COMPLETED,
        processedAt: new Date(),
        settledAt: settlementDate,
      },
    });

    return {
      transaction,
      ledgerEntries,
      breakdown: {
        grossAmount,
        platformFee,
        affiliateCommission,
        creatorNetAmount,
        settlementDate,
      },
    };
  }

  static async getFinancialSummary(
    organizationId: string,
    startDate: Date,
    endDate: Date,
    userId?: string
  ): Promise<FinancialSummary> {
    const whereClause = {
      organizationId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      ...(userId && { userId }),
    };

    const [
      revenueEntries,
      commissionEntries,
      feeEntries,
      availableEntries,
      pendingEntries,
      totalTransactions,
    ] = await Promise.all([
      db.ledgerEntry.aggregate({
        where: {
          ...whereClause,
          type: LedgerEntryType.SALE_GROSS,
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          ...whereClause,
          type: {
            in: [
              LedgerEntryType.AFFILIATE_COMMISSION_CREDIT,
              LedgerEntryType.COPRODUCER_COMMISSION_CREDIT,
            ],
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          ...whereClause,
          type: LedgerEntryType.PLATFORM_FEE,
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          ...whereClause,
          availableAt: {
            lte: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          ...whereClause,
          availableAt: {
            gt: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.transaction.count({
        where: {
          organizationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(userId && {
            OR: [{ fromUserId: userId }, { toUserId: userId }],
          }),
        },
      }),
    ]);

    return {
      totalRevenue: Number(revenueEntries._sum.amount || 0),
      totalCommissions: Number(commissionEntries._sum.amount || 0),
      totalFees: Math.abs(Number(feeEntries._sum.amount || 0)),
      availableBalance: Number(availableEntries._sum.amount || 0),
      pendingBalance: Number(pendingEntries._sum.amount || 0),
      totalTransactions,
      period: {
        startDate,
        endDate,
      },
    };
  }

  static async generateFinancialReport(
    organizationId: string,
    type: ReportType,
    period: ReportPeriod,
    startDate: Date,
    endDate: Date
  ) {
    const summary = await this.getFinancialSummary(organizationId, startDate, endDate);

    const reportData = {
      summary,
      details: await this.getReportDetails(organizationId, type, startDate, endDate),
    };

    const report = await db.financialReport.create({
      data: {
        organizationId,
        type,
        period,
        startDate,
        endDate,
        data: reportData,
        summary: summary,
      },
    });

    return report;
  }

  private static async getReportDetails(
    organizationId: string,
    type: ReportType,
    startDate: Date,
    endDate: Date
  ) {
    switch (type) {
      case ReportType.REVENUE:
        return await this.getRevenueDetails(organizationId, startDate, endDate);
      case ReportType.COMMISSION:
        return await this.getCommissionDetails(organizationId, startDate, endDate);
      case ReportType.CASH_FLOW:
        return await this.getCashFlowDetails(organizationId, startDate, endDate);
      default:
        return {};
    }
  }

  private static async getRevenueDetails(organizationId: string, startDate: Date, endDate: Date) {
    return await db.ledgerEntry.findMany({
      where: {
        organizationId,
        type: LedgerEntryType.SALE_GROSS,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: true,
        order: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  private static async getCommissionDetails(organizationId: string, startDate: Date, endDate: Date) {
    return await db.ledgerEntry.findMany({
      where: {
        organizationId,
        type: {
          in: [
            LedgerEntryType.AFFILIATE_COMMISSION_CREDIT,
            LedgerEntryType.COPRODUCER_COMMISSION_CREDIT,
          ],
        },
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: true,
        order: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  private static async getCashFlowDetails(organizationId: string, startDate: Date, endDate: Date) {
    return await db.cashFlowEntry.findMany({
      where: {
        organizationId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: true,
        order: true,
        transaction: true,
      },
      orderBy: {
        date: "desc",
      },
    });
  }

  private static async createCashFlowEntry(input: {
    organizationId: string;
    transactionId?: string;
    type: CashFlowType;
    category: CashFlowCategory;
    amount: number;
    description?: string;
    userId?: string;
    orderId?: string;
    date: Date;
  }) {
    return await db.cashFlowEntry.create({
      data: input,
    });
  }

  private static getCashFlowCategory(transactionType: TransactionType): CashFlowCategory {
    switch (transactionType) {
      case TransactionType.CREDIT:
        return CashFlowCategory.SALES;
      case TransactionType.COMMISSION:
        return CashFlowCategory.COMMISSIONS;
      case TransactionType.FEE:
        return CashFlowCategory.FEES;
      case TransactionType.WITHDRAWAL:
        return CashFlowCategory.WITHDRAWALS;
      case TransactionType.REFUND:
        return CashFlowCategory.REFUNDS;
      case TransactionType.TRANSFER:
        return CashFlowCategory.TRANSFERS;
      default:
        return CashFlowCategory.OTHER;
    }
  }

  static async getUserBalance(organizationId: string, userId: string) {
    const [available, pending, total] = await Promise.all([
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
          userId,
          availableAt: {
            lte: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
          userId,
          availableAt: {
            gt: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
          userId,
        },
        _sum: { amount: true },
      }),
    ]);

    return {
      availableBalance: Number(available._sum.amount || 0),
      pendingBalance: Number(pending._sum.amount || 0),
      totalBalance: Number(total._sum.amount || 0),
    };
  }

  static async createBalanceSnapshot(organizationId: string, userId?: string) {
    const balance = userId
      ? await this.getUserBalance(organizationId, userId)
      : await this.getOrganizationBalance(organizationId);

    return await db.balanceSnapshot.create({
      data: {
        organizationId,
        userId,
        totalBalance: balance.totalBalance,
        availableBalance: balance.availableBalance,
        pendingBalance: balance.pendingBalance,
        reservedBalance: 0,
        snapshotDate: new Date(),
      },
    });
  }

  private static async getOrganizationBalance(organizationId: string) {
    const [available, pending, total] = await Promise.all([
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
          availableAt: {
            lte: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
          availableAt: {
            gt: new Date(),
          },
        },
        _sum: { amount: true },
      }),
      db.ledgerEntry.aggregate({
        where: {
          organizationId,
        },
        _sum: { amount: true },
      }),
    ]);

    return {
      availableBalance: Number(available._sum.amount || 0),
      pendingBalance: Number(pending._sum.amount || 0),
      totalBalance: Number(total._sum.amount || 0),
    };
  }
}
