import { getBaseUrl } from "@repo/utils";
import { cors } from "hono/cors";

export const corsMiddleware = cors({
	origin: process.env.NODE_ENV === "development" ? ["http://localhost:3000", "http://localhost:3001"] : [getBaseUrl()],
	allowHeaders: ["Content-Type", "Authorization", "Cookie"],
	allowMethods: ["POST", "GET", "OPTIONS", "PUT", "DELETE"],
	exposeHeaders: ["Content-Length"],
	maxAge: 600,
	credentials: true,
});
