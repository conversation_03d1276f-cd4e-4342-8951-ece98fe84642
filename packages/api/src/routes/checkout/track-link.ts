import { z } from 'zod';
import { db } from '@repo/database';

// Track link action schema (unified for clicks and conversions)
const trackLinkSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  action: z.enum(['click', 'conversion']),
  // Click data
  userAgent: z.string().optional(),
  ipAddress: z.string().optional(),
  referrer: z.string().optional(),
  utmParams: z.record(z.string()).optional(),
  // Conversion data
  orderId: z.string().optional(),
  amount: z.number().positive().optional(),
  paymentMethod: z.string().optional(),
}).refine((data) => {
  if (data.action === 'conversion') {
    return data.orderId && data.amount && data.paymentMethod;
  }
  return true;
}, {
  message: 'Order ID, amount, and payment method are required for conversions',
});

// Unified track link handler (handles both clicks and conversions)
export async function trackLinkHandler(c: any) {
  try {
    const validatedData = c.req.valid("json");

    // Find the checkout link
    const checkoutLink = await db.checkoutLink.findUnique({
      where: { id: validatedData.linkId },
    });

    if (!checkoutLink) {
      return c.json({ error: 'Checkout link not found' }, 404);
    }

    // Check if link is expired
    if (checkoutLink.expiresAt && new Date() > checkoutLink.expiresAt) {
      return c.json({ error: 'Checkout link has expired' }, 400);
    }

    if (validatedData.action === 'click') {
      // Handle click tracking
      await db.checkoutLink.update({
        where: { id: validatedData.linkId },
        data: {
          clickCount: { increment: 1 },
          lastClickedAt: new Date(),
        },
      });

      // Record click event (if table exists)
      try {
        await db.checkoutLinkClick.create({
          data: {
            linkId: validatedData.linkId,
            userAgent: validatedData.userAgent,
            ipAddress: validatedData.ipAddress,
            referrer: validatedData.referrer,
            utmParams: validatedData.utmParams ? JSON.stringify(validatedData.utmParams) : null,
            timestamp: new Date(),
          },
        });
      } catch (error) {
        // Table might not exist, just log the click
        console.log('Click tracked (no history table):', validatedData.linkId);
      }

      return c.json({ message: 'Click tracked successfully' });
    } else if (validatedData.action === 'conversion') {
      // Handle conversion tracking
      await db.checkoutLink.update({
        where: { id: validatedData.linkId },
        data: {
          conversionCount: { increment: 1 },
          totalRevenue: { increment: validatedData.amount! },
          lastConversionAt: new Date(),
        },
      });

      // Record conversion event (if table exists)
      try {
        await db.checkoutLinkConversion.create({
          data: {
            linkId: validatedData.linkId,
            orderId: validatedData.orderId!,
            amount: validatedData.amount!,
            paymentMethod: validatedData.paymentMethod!,
            timestamp: new Date(),
          },
        });
      } catch (error) {
        // Table might not exist, just log the conversion
        console.log('Conversion tracked (no history table):', validatedData.linkId);
      }

      return c.json({ message: 'Conversion tracked successfully' });
    }

  } catch (error) {
    console.error('Error tracking link:', error);

    if (error instanceof z.ZodError) {
      return c.json({
        error: 'Invalid request data: ' + error.errors.map(e => e.message).join(', '),
      }, 400);
    }

    return c.json({ error: 'Internal server error' }, 500);
  }
}




