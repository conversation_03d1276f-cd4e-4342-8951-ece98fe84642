import { z } from 'zod';
import { db } from '@repo/database';
import { nanoid } from 'nanoid';

// Request schema
const generateLinkSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  offerId: z.string().optional(),
  utmParams: z.record(z.string()).optional(),
  expiresIn: z.number().positive().optional().default(1440), // 24 hours default
  customParams: z.record(z.string()).optional(),
});

// Bulk links schema
const generateBulkLinksSchema = z.object({
  links: z.array(generateLinkSchema).min(1).max(50),
});

// Utility function to generate checkout link
function createCheckoutLink(productId: string, options: {
  offerId?: string;
  utmParams?: Record<string, string>;
  expiresIn?: number;
  baseUrl?: string;
}) {
  const linkId = nanoid(12);
  const baseUrl = options.baseUrl || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

  let url = `${baseUrl}/checkout/${productId}?ref=${linkId}`;

  if (options.offerId) {
    url += `&offer=${options.offerId}`;
  }

  if (options.utmParams) {
    Object.entries(options.utmParams).forEach(([key, value]) => {
      url += `&${key}=${encodeURIComponent(value)}`;
    });
  }

  const expiresAt = options.expiresIn
    ? new Date(Date.now() + options.expiresIn * 60 * 1000)
    : null;

  return {
    id: linkId,
    productId,
    offerId: options.offerId,
    url,
    utmParams: options.utmParams,
    expiresAt,
    createdAt: new Date(),
  };
}

// Route handler
export async function generateCheckoutLinkHandler(c: any) {
  try {
    const user = c.get("user");
    const validatedData = c.req.valid("json");

    console.log('Generate checkout link request:', {
      userId: user?.id,
      productId: validatedData.productId,
      headers: Object.fromEntries(c.req.raw.headers.entries())
    });

    if (!user) {
      console.error('No user found in context');
      return c.json({ error: 'User not found' }, 401);
    }

    // Verify product exists and user has access
    const product = await db.product.findUnique({
      where: { id: validatedData.productId },
      include: {
        offers: true,
        creator: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!product) {
      console.error('Product not found:', validatedData.productId);
      return c.json({ error: 'Product not found' }, 404);
    }

    console.log('Product found:', {
      id: product.id,
      name: product.name,
      organizationId: product.organizationId
    });

    // Verify user has access to the product's organization
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      console.error('Access denied for user:', {
        userId: user.id,
        organizationId: product.organizationId
      });
      return c.json({ error: "Access denied" }, 403);
    }

    console.log('User access verified');

    // Verify offer exists if provided
    if (validatedData.offerId) {
      const offerExists = product.offers.some(offer => offer.id === validatedData.offerId);
      if (!offerExists) {
        console.error('Offer not found:', validatedData.offerId);
        return c.json({ error: 'Offer not found for this product' }, 400);
      }
    }

    // Generate checkout link
    const checkoutLink = createCheckoutLink(validatedData.productId, {
      offerId: validatedData.offerId,
      utmParams: validatedData.utmParams,
      expiresIn: validatedData.expiresIn,
      baseUrl: process.env.NEXT_PUBLIC_APP_URL,
    });

    console.log('Checkout link generated:', checkoutLink);

    // Store link in database for tracking
    const savedLink = await db.checkoutLink.create({
      data: {
        id: checkoutLink.id,
        productId: checkoutLink.productId,
        offerId: checkoutLink.offerId,
        url: checkoutLink.url,
        expiresAt: checkoutLink.expiresAt,
        utmParams: checkoutLink.utmParams ? JSON.stringify(checkoutLink.utmParams) : null,
        customParams: validatedData.customParams ? JSON.stringify(validatedData.customParams) : null,
        clickCount: 0,
        conversionCount: 0,
        totalRevenue: 0,
      },
    });

    console.log('Checkout link saved to database:', savedLink);

    return c.json({
      checkoutLink: {
        id: savedLink.id,
        url: savedLink.url,
        productId: savedLink.productId,
        offerId: savedLink.offerId,
        expiresAt: savedLink.expiresAt?.toISOString(),
        createdAt: savedLink.createdAt.toISOString(),
      },
    });

  } catch (error) {
    console.error('Error generating checkout link:', error);

    if (error instanceof z.ZodError) {
      return c.json({
        error: 'Invalid request data: ' + error.errors.map(e => e.message).join(', '),
      }, 400);
    }

    return c.json({ error: 'Internal server error' }, 500);
  }
}

export async function generateBulkLinksHandler(c: any) {
  try {
    const user = c.get("user");
    const validatedData = c.req.valid("json");
    const { links } = validatedData;

    const generated = [];
    const failed = [];

    for (const linkData of links) {
      try {
        const validatedLinkData = generateLinkSchema.parse(linkData);

        // Verify product exists and user has access
        const product = await db.product.findUnique({
          where: { id: validatedLinkData.productId },
          include: { offers: true },
        });

        if (!product) {
          failed.push({
            productId: validatedLinkData.productId,
            error: 'Product not found',
          });
          continue;
        }

        // Verify user has access to the product's organization
        const member = await db.member.findFirst({
          where: {
            organizationId: product.organizationId,
            userId: user.id,
          },
        });

        if (!member) {
          failed.push({
            productId: validatedLinkData.productId,
            error: 'Access denied',
          });
          continue;
        }

        // Generate and save link
        const checkoutLink = createCheckoutLink(validatedLinkData.productId, {
          offerId: validatedLinkData.offerId,
          utmParams: validatedLinkData.utmParams,
          expiresIn: validatedLinkData.expiresIn,
        });

        const savedLink = await db.checkoutLink.create({
          data: {
            id: checkoutLink.id,
            productId: checkoutLink.productId,
            offerId: checkoutLink.offerId,
            url: checkoutLink.url,
            expiresAt: checkoutLink.expiresAt,
            utmParams: checkoutLink.utmParams ? JSON.stringify(checkoutLink.utmParams) : null,
            clickCount: 0,
            conversionCount: 0,
            totalRevenue: 0,
          },
        });

        generated.push({
          id: savedLink.id,
          url: savedLink.url,
          productId: savedLink.productId,
          offerId: savedLink.offerId,
          expiresAt: savedLink.expiresAt?.toISOString(),
          createdAt: savedLink.createdAt.toISOString(),
        });

      } catch (error) {
        failed.push({
          productId: linkData.productId || 'unknown',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return c.json({
      checkoutLinks: generated,
      failed,
      totalGenerated: generated.length,
      totalFailed: failed.length,
    });

  } catch (error) {
    console.error('Error generating bulk checkout links:', error);

    if (error instanceof z.ZodError) {
      return c.json({
        error: 'Invalid request data: ' + error.errors.map(e => e.message).join(', '),
      }, 400);
    }

    return c.json({ error: 'Internal server error' }, 500);
  }
}
