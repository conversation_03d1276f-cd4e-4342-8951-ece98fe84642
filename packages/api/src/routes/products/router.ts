import { <PERSON>o } from "hono";
import { zValida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";

const productsRouter = new Hono();

// Use the standard auth middleware
productsRouter.use("*", authMiddleware);

// Schema de validação para produto
const createProductSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  slug: z.string().min(1, "Slug é obrigatório"),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  priceCents: z.number().min(0, "Preço deve ser maior ou igual a 0"),
  comparePriceCents: z.number().min(0).optional(),
  currency: z.string().default("BRL"),
  		type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
  visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
  gallery: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  duration: z.number().min(0).optional(),
  level: z.string().optional(),
  language: z.string().default("pt-BR"),
  certificate: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  settings: z.record(z.any()).default({}),
});

const updateProductSchema = createProductSchema.partial();

// GET /products - Listar produtos da organização
productsRouter.get("/", async (c) => {
  try {
    const user = c.get("user");
    const { searchParams } = new URL(c.req.url);

    const organizationId = searchParams.get("organizationId");
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const categoryId = searchParams.get("categoryId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    if (!organizationId) {
      return c.json({ error: "Organization ID is required" }, 400);
    }

    // Verificar se o usuário é membro da organização
    const member = await db.member.findFirst({
      where: {
        organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    const where: any = { organizationId };

    if (status) where.status = status;
    if (type) where.type = type;
    if (categoryId) where.categoryId = categoryId;

    const [products, total] = await Promise.all([
      db.product.findMany({
        where,
        include: {
          category: true,
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              orders: true,
              enrollments: true,
              reviews: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.product.count({ where }),
    ]);

    return c.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// GET /products/:id - Buscar produto específico
productsRouter.get("/:id", async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");

    const product = await db.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        modules: {
          include: {
            lessons: true,
          },
          orderBy: { order: "asc" },
        },
        offers: true,
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            orders: true,
            enrollments: true,
            reviews: true,
          },
        },
      },
    });

    if (!product) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    return c.json({ product });
  } catch (error) {
    console.error("Error fetching product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// POST /products - Criar novo produto
productsRouter.post("/", zValidator("json", createProductSchema), async (c) => {
  try {
    const user = c.get("user");
    const data = c.req.valid("json");

    const { searchParams } = new URL(c.req.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return c.json({ error: "Organization ID is required" }, 400);
    }

    // Verificar se o usuário é membro da organização
    const member = await db.member.findFirst({
      where: {
        organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    // Verificar se o slug já existe na organização
    const existingProduct = await db.product.findFirst({
      where: {
        organizationId,
        slug: data.slug,
      },
    });

    if (existingProduct) {
      return c.json({ error: "Slug already exists in this organization" }, 400);
    }

    const product = await db.product.create({
      data: {
        ...data,
        organizationId,
        creatorId: user.id,
      },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return c.json({ product }, 201);
  } catch (error) {
    console.error("Error creating product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// PUT /products/:id - Atualizar produto
productsRouter.put("/:id", zValidator("json", updateProductSchema), async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");
    const data = c.req.valid("json");

    // Buscar o produto
    const existingProduct = await db.product.findUnique({
      where: { id: productId },
    });

    if (!existingProduct) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: existingProduct.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    // Se estiver alterando o slug, verificar se já existe
    if (data.slug && data.slug !== existingProduct.slug) {
      const slugExists = await db.product.findFirst({
        where: {
          organizationId: existingProduct.organizationId,
          slug: data.slug,
          id: { not: productId },
        },
      });

      if (slugExists) {
        return c.json({ error: "Slug already exists in this organization" }, 400);
      }
    }

    const product = await db.product.update({
      where: { id: productId },
      data,
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return c.json({ product });
  } catch (error) {
    console.error("Error updating product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// DELETE /products/:id - Deletar produto
productsRouter.delete("/:id", async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");

    // Buscar o produto
    const product = await db.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    // Verificar se há pedidos ativos
    const activeOrders = await db.order.findFirst({
      where: {
        productId,
        status: { in: ["PENDING", "PROCESSING"] },
      },
    });

    if (activeOrders) {
      return c.json({
        error: "Cannot delete product with active orders. Archive it instead."
      }, 400);
    }

    // Deletar o produto (cascade irá deletar módulos, lições, etc.)
    await db.product.delete({
      where: { id: productId },
    });

    return c.json({ message: "Product deleted successfully" });
  } catch (error) {
    console.error("Error deleting product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// POST /products/:id/archive - Arquivar produto
productsRouter.post("/:id/archive", async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");

    // Buscar o produto
    const product = await db.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    const updatedProduct = await db.product.update({
      where: { id: productId },
      data: { status: "ARCHIVED" },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return c.json({ product: updatedProduct });
  } catch (error) {
    console.error("Error archiving product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// POST /products/:id/publish - Publicar produto
productsRouter.post("/:id/publish", async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");

    // Buscar o produto
    const product = await db.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    const updatedProduct = await db.product.update({
      where: { id: productId },
      data: { status: "PUBLISHED" },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return c.json({ product: updatedProduct });
  } catch (error) {
    console.error("Error publishing product:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

// GET /products/:id/checkout-links - Listar links de checkout do produto
productsRouter.get("/:id/checkout-links", async (c) => {
  try {
    const user = c.get("user");
    const productId = c.req.param("id");

    // Buscar o produto
    const product = await db.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return c.json({ error: "Product not found" }, 404);
    }

    // Verificar se o usuário tem acesso ao produto
    const member = await db.member.findFirst({
      where: {
        organizationId: product.organizationId,
        userId: user.id,
      },
    });

    if (!member) {
      return c.json({ error: "Access denied" }, 403);
    }

    const checkoutLinks = await db.checkoutLink.findMany({
      where: { productId },
      orderBy: { createdAt: "desc" },
    });

    return c.json({ checkoutLinks });
  } catch (error) {
    console.error("Error fetching checkout links:", error);
    return c.json({ error: "Internal server error" }, 500);
  }
});

export { productsRouter };
