import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { FinancialService } from "../services/financial";
import { authMiddleware } from "../middleware/auth";

const TransactionType = z.enum(["CREDIT", "DEBIT", "COMMISSION", "FEE", "WITHDRAWAL", "REFUND", "TRANSFER"]);
const LedgerEntryType = z.enum([
  "SALE_GROSS", "SALE_NET", "PLATFORM_FEE", "AFFILIATE_COMMISSION_CREDIT", 
  "AFFILIATE_COMMISSION_DEBIT", "COPRODUCER_COMMISSION_CREDIT", "COPRODUCER_COMMISSION_DEBIT",
  "WITHDRAWAL", "REFUND", "ADJUSTMENT", "CHARGEBACK"
]);
const ReportType = z.enum(["REVENUE", "COMMISSION", "<PERSON>SH_FLOW", "BALANC<PERSON>"]);
const ReportPeriod = z.enum(["DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY", "CUSTOM"]);

const router = new Hono().basePath("/financial").use(authMiddleware);

const createTransactionSchema = z.object({
  organizationId: z.string(),
  type: TransactionType,
  amount: z.number().positive(),
  currency: z.string().optional(),
  description: z.string().optional(),
  fromUserId: z.string().optional(),
  toUserId: z.string().optional(),
  orderId: z.string().optional(),
  paymentMethod: z.string().optional(),
  externalId: z.string().optional(),
  gatewayId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

const createLedgerEntrySchema = z.object({
  organizationId: z.string(),
  userId: z.string(),
  type: LedgerEntryType,
  amount: z.number(),
  description: z.string().optional(),
  orderId: z.string().optional(),
  transactionId: z.string().optional(),
  referenceId: z.string().optional(),
  availableAt: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

const financialSummarySchema = z.object({
  organizationId: z.string(),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  userId: z.string().optional(),
});

const generateReportSchema = z.object({
  organizationId: z.string(),
  type: ReportType,
  period: ReportPeriod,
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
});

router
  .post(
    "/transactions",
    validator("json", createTransactionSchema),
    describeRoute({
      tags: ["Financial"],
      summary: "Create transaction",
      description: "Create a new financial transaction",
    }),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const transaction = await FinancialService.createTransaction(data);
        return c.json(transaction);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .post(
    "/ledger-entries",
    validator("json", createLedgerEntrySchema),
    describeRoute({
      tags: ["Financial"],
      summary: "Create ledger entry",
      description: "Create a new ledger entry",
    }),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const processedData = {
          ...data,
          availableAt: data.availableAt ? new Date(data.availableAt) : undefined,
        };
        const ledgerEntry = await FinancialService.createLedgerEntry(processedData);
        return c.json(ledgerEntry);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .post(
    "/orders/:orderId/process-payment",
    describeRoute({
      tags: ["Financial"],
      summary: "Process order payment",
      description: "Process payment for an order",
    }),
    async (c) => {
      try {
        const orderId = c.req.param("orderId");
        const result = await FinancialService.processOrderPayment(orderId);
        return c.json(result);
      } catch (error) {
        if (error instanceof Error) {
          throw new HTTPException(400, { message: error.message });
        }
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .get(
    "/summary",
    validator("query", financialSummarySchema),
    describeRoute({
      tags: ["Financial"],
      summary: "Get financial summary",
      description: "Get financial summary for organization",
    }),
    async (c) => {
      try {
        const data = c.req.valid("query");
        const summary = await FinancialService.getFinancialSummary(
          data.organizationId,
          new Date(data.startDate),
          new Date(data.endDate),
          data.userId
        );
        return c.json(summary);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .post(
    "/reports/generate",
    validator("json", generateReportSchema),
    describeRoute({
      tags: ["Financial"],
      summary: "Generate financial report",
      description: "Generate a financial report",
    }),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const report = await FinancialService.generateFinancialReport(
          data.organizationId,
          data.type,
          data.period,
          new Date(data.startDate),
          new Date(data.endDate)
        );
        return c.json(report);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .get(
    "/balance/:organizationId/:userId",
    describeRoute({
      tags: ["Financial"],
      summary: "Get user balance",
      description: "Get balance for a specific user",
    }),
    async (c) => {
      try {
        const organizationId = c.req.param("organizationId");
        const userId = c.req.param("userId");
        const balance = await FinancialService.getUserBalance(organizationId, userId);
        return c.json(balance);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  )
  .post(
    "/balance-snapshot",
    validator("json", z.object({
      organizationId: z.string(),
      userId: z.string().optional(),
    })),
    describeRoute({
      tags: ["Financial"],
      summary: "Create balance snapshot",
      description: "Create a balance snapshot",
    }),
    async (c) => {
      try {
        const { organizationId, userId } = c.req.valid("json");
        const snapshot = await FinancialService.createBalanceSnapshot(organizationId, userId);
        return c.json(snapshot);
      } catch (error) {
        throw new HTTPException(500, { message: "Internal server error" });
      }
    }
  );

export const financialRouter = router;