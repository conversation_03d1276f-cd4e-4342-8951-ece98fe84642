import { <PERSON>o } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { auth } from "@repo/auth";
import { ChatwootService } from "../../services/chatwoot";

const contactsRouter = new Hono();

// Middleware de autenticação
contactsRouter.use("*", async (c, next) => {
  const authResult = await auth.api.authenticate(c.req);
  if (!authResult.success) {
    return c.json({ error: "Unauthorized" }, 401);
  }
  c.set("user", authResult.user);
  await next();
});

// Schema de validação para criar contato
const createContactSchema = z.object({
  name: z.string().min(1),
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  customAttributes: z.record(z.any()).optional(),
});

// Schema de validação para atualizar contato
const updateContactSchema = z.object({
  name: z.string().min(1).optional(),
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  customAttributes: z.record(z.any()).optional(),
});

// Listar todos os contatos
contactsRouter.get("/", async (c) => {
  try {
    const user = c.get("user");
    const { page = "1", limit = "20", search } = c.req.query();

    // TODO: Implementar busca de contatos via Chatwoot API
    // Por enquanto retornando mock data

    return c.json({
      success: true,
      data: [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: 0,
      },
    });
  } catch (error) {
    console.error("Erro ao listar contatos:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Obter contato específico
contactsRouter.get("/:id", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    // TODO: Implementar busca de contato específico via Chatwoot API

    return c.json({
      success: true,
      data: null,
    });
  } catch (error) {
    console.error("Erro ao obter contato:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Criar novo contato
contactsRouter.post("/", zValidator("json", createContactSchema), async (c) => {
  try {
    const body = c.req.valid("json");
    const user = c.get("user");

    // TODO: Implementar criação de contato via Chatwoot API

    return c.json({
      success: true,
      data: null,
    }, 201);
  } catch (error) {
    console.error("Erro ao criar contato:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Atualizar contato
contactsRouter.patch("/:id", zValidator("json", updateContactSchema), async (c) => {
  try {
    const { id } = c.req.param();
    const body = c.req.valid("json");
    const user = c.get("user");

    // TODO: Implementar atualização de contato via Chatwoot API

    return c.json({
      success: true,
      data: null,
    });
  } catch (error) {
    console.error("Erro ao atualizar contato:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Deletar contato
contactsRouter.delete("/:id", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    // TODO: Implementar deleção de contato via Chatwoot API

    return c.json({
      success: true,
      message: "Contato deletado com sucesso",
    });
  } catch (error) {
    console.error("Erro ao deletar contato:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

export { contactsRouter };
