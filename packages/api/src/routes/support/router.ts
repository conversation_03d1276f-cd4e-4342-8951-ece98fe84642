import { Hono } from "hono";
import { conversationsRouter } from "./conversations";
import { contactsRouter } from "./contacts";
import { messagesRouter } from "./messages";
import { webhooksRouter } from "./webhooks";

export const supportRouter = new Hono()
  .route("/conversations", conversationsRouter)
  .route("/contacts", contactsRouter)
  .route("/messages", messagesRouter)
  .route("/webhooks", webhooksRouter);

export type SupportRouter = typeof supportRouter;
