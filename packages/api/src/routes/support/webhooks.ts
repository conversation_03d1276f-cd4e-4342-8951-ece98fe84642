import { <PERSON><PERSON> } from "hono";
import { z<PERSON>alidator } from "@hono/zod-validator";
import { z } from "zod";
import { ChatwootService } from "../../services/chatwoot";

const webhooksRouter = new Hono();

// Schema de validação para webhook de conversa
const conversationWebhookSchema = z.object({
  event: z.string(),
  conversation_created: z.object({
    id: z.number(),
    inbox_id: z.number(),
    status: z.string(),
    priority: z.string(),
    assignee_id: z.number().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    contact: z.object({
      id: z.number(),
      name: z.string(),
      email: z.string().optional(),
      phone_number: z.string().optional(),
    }),
  }).optional(),
  conversation_updated: z.object({
    id: z.number(),
    inbox_id: z.number(),
    status: z.string(),
    priority: z.string(),
    assignee_id: z.number().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    contact: z.object({
      id: z.number(),
      name: z.string(),
      email: z.string().optional(),
      phone_number: z.string().optional(),
    }),
  }).optional(),
  conversation_resolved: z.object({
    id: z.number(),
    inbox_id: z.number(),
    status: z.string(),
    priority: z.string(),
    assignee_id: z.number().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    contact: z.object({
      id: z.number(),
      name: z.string(),
      email: z.string().optional(),
      phone_number: z.string().optional(),
    }),
  }).optional(),
});

// Schema de validação para webhook de mensagem
const messageWebhookSchema = z.object({
  event: z.string(),
  message_created: z.object({
    id: z.number(),
    conversation_id: z.number(),
    content: z.string(),
    message_type: z.number(),
    created_at: z.string(),
    sender: z.object({
      id: z.number(),
      name: z.string(),
      type: z.string(),
    }),
  }).optional(),
});

// Webhook para eventos de conversa
webhooksRouter.post("/conversations", zValidator("json", conversationWebhookSchema), async (c) => {
  try {
    const body = c.req.valid("json");

    console.log("Webhook de conversa recebido:", body.event);

    // Processar diferentes tipos de eventos
    switch (body.event) {
      case "conversation_created":
        await handleConversationCreated(body.conversation_created);
        break;
      case "conversation_updated":
        await handleConversationUpdated(body.conversation_updated);
        break;
      case "conversation_resolved":
        await handleConversationResolved(body.conversation_resolved);
        break;
      default:
        console.log("Evento não processado:", body.event);
    }

    return c.json({ success: true, message: "Webhook processado com sucesso" });
  } catch (error) {
    console.error("Erro ao processar webhook de conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Webhook para eventos de mensagem
webhooksRouter.post("/messages", zValidator("json", messageWebhookSchema), async (c) => {
  try {
    const body = c.req.valid("json");

    console.log("Webhook de mensagem recebido:", body.event);

    if (body.event === "message_created" && body.message_created) {
      await handleMessageCreated(body.message_created);
    }

    return c.json({ success: true, message: "Webhook processado com sucesso" });
  } catch (error) {
    console.error("Erro ao processar webhook de mensagem:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Webhook para eventos de contato
webhooksRouter.post("/contacts", async (c) => {
  try {
    const body = await c.req.json();

    console.log("Webhook de contato recebido:", body.event);

    // TODO: Implementar processamento de eventos de contato

    return c.json({ success: true, message: "Webhook processado com sucesso" });
  } catch (error) {
    console.error("Erro ao processar webhook de contato:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Funções auxiliares para processar webhooks
async function handleConversationCreated(conversation: any) {
  try {
    console.log("Nova conversa criada:", conversation.id);

    // TODO: Implementar lógica para nova conversa
    // - Notificar agentes disponíveis
    // - Atualizar dashboard em tempo real
    // - Sincronizar com banco local

  } catch (error) {
    console.error("Erro ao processar conversa criada:", error);
  }
}

async function handleConversationUpdated(conversation: any) {
  try {
    console.log("Conversa atualizada:", conversation.id);

    // TODO: Implementar lógica para conversa atualizada
    // - Atualizar interface em tempo real
    // - Notificar mudanças de status
    // - Sincronizar com banco local

  } catch (error) {
    console.error("Erro ao processar conversa atualizada:", error);
  }
}

async function handleConversationResolved(conversation: any) {
  try {
    console.log("Conversa resolvida:", conversation.id);

    // TODO: Implementar lógica para conversa resolvida
    // - Atualizar métricas
    // - Notificar resolução
    // - Sincronizar com banco local

  } catch (error) {
    console.error("Erro ao processar conversa resolvida:", error);
  }
}

async function handleMessageCreated(message: any) {
  try {
    console.log("Nova mensagem criada:", message.id);

    // TODO: Implementar lógica para nova mensagem
    // - Atualizar conversa em tempo real
    // - Notificar agentes se necessário
    // - Sincronizar com banco local

  } catch (error) {
    console.error("Erro ao processar mensagem criada:", error);
  }
}

// Endpoint para verificar se webhooks estão funcionando
webhooksRouter.get("/health", async (c) => {
  return c.json({
    success: true,
    message: "Webhooks funcionando",
    timestamp: new Date().toISOString(),
  });
});

export { webhooksRouter };
