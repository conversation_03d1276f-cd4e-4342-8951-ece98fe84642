import { <PERSON>o } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { auth } from "@repo/auth";
import { ChatwootService } from "../../services/chatwoot";

const conversationRouter = new Hono();

// Middleware de autenticação
conversationRouter.use("*", async (c, next) => {
  const authResult = await auth.api.authenticate(c.req);
  if (!authResult.success) {
    return c.json({ error: "Unauthorized" }, 401);
  }
  c.set("user", authResult.user);
  await next();
});

// Schema de validação para criar conversa
const createConversationSchema = z.object({
  contactId: z.string(),
  message: z.string().optional(),
  channel: z.enum(["whatsapp", "sms", "email"]),
  source: z.string().optional(),
});

// Schema de validação para atualizar conversa
const updateConversationSchema = z.object({
  status: z.enum(["open", "pending", "resolved", "closed"]).optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  assigneeId: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// Listar todas as conversas
conversationRouter.get("/", async (c) => {
  try {
    const user = c.get("user");
    const { page = "1", limit = "20", status, channel } = c.req.query();

    const conversations = await ChatwootService.listConversations({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      channel,
      accountId: user.organizationId || "1",
    });

    return c.json({
      success: true,
      data: conversations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: conversations.length,
      },
    });
  } catch (error) {
    console.error("Erro ao listar conversas:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Obter conversa específica
conversationRouter.get("/:id", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    const conversation = await ChatwootService.getConversation(id, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao obter conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Criar nova conversa
conversationRouter.post("/", zValidator("json", createConversationSchema), async (c) => {
  try {
    const body = c.req.valid("json");
    const user = c.get("user");

    const conversation = await ChatwootService.createConversation({
      ...body,
      accountId: user.organizationId || "1",
      agentId: user.id,
    });

    return c.json({
      success: true,
      data: conversation,
    }, 201);
  } catch (error) {
    console.error("Erro ao criar conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Atualizar conversa
conversationRouter.patch("/:id", zValidator("json", updateConversationSchema), async (c) => {
  try {
    const { id } = c.req.param();
    const body = c.req.valid("json");
    const user = c.get("user");

    const conversation = await ChatwootService.updateConversation(id, body, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao atualizar conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Fechar conversa
conversationRouter.post("/:id/close", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    const conversation = await ChatwootService.closeConversation(id, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao fechar conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Reabrir conversa
conversationRouter.post("/:id/reopen", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    const conversation = await ChatwootService.reopenConversation(id, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao reabrir conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Atribuir conversa
conversationRouter.post("/:id/assign", async (c) => {
  try {
    const { id } = c.req.param();
    const { assigneeId } = await c.req.json();
    const user = c.get("user");

    if (!assigneeId) {
      return c.json({ error: "ID do agente é obrigatório" }, 400);
    }

    const conversation = await ChatwootService.assignConversation(id, assigneeId, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao atribuir conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Marcar como favorita
conversationRouter.post("/:id/star", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    const conversation = await ChatwootService.starConversation(id, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao marcar conversa como favorita:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Remover favorita
conversationRouter.delete("/:id/star", async (c) => {
  try {
    const { id } = c.req.param();
    const user = c.get("user");

    const conversation = await ChatwootService.unstarConversation(id, user.organizationId || "1");

    if (!conversation) {
      return c.json({ error: "Conversa não encontrada" }, 404);
    }

    return c.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error("Erro ao remover favorita da conversa:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

export { conversationRouter as conversationsRouter };
