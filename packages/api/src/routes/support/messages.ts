import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { auth } from "@repo/auth";
import { ChatwootService } from "../../services/chatwoot";

const messagesRouter = new Hono();

// Middleware de autenticação
messagesRouter.use("*", async (c, next) => {
  const authResult = await auth.api.authenticate(c.req);
  if (!authResult.success) {
    return c.json({ error: "Unauthorized" }, 401);
  }
  c.set("user", authResult.user);
  await next();
});

// Schema de validação para enviar mensagem
const sendMessageSchema = z.object({
  content: z.string().min(1),
  messageType: z.enum(["incoming", "outgoing"]).default("outgoing"),
  attachments: z.array(z.object({
    url: z.string().url(),
    name: z.string(),
    type: z.string(),
  })).optional(),
});

// Schema de validação para mensagem privada
const privateNoteSchema = z.object({
  content: z.string().min(1),
});

// Enviar mensagem para uma conversa
messagesRouter.post("/conversations/:conversationId", zValidator("json", sendMessageSchema), async (c) => {
  try {
    const { conversationId } = c.req.param();
    const body = c.req.valid("json");
    const user = c.get("user");

    const message = await ChatwootService.sendMessage(
      conversationId,
      body.content,
      body.messageType
    );

    return c.json({
      success: true,
      data: message,
    }, 201);
  } catch (error) {
    console.error("Erro ao enviar mensagem:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Listar mensagens de uma conversa
messagesRouter.get("/conversations/:conversationId", async (c) => {
  try {
    const { conversationId } = c.req.param();
    const { page = "1", limit = "50" } = c.req.query();
    const user = c.get("user");

    // TODO: Implementar busca de mensagens via Chatwoot API
    // Por enquanto retornando mock data

    return c.json({
      success: true,
      data: [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: 0,
      },
    });
  } catch (error) {
    console.error("Erro ao listar mensagens:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Adicionar nota privada (visível apenas para agentes)
messagesRouter.post("/conversations/:conversationId/private", zValidator("json", privateNoteSchema), async (c) => {
  try {
    const { conversationId } = c.req.param();
    const body = c.req.valid("json");
    const user = c.get("user");

    // TODO: Implementar nota privada via Chatwoot API

    return c.json({
      success: true,
      data: null,
    }, 201);
  } catch (error) {
    console.error("Erro ao adicionar nota privada:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Marcar mensagem como lida
messagesRouter.post("/:messageId/read", async (c) => {
  try {
    const { messageId } = c.req.param();
    const user = c.get("user");

    // TODO: Implementar marcação de mensagem como lida via Chatwoot API

    return c.json({
      success: true,
      message: "Mensagem marcada como lida",
    });
  } catch (error) {
    console.error("Erro ao marcar mensagem como lida:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

// Deletar mensagem
messagesRouter.delete("/:messageId", async (c) => {
  try {
    const { messageId } = c.req.param();
    const user = c.get("user");

    // TODO: Implementar deleção de mensagem via Chatwoot API

    return c.json({
      success: true,
      message: "Mensagem deletada com sucesso",
    });
  } catch (error) {
    console.error("Erro ao deletar mensagem:", error);
    return c.json({ error: "Erro interno do servidor" }, 500);
  }
});

export { messagesRouter };
