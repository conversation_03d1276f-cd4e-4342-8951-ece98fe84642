import { db } from "../client"
import type { ProductType, ProductStatus, ProductVisibility } from "@prisma/client"

export interface CreateProductData {
  organizationId: string
  name: string
  slug: string
  description?: string
  shortDescription?: string
  price: number
  comparePrice?: number
  type: ProductType
  status?: ProductStatus
  visibility?: ProductVisibility
  categoryId?: string
  thumbnail?: string
  gallery?: string[]
  tags?: string[]
  features?: string[]
  requirements?: string[]
  duration?: number
  level?: string
  language?: string
  certificate?: boolean
  downloadable?: boolean
  settings?: Record<string, any>
}

export interface UpdateProductData {
  name?: string
  slug?: string
  description?: string
  shortDescription?: string
  price?: number
  comparePrice?: number
  type?: ProductType
  status?: ProductStatus
  visibility?: ProductVisibility
  categoryId?: string
  thumbnail?: string
  gallery?: string[]
  tags?: string[]
  features?: string[]
  requirements?: string[]
  duration?: number
  level?: string
  language?: string
  certificate?: boolean
  downloadable?: boolean
  settings?: Record<string, any>
}

export async function createProduct(data: CreateProductData) {
  return db.product.create({
    data: {
      ...data,
      settings: data.settings || {}
    },
    include: {
      category: true,
      organization: true,
      modules: {
        include: {
          lessons: true
        }
      }
    }
  })
}

export async function getProductById(id: string) {
  return db.product.findUnique({
    where: { id },
    include: {
      category: true,
      organization: true,
      modules: {
        include: {
          lessons: true
        },
        orderBy: { order: 'asc' }
      },
      enrollments: {
        include: {
          user: true
        }
      },
      reviews: {
        include: {
          user: true
        }
      }
    }
  })
}

export async function getProductBySlug(organizationId: string, slug: string) {
  return db.product.findUnique({
    where: {
      organizationId_slug: {
        organizationId,
        slug
      }
    },
    include: {
      category: true,
      organization: true,
      modules: {
        include: {
          lessons: true
        },
        orderBy: { order: 'asc' }
      }
    }
  })
}

export async function getProductsByOrganization(organizationId: string, filters?: {
  type?: ProductType
  status?: ProductStatus
  categoryId?: string
  search?: string
}) {
  const where: any = {
    organizationId
  }

  if (filters?.type) {
    where.type = filters.type
  }

  if (filters?.status) {
    where.status = filters.status
  }

  if (filters?.categoryId) {
    where.categoryId = filters.categoryId
  }

  if (filters?.search) {
    where.OR = [
      { name: { contains: filters.search, mode: 'insensitive' } },
      { description: { contains: filters.search, mode: 'insensitive' } },
      { tags: { has: filters.search } }
    ]
  }

  return db.product.findMany({
    where,
    include: {
      category: true,
      _count: {
        select: {
          enrollments: true,
          reviews: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
}

export async function updateProduct(id: string, data: UpdateProductData) {
  return db.product.update({
    where: { id },
    data,
    include: {
      category: true,
      organization: true,
      modules: {
        include: {
          lessons: true
        }
      }
    }
  })
}

export async function deleteProduct(id: string) {
  return db.product.delete({
    where: { id }
  })
}

export async function getPublishedProducts(organizationId: string, type?: ProductType) {
  const where: any = {
    organizationId,
    status: 'PUBLISHED',
    visibility: 'PUBLIC'
  }

  if (type) {
    where.type = type
  }

  return db.product.findMany({
    where,
    include: {
      category: true,
      _count: {
        select: {
          enrollments: true,
          reviews: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
}

export async function getProductStats(productId: string) {
  const [enrollmentCount, reviewCount, avgRating] = await Promise.all([
    db.courseEnrollment.count({
      where: { productId }
    }),
    db.productReview.count({
      where: { productId }
    }),
    db.productReview.aggregate({
      where: { productId },
      _avg: {
        rating: true
      }
    })
  ])

  return {
    enrollmentCount,
    reviewCount,
    avgRating: avgRating._avg.rating || 0
  }
}