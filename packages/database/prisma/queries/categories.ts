import { db } from "../client"

export interface CreateCategoryData {
  organizationId: string
  name: string
  slug: string
  description?: string
  icon?: string
  color?: string
  sortOrder?: number
  parentId?: string
}

export interface UpdateCategoryData {
  name?: string
  slug?: string
  description?: string
  icon?: string
  color?: string
  sortOrder?: number
  parentId?: string
}

export async function createCategory(data: CreateCategoryData) {
  return db.category.create({
    data,
    include: {
      parent: true,
      children: true,
      products: true
    }
  })
}

export async function getCategoryById(id: string) {
  return db.category.findUnique({
    where: { id },
    include: {
      parent: true,
      children: {
        orderBy: { sortOrder: 'asc' }
      },
      products: {
        where: {
          status: 'PUBLISHED'
        }
      },
      _count: {
        select: {
          products: true
        }
      }
    }
  })
}

export async function getCategoryBySlug(organizationId: string, slug: string) {
  return db.category.findUnique({
    where: {
      organizationId_slug: {
        organizationId,
        slug
      }
    },
    include: {
      parent: true,
      children: {
        orderBy: { sortOrder: 'asc' }
      },
      products: {
        where: {
          status: 'PUBLISHED'
        }
      }
    }
  })
}

export async function getCategoriesByOrganization(organizationId: string, includeEmpty = false) {
  const where: any = {
    organizationId
  }

  if (!includeEmpty) {
    where.products = {
      some: {
        status: 'PUBLISHED'
      }
    }
  }

  return db.category.findMany({
    where,
    include: {
      parent: true,
      children: {
        orderBy: { sortOrder: 'asc' }
      },
      _count: {
        select: {
          products: {
            where: {
              status: 'PUBLISHED'
            }
          }
        }
      }
    },
    orderBy: [
      { sortOrder: 'asc' },
      { name: 'asc' }
    ]
  })
}

export async function getRootCategories(organizationId: string) {
  return db.category.findMany({
    where: {
      organizationId,
      parentId: null
    },
    include: {
      children: {
        include: {
          _count: {
            select: {
              products: {
                where: {
                  status: 'PUBLISHED'
                }
              }
            }
          }
        },
        orderBy: { sortOrder: 'asc' }
      },
      _count: {
        select: {
          products: {
            where: {
              status: 'PUBLISHED'
            }
          }
        }
      }
    },
    orderBy: [
      { sortOrder: 'asc' },
      { name: 'asc' }
    ]
  })
}

export async function updateCategory(id: string, data: UpdateCategoryData) {
  return db.category.update({
    where: { id },
    data,
    include: {
      parent: true,
      children: true,
      products: true
    }
  })
}

export async function deleteCategory(id: string) {
  return db.category.delete({
    where: { id }
  })
}

export async function reorderCategories(organizationId: string, categoryOrders: { id: string; sortOrder: number }[]) {
  const updatePromises = categoryOrders.map(({ id, sortOrder }) =>
    db.category.update({
      where: { id },
      data: { sortOrder }
    })
  )

  return Promise.all(updatePromises)
}