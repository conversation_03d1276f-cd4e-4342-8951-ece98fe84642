datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider = "prisma-client-js"
    output   = "./generated/client"
}

generator zod {
    provider         = "zod-prisma-types"
    output           = "./zod"
    createInputTypes = true
    addIncludeType   = false
    addSelectType    = false
}

generator json {
    provider = "prisma-json-types-generator"
}

enum UserRole {
    USER
    TEACHER
    AFFILIATE
    ADMIN
    SUPER_ADMIN
}

enum OrderStatus {
    PENDING
    PROCESSING
    COMPLETED
    CANCELLED
    REFUNDED
    FAILED
}

enum OrderType {
    PURCHASE
    SUBSCRIPTION
    UPGRADE
    RENEWAL
}

enum TransactionType {
    CREDIT
    DEBIT
    TRANSFER
    REFUND
    FEE
    COMMISSION
    WITHDRAWAL
    DEPOSIT
    PAYOUT
    CHARGEBACK
    DISPUTE
}

enum TransactionStatus {
    PENDING
    PROCESSING
    COMPLETED
    FAILED
    CANCELLED
    REVERSED
}

enum LedgerEntryType {
    SALE_GROSS
    PLATFORM_FEE
    PAYMENT_PROCESSOR_FEE
    AFFILIATE_COMMISSION_DEBIT
    AFFILIATE_COMMISSION_CREDIT
    COPRODUCER_COMMISSION_DEBIT
    COPRODUCER_COMMISSION_CREDIT
    REFUND
    WITHDRAWAL
    DEPOSIT
    TRANSFER_IN
    TRANSFER_OUT
    ADJUSTMENT
    DISPUTE_FEE
    CHARGEBACK_FEE
    INTERNATIONAL_FEE
}

enum ReportType {
    REVENUE
    COMMISSION
    FEES
    CASH_FLOW
    BALANCE
    TAX
    RECONCILIATION
}

enum ReportPeriod {
    DAILY
    WEEKLY
    MONTHLY
    QUARTERLY
    YEARLY
    CUSTOM
}

enum CashFlowType {
    INFLOW
    OUTFLOW
}

enum CashFlowCategory {
    SALES
    COMMISSIONS
    FEES
    WITHDRAWALS
    REFUNDS
    ADJUSTMENTS
    TRANSFERS
    OTHER
}

model User {
    id                  String       @id @default(cuid())
    name                String?
    email               String       @unique
    emailVerified       Boolean      @default(false)
    image               String?
    createdAt           DateTime     @default(now())
    updatedAt           DateTime     @updatedAt
    username            String?      @unique
    role                UserRole     @default(USER)
    banned              Boolean?
    banReason           String?
    banExpires          DateTime?
    onboardingComplete  Boolean      @default(false)
    paymentsCustomerId  String?
    locale              String?
    twoFactorEnabled    Boolean      @default(false)
    twoFactor           TwoFactor[]
    sessions            Session[]
    accounts            Account[]
    passkeys            Passkey[]
    organizationMembers Member[]
    organizationInvites Invitation[]
    purchases           Purchase[]
    aiChats             AiChat[]

    // Digital Products & White-Label
    createdProducts       Product[]              @relation("CreatedProducts")
    orders                Order[]                @relation("OrderBuyer")
    affiliatedOrders      Order[]                @relation("OrderAffiliate")
    enrollments           CourseEnrollment[]
    certificates          Certificate[]
    assets                Asset[]                @relation("CreatedAssets")
    lessonProgress        LessonProgress[]
    affiliateProfile      AffiliateProfile?
    teacherProfile        TeacherProfile?
    ledgerEntries         LedgerEntry[]
    withdraws             Withdraw[]
    reviews               ProductReview[]
    favoriteProducts      Product[]              @relation("FavoriteProducts")
    createdCoupons        Coupon[]               @relation("CreatedCoupons")
    affiliateInvitations  AffiliateInvitation[]  @relation("CreatedAffiliateInvitations")
    teacherInvitations    TeacherInvitation[]    @relation("CreatedTeacherInvitations")
    coProducerInvitations CoProducerInvitation[] @relation("CreatedCoProducerInvitations")
    coProducerRoles       CoProducer[]
    mentoringSessions     MentoringSession[]
    gatewayConfigurations GatewayConfiguration[]
    offerInteractions     OfferInteraction[]
    transactionsFrom      Transaction[]          @relation("TransactionFrom")
    transactionsTo        Transaction[]          @relation("TransactionTo")
    cashFlowEntries       CashFlowEntry[]
    balanceSnapshots      BalanceSnapshot[]

    @@map("user")
}

model Session {
    id        String   @id @default(cuid())
    expiresAt DateTime
    ipAddress String?
    userAgent String?
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    impersonatedBy String?

    activeOrganizationId String?

    token     String
    createdAt DateTime
    updatedAt DateTime

    @@unique([token])
    @@map("session")
}

model Account {
    id           String    @id @default(cuid())
    accountId    String
    providerId   String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    accessToken  String?   @db.Text
    refreshToken String?   @db.Text
    idToken      String?   @db.Text
    expiresAt    DateTime?
    password     String?

    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?
    createdAt             DateTime
    updatedAt             DateTime

    @@map("account")
}

model Verification {
    id         String   @id @default(cuid())
    identifier String
    value      String   @db.Text
    expiresAt  DateTime

    createdAt DateTime?
    updatedAt DateTime?

    @@map("verification")
}

model Passkey {
    id           String    @id @default(cuid())
    name         String?
    publicKey    String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    credentialID String
    counter      Int
    deviceType   String
    backedUp     Boolean
    transports   String?
    createdAt    DateTime?

    @@map("passkey")
}

model TwoFactor {
    id          String @id @default(cuid())
    secret      String
    backupCodes String
    userId      String
    user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("twoFactor")
}

model Organization {
    id                 String   @id @default(cuid())
    name               String
    slug               String?  @unique
    logo               String?
    createdAt          DateTime @default(now())
    updatedAt          DateTime @updatedAt
    metadata           String?
    paymentsCustomerId String?

    // White-label configuration
    domain             String? @unique
    customDomain       String? @unique
    branding           Json? // Colors, fonts, etc.
    settings           Json? // Platform settings
    subscriptionPlan   String? @default("basic")
    subscriptionStatus String? @default("active")
    billingEmail       String?
    timezone           String? @default("UTC")
    currency           String? @default("BRL")
    language           String? @default("pt")

    // Feature flags
    enableAffiliatePogram  Boolean @default(true)
    enableDigitalProducts  Boolean @default(true)
    enableCertificates     Boolean @default(true)
    enableCustomBranding   Boolean @default(false)
    enableCustomDomain     Boolean @default(false)
    enableAdvancedPayments Boolean @default(false)

    // Limits based on subscription
    maxProducts     Int? @default(10)
    maxUsers        Int? @default(100)
    maxStorageGB    Int? @default(5)
    maxTransactions Int? @default(1000)

    members     Member[]
    invitations Invitation[]
    purchases   Purchase[]
    aiChats     AiChat[]

    // Digital Products & Platform
    products              Product[]
    categories            Category[]
    orders                Order[]
    coupons               Coupon[]
    assets                Asset[]                @relation("OrganizationAssets")
    certificates          Certificate[]
    gatewayConfigurations GatewayConfiguration[]
    paymentProviders      PaymentProvider[]
    webhooks              Webhook[]
    customPages           CustomPage[]
    emailTemplates        EmailTemplate[]
    ledgerEntries         LedgerEntry[]
    transactions          Transaction[]
    financialReports      FinancialReport[]
    cashFlowEntries       CashFlowEntry[]
    balanceSnapshots      BalanceSnapshot[]
    reconciliationEntries ReconciliationEntry[]

    @@map("organization")
}

model Member {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    role           String
    createdAt      DateTime

    @@unique([organizationId, userId])
    @@map("member")
}

model Invitation {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    email          String
    role           String?
    status         String
    expiresAt      DateTime
    inviterId      String
    user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

    @@map("invitation")
}

enum PurchaseType {
    SUBSCRIPTION
    ONE_TIME
}

model Purchase {
    id             String        @id @default(cuid())
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId         String?
    type           PurchaseType
    customerId     String
    subscriptionId String?       @unique
    productId      String
    status         String?
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@index([subscriptionId])
    @@map("purchase")
}

model AiChat {
    id             String        @id @default(cuid())
    organizationId String?
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    title          String?
    /// [Array<{role: "user" | "assistant"; content: string;}>]
    messages       Json          @default("[]") /// @zod.custom.use(z.array(z.object({ role: z.enum(['user', 'assistant']), content: z.string() })))
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@map("ai_chat")
}

// =============================================================================
// DIGITAL PRODUCTS SYSTEM
// =============================================================================

enum ProductType {
    COURSE
    EBOOK
    MENTORSHIP
    SUBSCRIPTION
    BUNDLE
}

enum ProductStatus {
    DRAFT
    PUBLISHED
    ARCHIVED
    SUSPENDED
}

enum ProductVisibility {
    PUBLIC
    PRIVATE
    UNLISTED
}

enum CheckoutType {
    DEFAULT
    CUSTOM
    EXTERNAL
}

model Category {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    slug           String
    description    String?
    icon           String?
    color          String?
    sortOrder      Int          @default(0)
    parentId       String?
    parent         Category?    @relation("CategoryParent", fields: [parentId], references: [id])
    children       Category[]   @relation("CategoryParent")
    products       Product[]
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, slug])
    @@map("category")
}

model Product {
    id                String            @id @default(cuid())
    organizationId    String
    organization      Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId         String
    creator           User              @relation("CreatedProducts", fields: [creatorId], references: [id])
    name              String
    slug              String
    description       String?
    shortDescription  String?
    priceCents        Int // Preço em centavos
    comparePriceCents Int? // Preço de comparação em centavos
    currency          String            @default("BRL") // Moeda do produto
    type              ProductType
    status            ProductStatus     @default(DRAFT)
    visibility        ProductVisibility @default(PRIVATE)
    categoryId        String?
    category          Category?         @relation(fields: [categoryId], references: [id])
    thumbnail         String?
    gallery           String[]          @default([])
    tags              String[]          @default([])
    features          String[]          @default([])
    requirements      String[]          @default([])
    duration          Int? // in minutes
    level             String? // BEGINNER, INTERMEDIATE, ADVANCED
    language          String            @default("pt-BR")
    certificate       Boolean           @default(false)
    downloadable      Boolean           @default(false)
    checkoutType      CheckoutType      @default(DEFAULT)
    /// Product settings and metadata
    settings          Json              @default("{}") /// @zod.custom.use(z.record(z.any()))
    createdAt         DateTime          @default(now())
    updatedAt         DateTime          @updatedAt

    // Relations
    modules     ProductModule[]
    enrollments CourseEnrollment[]
    reviews     ProductReview[]

    // Product types
    ebook     Ebook?
    course    Course?
    mentoring Mentoring?

    // Sales & Marketing
    offers       Offer[]
    targetOffers Offer[]     @relation("TargetProduct")
    orders       Order[]
    orderItems   OrderItem[]
    favoriteBy   User[]      @relation("FavoriteProducts")

    // Assets & Content
    assets Asset[] @relation("ProductAssets")

    // Affiliates & Co-producers
    affiliateInvitations  AffiliateInvitation[]
    affiliateLinks        AffiliateLink[]
    coProducerInvitations CoProducerInvitation[]
    coProducers           CoProducer[]

    // Abandoned checkouts
    abandonedCheckouts AbandonedCheckout[]

    // Checkout links
    checkoutLinks CheckoutLink[]

    @@unique([organizationId, slug])
    @@map("product")
}

model ProductModule {
    id          String          @id @default(cuid())
    productId   String
    product     Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
    title       String
    description String?
    order       Int
    duration    Int? // in minutes
    isPublished Boolean         @default(true)
    lessons     ProductLesson[]
    createdAt   DateTime        @default(now())
    updatedAt   DateTime        @updatedAt

    @@map("product_module")
}

model ProductLesson {
    id          String        @id @default(cuid())
    moduleId    String
    module      ProductModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)
    title       String
    description String?
    content     String?       @db.Text
    videoUrl    String?
    duration    Int? // in minutes
    order       Int
    isPublished Boolean       @default(true)
    isFree      Boolean       @default(false)
    /// Additional lesson data
    metadata    Json          @default("{}") /// @zod.custom.use(z.record(z.any()))
    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt

    @@map("product_lesson")
}

// ===== ADDITIONAL MODELS FOR DIGITAL PRODUCTS =====

model Order {
    id              String          @id @default(cuid())
    organizationId  String
    organization    Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    buyerId         String
    buyer           User            @relation("OrderBuyer", fields: [buyerId], references: [id])
    affiliateId     String?
    affiliate       User?           @relation("OrderAffiliate", fields: [affiliateId], references: [id])
    productId       String
    product         Product         @relation(fields: [productId], references: [id])
    status          OrderStatus     @default(PENDING)
    type            OrderType       @default(PURCHASE)
    totalCents      Int // Valor total em centavos
    currency        String          @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    paymentMethod   String?
    paymentId       String?
    createdAt       DateTime        @default(now())
    updatedAt       DateTime        @updatedAt
    items           OrderItem[]
    ledgerEntries   LedgerEntry[]
    transactions    Transaction[]
    cashFlowEntries CashFlowEntry[]

    @@map("orders")
}

model OrderItem {
    id         String  @id @default(cuid())
    orderId    String
    order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    productId  String
    product    Product @relation(fields: [productId], references: [id])
    quantity   Int     @default(1)
    priceCents Int // Preço unitário em centavos
    totalCents Int // Total em centavos

    @@map("order_items")
}

model Certificate {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id])
    productId      String?
    title          String
    description    String?
    certificateUrl String?
    issuedAt       DateTime     @default(now())
    expiresAt      DateTime?
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())

    @@map("certificates")
}

model Asset {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation("OrganizationAssets", fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId      String
    creator        User         @relation("CreatedAssets", fields: [creatorId], references: [id])
    productId      String?
    product        Product?     @relation("ProductAssets", fields: [productId], references: [id])
    name           String
    type           String
    url            String
    size           Int?
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("assets")
}

model LessonProgress {
    id        String   @id @default(cuid())
    userId    String
    user      User     @relation(fields: [userId], references: [id])
    lessonId  String
    completed Boolean  @default(false)
    progress  Int      @default(0)
    watchTime Int      @default(0)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([userId, lessonId])
    @@map("lesson_progress")
}

model AffiliateProfile {
    id                 String   @id @default(cuid())
    userId             String   @unique
    user               User     @relation(fields: [userId], references: [id])
    commissionCents    Int      @default(0) // Comissão em centavos
    totalEarningsCents Int      @default(0) // Total de ganhos em centavos
    currency           String   @default("BRL") // Moeda da conta do afiliado
    isActive           Boolean  @default(true)
    bankAccount        Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt          DateTime @default(now())
    updatedAt          DateTime @updatedAt

    @@map("affiliate_profiles")
}

model TeacherProfile {
    id          String   @id @default(cuid())
    userId      String   @unique
    user        User     @relation(fields: [userId], references: [id])
    bio         String?
    expertise   String[] @default([])
    experience  String?
    education   String?
    website     String?
    socialLinks Json? /// @zod.custom.use(z.record(z.any()).optional())
    isVerified  Boolean  @default(false)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@map("teacher_profiles")
}

model LedgerEntry {
    id              String          @id @default(cuid())
    organizationId  String
    organization    Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId          String
    user            User            @relation(fields: [userId], references: [id])
    type            LedgerEntryType
    amountCents     Int // Valor em centavos
    currency        String          @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    description     String?
    orderId         String?
    order           Order?          @relation(fields: [orderId], references: [id])
    transactionId   String?
    transaction     Transaction?    @relation(fields: [transactionId], references: [id])
    referenceId     String?
    availableAt     DateTime?
    settledAt       DateTime? // Data de liquidação
    metadata        Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt       DateTime        @default(now())
    updatedAt       DateTime        @updatedAt

    @@map("ledger_entries")
}

model Withdraw {
    id              String    @id @default(cuid())
    userId          String
    user            User      @relation(fields: [userId], references: [id])
    amountCents     Int // Valor em centavos
    currency        String    @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    status          String    @default("pending")
    method          String
    feeCents        Int? // Taxa em centavos
    netAmountCents  Int? // Valor líquido em centavos
    externalId      String? // ID do gateway de pagamento
    processedAt     DateTime?
    settledAt       DateTime?
    details         Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt

    @@map("withdraws")
}

model Coupon {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId      String
    creator        User         @relation("CreatedCoupons", fields: [creatorId], references: [id])
    code           String
    type           String
    valueCents     Int // Valor do cupom em centavos
    minAmountCents Int? // Valor mínimo em centavos
    currency       String       @default("BRL") // Moeda do cupom
    maxUses        Int?
    usedCount      Int          @default(0)
    isActive       Boolean      @default(true)
    startsAt       DateTime?
    expiresAt      DateTime?
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, code])
    @@map("coupons")
}

model CourseEnrollment {
    id          String    @id @default(cuid())
    userId      String
    user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    productId   String
    product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
    enrolledAt  DateTime  @default(now())
    completedAt DateTime?
    progress    Int       @default(0) // percentage 0-100

    @@unique([userId, productId])
    @@map("course_enrollment")
}

model ProductReview {
    id        String   @id @default(cuid())
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    productId String
    product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    rating    Int // 1-5 stars
    comment   String?
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([userId, productId])
    @@map("product_review")
}

// ===== REMAINING MODELS =====

model AffiliateInvitation {
    id              String    @id @default(cuid())
    creatorId       String
    creator         User      @relation("CreatedAffiliateInvitations", fields: [creatorId], references: [id])
    productId       String
    product         Product   @relation(fields: [productId], references: [id])
    email           String
    commissionCents Int // Comissão em centavos
    currency        String    @default("BRL") // Moeda da comissão
    status          String    @default("pending")
    expiresAt       DateTime?
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt

    @@map("affiliate_invitations")
}

model TeacherInvitation {
    id        String    @id @default(cuid())
    creatorId String
    creator   User      @relation("CreatedTeacherInvitations", fields: [creatorId], references: [id])
    email     String
    role      String    @default("teacher")
    status    String    @default("pending")
    expiresAt DateTime?
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt

    @@map("teacher_invitations")
}

model CoProducerInvitation {
    id                    String    @id @default(cuid())
    creatorId             String
    creator               User      @relation("CreatedCoProducerInvitations", fields: [creatorId], references: [id])
    productId             String
    product               Product   @relation(fields: [productId], references: [id])
    email                 String
    percentageBasisPoints Int // Porcentagem em basis points (ex: 500 = 5.00%)
    status                String    @default("pending")
    expiresAt             DateTime?
    createdAt             DateTime  @default(now())
    updatedAt             DateTime  @updatedAt

    @@map("coproducer_invitations")
}

model CoProducer {
    id                    String   @id @default(cuid())
    userId                String
    user                  User     @relation(fields: [userId], references: [id])
    productId             String
    product               Product  @relation(fields: [productId], references: [id])
    percentageBasisPoints Int // Porcentagem em basis points (ex: 500 = 5.00%)
    isActive              Boolean  @default(true)
    createdAt             DateTime @default(now())
    updatedAt             DateTime @updatedAt

    @@unique([userId, productId])
    @@map("coproducers")
}

model MentoringSession {
    id          String   @id @default(cuid())
    userId      String
    user        User     @relation(fields: [userId], references: [id])
    mentorId    String
    title       String
    description String?
    scheduledAt DateTime
    duration    Int      @default(60)
    status      String   @default("scheduled")
    meetingUrl  String?
    notes       String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@map("mentoring_sessions")
}

model GatewayConfiguration {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id])
    name           String
    type           String
    settings       Json /// @zod.custom.use(z.record(z.any()))
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("gateway_configurations")
}

model PaymentProvider {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    type           String
    settings       Json /// @zod.custom.use(z.record(z.any()))
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("payment_providers")
}

model Webhook {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    url            String
    events         String[]     @default([])
    secret         String?
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("webhooks")
}

model CustomPage {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    title          String
    slug           String
    content        String?      @db.Text
    isPublished    Boolean      @default(false)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, slug])
    @@map("custom_pages")
}

model EmailTemplate {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    subject        String
    content        String       @db.Text
    variables      String[]     @default([])
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, name])
    @@map("email_templates")
}

model Ebook {
    id        String   @id @default(cuid())
    productId String   @unique
    product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    fileUrl   String
    fileSize  Int?
    pages     Int?
    format    String   @default("PDF")
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("ebooks")
}

model Course {
    id            String   @id @default(cuid())
    productId     String   @unique
    product       Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    totalLessons  Int      @default(0)
    totalDuration Int      @default(0)
    level         String   @default("BEGINNER")
    certificate   Boolean  @default(false)
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt

    @@map("courses")
}

model Mentoring {
    id              String   @id @default(cuid())
    productId       String   @unique
    product         Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    sessionDuration Int      @default(60)
    maxSessions     Int?
    isRecurring     Boolean  @default(false)
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt

    @@map("mentorings")
}

model Offer {
    id              String             @id @default(cuid())
    productId       String
    product         Product            @relation(fields: [productId], references: [id])
    targetProductId String?
    targetProduct   Product?           @relation("TargetProduct", fields: [targetProductId], references: [id])
    name            String
    type            String
    valueCents      Int // Valor da oferta em centavos
    currency        String             @default("BRL") // Moeda da oferta
    isActive        Boolean            @default(true)
    startsAt        DateTime?
    expiresAt       DateTime?
    createdAt       DateTime           @default(now())
    updatedAt       DateTime           @updatedAt
    interactions    OfferInteraction[]

    @@map("offers")
}

model AffiliateLink {
    id            String   @id @default(cuid())
    productId     String
    product       Product  @relation(fields: [productId], references: [id])
    code          String   @unique
    clicks        Int      @default(0)
    sales         Int      @default(0)
    earningsCents Int      @default(0) // Ganhos em centavos
    currency      String   @default("BRL") // Moeda dos ganhos
    isActive      Boolean  @default(true)
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt

    @@map("affiliate_links")
}

model AbandonedCheckout {
    id        String   @id @default(cuid())
    productId String
    product   Product  @relation(fields: [productId], references: [id])
    email     String
    data      Json /// @zod.custom.use(z.record(z.any()))
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("abandoned_checkouts")
}

model CheckoutLink {
    id               String                   @id @default(cuid())
    productId        String
    product          Product                  @relation(fields: [productId], references: [id], onDelete: Cascade)
    offerId          String?
    url              String
    expiresAt        DateTime?
    utmParams        String? // JSON string
    customParams     String? // JSON string
    clickCount       Int                      @default(0)
    conversionCount  Int                      @default(0)
    totalRevenue     Int                      @default(0) // in cents
    lastClickedAt    DateTime?
    lastConversionAt DateTime?
    createdAt        DateTime                 @default(now())
    updatedAt        DateTime                 @updatedAt
    clicks           CheckoutLinkClick[]
    conversions      CheckoutLinkConversion[]

    @@map("checkout_links")
}

model CheckoutLinkClick {
    id        String       @id @default(cuid())
    linkId    String
    link      CheckoutLink @relation(fields: [linkId], references: [id], onDelete: Cascade)
    userAgent String?
    ipAddress String?
    referrer  String?
    utmParams String? // JSON string
    clickedAt DateTime     @default(now())

    @@map("checkout_link_clicks")
}

model CheckoutLinkConversion {
    id            String       @id @default(cuid())
    linkId        String
    link          CheckoutLink @relation(fields: [linkId], references: [id], onDelete: Cascade)
    orderId       String
    amount        Int // in cents
    paymentMethod String
    convertedAt   DateTime     @default(now())

    @@map("checkout_link_conversions")
}

model OfferInteraction {
    id        String   @id @default(cuid())
    offerId   String
    offer     Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
    userId    String?
    user      User?    @relation(fields: [userId], references: [id])
    type      String
    metadata  Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt DateTime @default(now())

    @@map("offer_interactions")
}

model Transaction {
    id              String            @id @default(cuid())
    organizationId  String
    organization    Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type            TransactionType
    status          TransactionStatus @default(PENDING)
    amountCents     Int // Valor em centavos
    currency        String            @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    description     String?
    externalId      String? // ID do gateway de pagamento
    gatewayId       String?
    paymentMethod   String?
    fromUserId      String?
    fromUser        User?             @relation("TransactionFrom", fields: [fromUserId], references: [id])
    toUserId        String?
    toUser          User?             @relation("TransactionTo", fields: [toUserId], references: [id])
    orderId         String?
    order           Order?            @relation(fields: [orderId], references: [id])
    parentId        String?
    parent          Transaction?      @relation("TransactionParent", fields: [parentId], references: [id])
    children        Transaction[]     @relation("TransactionParent")
    ledgerEntries   LedgerEntry[]
    fees            TransactionFee[]
    cashFlowEntries CashFlowEntry[]
    metadata        Json? /// @zod.custom.use(z.record(z.any()).optional())
    processedAt     DateTime?
    settledAt       DateTime?
    createdAt       DateTime          @default(now())
    updatedAt       DateTime          @updatedAt

    @@map("transactions")
}

model TransactionFee {
    id            String      @id @default(cuid())
    transactionId String
    transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
    type          String // PLATFORM, PAYMENT_PROCESSOR, INTERNATIONAL, etc.
    amountCents   Int // Valor da taxa em centavos
    percentage    Decimal?    @db.Decimal(5, 4)
    description   String?
    createdAt     DateTime    @default(now())

    @@map("transaction_fees")
}

model FinancialReport {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type           ReportType
    period         ReportPeriod
    startDate      DateTime
    endDate        DateTime
    data           Json /// @zod.custom.use(z.record(z.any()))
    summary        Json? /// @zod.custom.use(z.record(z.any()).optional())
    generatedBy    String?
    generatedAt    DateTime     @default(now())
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("financial_reports")
}

model CashFlowEntry {
    id             String           @id @default(cuid())
    organizationId String
    organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type           CashFlowType
    category       CashFlowCategory
    amountCents    Int // Valor em centavos
    currency       String           @default("BRL")
    description    String?
    transactionId  String?
    transaction    Transaction?     @relation(fields: [transactionId], references: [id])
    orderId        String?
    order          Order?           @relation(fields: [orderId], references: [id])
    userId         String?
    user           User?            @relation(fields: [userId], references: [id])
    date           DateTime
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt      DateTime         @default(now())
    updatedAt      DateTime         @updatedAt

    @@map("cash_flow_entries")
}

model BalanceSnapshot {
    id                    String       @id @default(cuid())
    organizationId        String
    organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId                String?
    user                  User?        @relation(fields: [userId], references: [id])
    totalBalanceCents     Int // Saldo total em centavos
    availableBalanceCents Int // Saldo disponível em centavos
    pendingBalanceCents   Int // Saldo pendente em centavos
    reservedBalanceCents  Int // Saldo reservado em centavos
    currency              String       @default("BRL")
    accountCurrency       String? // Moeda da conta do usuário
    snapshotDate          DateTime
    metadata              Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt             DateTime     @default(now())

    @@map("balance_snapshots")
}

model ReconciliationEntry {
    id                 String       @id @default(cuid())
    organizationId     String
    organization       Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    externalId         String
    externalSource     String
    internalId         String?
    amountCents        Int // Valor em centavos
    currency           String       @default("BRL")
    status             String       @default("PENDING")
    discrepancyCents   Int? // Discrepância em centavos
    reconciliationDate DateTime
    resolvedAt         DateTime?
    resolvedBy         String?
    notes              String?
    metadata           Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt          DateTime     @default(now())
    updatedAt          DateTime     @updatedAt

    @@map("reconciliation_entries")
}
