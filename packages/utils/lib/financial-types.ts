export interface MonetaryAmount {
  amountCents: number;
  currency: string;
}

export interface TransactionData {
  id: string;
  userId: string;
  type: TransactionType;
  status: TransactionStatus;
  amountCents: number;
  currency: string;
  accountCurrency?: string;
  description?: string;
  externalId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  settledAt?: Date;
}

export interface LedgerEntryData {
  id: string;
  userId: string;
  type: LedgerEntryType;
  amountCents: number;
  currency: string;
  accountCurrency?: string;
  description?: string;
  transactionId?: string;
  orderId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  settledAt?: Date;
}

export interface BalanceData {
  userId: string;
  currency: string;
  accountCurrency?: string;
  totalBalanceCents: number;
  availableBalanceCents: number;
  pendingBalanceCents: number;
  reservedBalanceCents: number;
  lastUpdated: Date;
}

export interface WithdrawData {
  id: string;
  userId: string;
  amountCents: number;
  currency: string;
  accountCurrency?: string;
  feeCents?: number;
  netAmountCents?: number;
  status: WithdrawStatus;
  method: string;
  destination: Record<string, any>;
  externalId?: string;
  createdAt: Date;
  processedAt?: Date;
  settledAt?: Date;
}

export interface TransactionFeeData {
  id: string;
  transactionId: string;
  type: string;
  amountCents: number;
  percentageBasisPoints?: number;
  description?: string;
  createdAt: Date;
}

export interface CashFlowEntryData {
  id: string;
  userId: string;
  type: CashFlowType;
  category: CashFlowCategory;
  amountCents: number;
  currency: string;
  description?: string;
  date: Date;
  createdAt: Date;
}

export interface ReconciliationEntryData {
  id: string;
  date: Date;
  type: string;
  expectedAmountCents: number;
  actualAmountCents: number;
  discrepancyCents: number;
  currency: string;
  status: string;
  description?: string;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface ProductPricingData {
  id: string;
  priceCents: number;
  comparePriceCents?: number;
  currency: string;
}

export interface CommissionData {
  commissionCents: number;
  totalEarningsCents: number;
  currency: string;
  percentageBasisPoints?: number;
}

export interface CouponData {
  id: string;
  code: string;
  type: string;
  valueCents: number;
  minAmountCents?: number;
  currency: string;
  maxUses?: number;
  usedCount: number;
  isActive: boolean;
  startsAt?: Date;
  expiresAt?: Date;
}

export interface OfferData {
  id: string;
  productId: string;
  name: string;
  type: string;
  valueCents: number;
  currency: string;
  isActive: boolean;
  startsAt?: Date;
  expiresAt?: Date;
}

export enum TransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  TRANSFER = 'TRANSFER',
  WITHDRAWAL = 'WITHDRAWAL',
  DEPOSIT = 'DEPOSIT',
  FEE = 'FEE',
  COMMISSION = 'COMMISSION',
  PAYOUT = 'PAYOUT',
  CHARGEBACK = 'CHARGEBACK',
  DISPUTE = 'DISPUTE'
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum LedgerEntryType {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT',
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  COMMISSION = 'COMMISSION',
  FEE = 'FEE',
  WITHDRAWAL = 'WITHDRAWAL',
  DEPOSIT = 'DEPOSIT',
  ADJUSTMENT = 'ADJUSTMENT',
  PAYMENT_PROCESSOR_FEE = 'PAYMENT_PROCESSOR_FEE',
  DISPUTE_FEE = 'DISPUTE_FEE',
  CHARGEBACK_FEE = 'CHARGEBACK_FEE',
  INTERNATIONAL_FEE = 'INTERNATIONAL_FEE'
}

export enum WithdrawStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export enum CashFlowType {
  INFLOW = 'INFLOW',
  OUTFLOW = 'OUTFLOW'
}

export enum CashFlowCategory {
  REVENUE = 'REVENUE',
  EXPENSE = 'EXPENSE',
  INVESTMENT = 'INVESTMENT',
  FINANCING = 'FINANCING',
  OPERATING = 'OPERATING'
}

export interface FinancialCalculations {
  calculateNetAmount(grossCents: number, feeCents: number): number;
  calculateCommission(amountCents: number, basisPoints: number): number;
  calculateFee(amountCents: number, basisPoints: number): number;
  calculateTax(amountCents: number, taxBasisPoints: number): number;
  applyDiscount(amountCents: number, discountBasisPoints: number): number;
}

export interface CurrencyConversion {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  timestamp: Date;
}

export interface ExchangeRateProvider {
  getRate(from: string, to: string): Promise<number>;
  getRates(base: string): Promise<Record<string, number>>;
  isSupported(currency: string): boolean;
}