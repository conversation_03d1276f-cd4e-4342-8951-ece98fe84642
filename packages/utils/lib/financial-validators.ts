import { validateCurrency } from './currency';
import type { MonetaryAmount, TransactionData, LedgerEntryData, BalanceData } from './financial-types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function validateMonetaryAmount(amount: MonetaryAmount): ValidationResult {
  const errors: string[] = [];

  if (!Number.isInteger(amount.amountCents)) {
    errors.push('Amount must be an integer (cents)');
  }

  if (amount.amountCents < 0) {
    errors.push('Amount cannot be negative');
  }

  if (!amount.currency || typeof amount.currency !== 'string') {
    errors.push('Currency is required and must be a string');
  } else if (!validateCurrency(amount.currency)) {
    errors.push(`Invalid currency code: ${amount.currency}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateTransaction(transaction: Partial<TransactionData>): ValidationResult {
  const errors: string[] = [];

  if (!transaction.userId || typeof transaction.userId !== 'string') {
    errors.push('User ID is required');
  }

  if (!transaction.type || typeof transaction.type !== 'string') {
    errors.push('Transaction type is required');
  }

  if (!transaction.status || typeof transaction.status !== 'string') {
    errors.push('Transaction status is required');
  }

  if (transaction.amountCents !== undefined) {
    if (!Number.isInteger(transaction.amountCents)) {
      errors.push('Amount must be an integer (cents)');
    }

    if (transaction.amountCents <= 0) {
      errors.push('Transaction amount must be positive');
    }
  }

  if (transaction.currency) {
    if (!validateCurrency(transaction.currency)) {
      errors.push(`Invalid currency code: ${transaction.currency}`);
    }
  }

  if (transaction.accountCurrency && !validateCurrency(transaction.accountCurrency)) {
    errors.push(`Invalid account currency code: ${transaction.accountCurrency}`);
  }

  if (transaction.externalId && typeof transaction.externalId !== 'string') {
    errors.push('External ID must be a string');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateLedgerEntry(entry: Partial<LedgerEntryData>): ValidationResult {
  const errors: string[] = [];

  if (!entry.userId || typeof entry.userId !== 'string') {
    errors.push('User ID is required');
  }

  if (!entry.type || typeof entry.type !== 'string') {
    errors.push('Ledger entry type is required');
  }

  if (entry.amountCents !== undefined) {
    if (!Number.isInteger(entry.amountCents)) {
      errors.push('Amount must be an integer (cents)');
    }
  }

  if (entry.currency) {
    if (!validateCurrency(entry.currency)) {
      errors.push(`Invalid currency code: ${entry.currency}`);
    }
  }

  if (entry.accountCurrency && !validateCurrency(entry.accountCurrency)) {
    errors.push(`Invalid account currency code: ${entry.accountCurrency}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateBalance(balance: Partial<BalanceData>): ValidationResult {
  const errors: string[] = [];

  if (!balance.userId || typeof balance.userId !== 'string') {
    errors.push('User ID is required');
  }

  if (!balance.currency || typeof balance.currency !== 'string') {
    errors.push('Currency is required');
  } else if (!validateCurrency(balance.currency)) {
    errors.push(`Invalid currency code: ${balance.currency}`);
  }

  if (balance.accountCurrency && !validateCurrency(balance.accountCurrency)) {
    errors.push(`Invalid account currency code: ${balance.accountCurrency}`);
  }

  const balanceFields = [
    'totalBalanceCents',
    'availableBalanceCents',
    'pendingBalanceCents',
    'reservedBalanceCents',
  ] as const;

  for (const field of balanceFields) {
    if (balance[field] !== undefined) {
      if (!Number.isInteger(balance[field])) {
        errors.push(`${field} must be an integer (cents)`);
      }

      if (balance[field]! < 0) {
        errors.push(`${field} cannot be negative`);
      }
    }
  }

  if (
    balance.totalBalanceCents !== undefined &&
    balance.availableBalanceCents !== undefined &&
    balance.pendingBalanceCents !== undefined &&
    balance.reservedBalanceCents !== undefined
  ) {
    const calculatedTotal = 
      balance.availableBalanceCents + 
      balance.pendingBalanceCents + 
      balance.reservedBalanceCents;

    if (balance.totalBalanceCents !== calculatedTotal) {
      errors.push('Total balance must equal sum of available, pending, and reserved balances');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateBasisPoints(basisPoints: number): ValidationResult {
  const errors: string[] = [];

  if (!Number.isInteger(basisPoints)) {
    errors.push('Basis points must be an integer');
  }

  if (basisPoints < 0) {
    errors.push('Basis points cannot be negative');
  }

  if (basisPoints > 10000) {
    errors.push('Basis points cannot exceed 10000 (100%)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateCurrencyPair(fromCurrency: string, toCurrency: string): ValidationResult {
  const errors: string[] = [];

  if (!validateCurrency(fromCurrency)) {
    errors.push(`Invalid source currency: ${fromCurrency}`);
  }

  if (!validateCurrency(toCurrency)) {
    errors.push(`Invalid target currency: ${toCurrency}`);
  }

  if (fromCurrency === toCurrency) {
    errors.push('Source and target currencies cannot be the same');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateExchangeRate(rate: number): ValidationResult {
  const errors: string[] = [];

  if (typeof rate !== 'number' || isNaN(rate)) {
    errors.push('Exchange rate must be a valid number');
  }

  if (rate <= 0) {
    errors.push('Exchange rate must be positive');
  }

  if (!isFinite(rate)) {
    errors.push('Exchange rate must be finite');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateTransactionLimits(
  amountCents: number,
  limits: {
    minAmountCents?: number;
    maxAmountCents?: number;
    dailyLimitCents?: number;
    monthlyLimitCents?: number;
  },
  currentUsage?: {
    dailyUsageCents?: number;
    monthlyUsageCents?: number;
  }
): ValidationResult {
  const errors: string[] = [];

  if (limits.minAmountCents && amountCents < limits.minAmountCents) {
    errors.push(`Amount below minimum limit of ${limits.minAmountCents} cents`);
  }

  if (limits.maxAmountCents && amountCents > limits.maxAmountCents) {
    errors.push(`Amount exceeds maximum limit of ${limits.maxAmountCents} cents`);
  }

  if (limits.dailyLimitCents && currentUsage?.dailyUsageCents !== undefined) {
    const newDailyUsage = currentUsage.dailyUsageCents + amountCents;
    if (newDailyUsage > limits.dailyLimitCents) {
      errors.push(`Transaction would exceed daily limit of ${limits.dailyLimitCents} cents`);
    }
  }

  if (limits.monthlyLimitCents && currentUsage?.monthlyUsageCents !== undefined) {
    const newMonthlyUsage = currentUsage.monthlyUsageCents + amountCents;
    if (newMonthlyUsage > limits.monthlyLimitCents) {
      errors.push(`Transaction would exceed monthly limit of ${limits.monthlyLimitCents} cents`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateBusinessRules(
  transaction: Partial<TransactionData>,
  balance?: Partial<BalanceData>
): ValidationResult {
  const errors: string[] = [];

  if (transaction.type === 'WITHDRAWAL' || transaction.type === 'TRANSFER') {
    if (balance?.availableBalanceCents !== undefined && transaction.amountCents !== undefined) {
      if (balance.availableBalanceCents < transaction.amountCents) {
        errors.push('Insufficient available balance for transaction');
      }
    }
  }

  if (transaction.type === 'REFUND') {
    if (transaction.amountCents !== undefined && transaction.amountCents <= 0) {
      errors.push('Refund amount must be positive');
    }
  }

  if (transaction.status === 'COMPLETED' && !transaction.settledAt) {
    errors.push('Completed transactions must have a settlement date');
  }

  if (transaction.status === 'FAILED' && transaction.settledAt) {
    errors.push('Failed transactions cannot have a settlement date');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export class FinancialValidator {
  static validateAll(data: {
    transaction?: Partial<TransactionData>;
    ledgerEntry?: Partial<LedgerEntryData>;
    balance?: Partial<BalanceData>;
    monetaryAmount?: MonetaryAmount;
  }): ValidationResult {
    const allErrors: string[] = [];

    if (data.transaction) {
      const result = validateTransaction(data.transaction);
      allErrors.push(...result.errors);
    }

    if (data.ledgerEntry) {
      const result = validateLedgerEntry(data.ledgerEntry);
      allErrors.push(...result.errors);
    }

    if (data.balance) {
      const result = validateBalance(data.balance);
      allErrors.push(...result.errors);
    }

    if (data.monetaryAmount) {
      const result = validateMonetaryAmount(data.monetaryAmount);
      allErrors.push(...result.errors);
    }

    if (data.transaction && data.balance) {
      const businessResult = validateBusinessRules(data.transaction, data.balance);
      allErrors.push(...businessResult.errors);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
    };
  }
}