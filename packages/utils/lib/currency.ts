export interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  decimals: number;
}

export const SUPPORTED_CURRENCIES: Record<string, CurrencyConfig> = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    decimals: 2,
  },
  BRL: {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    decimals: 2,
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    decimals: 2,
  },
  GBP: {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    decimals: 2,
  },
};

export function centsToDecimal(cents: number, currency: string = 'USD'): number {
  const config = SUPPORTED_CURRENCIES[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }
  return cents / Math.pow(10, config.decimals);
}

export function decimalToCents(amount: number, currency: string = 'USD'): number {
  const config = SUPPORTED_CURRENCIES[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }
  return Math.round(amount * Math.pow(10, config.decimals));
}

export function formatCurrency(
  cents: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  const config = SUPPORTED_CURRENCIES[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }

  const amount = centsToDecimal(cents, currency);
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: config.code,
    minimumFractionDigits: config.decimals,
    maximumFractionDigits: config.decimals,
  }).format(amount);
}

export function formatCurrencyCompact(
  cents: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  const config = SUPPORTED_CURRENCIES[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }

  const amount = centsToDecimal(cents, currency);
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: config.code,
    notation: 'compact',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  }).format(amount);
}

export function basisPointsToPercentage(basisPoints: number): number {
  return basisPoints / 100;
}

export function percentageToBasisPoints(percentage: number): number {
  return Math.round(percentage * 100);
}

export function formatPercentage(
  basisPoints: number,
  decimals: number = 2
): string {
  const percentage = basisPointsToPercentage(basisPoints);
  return `${percentage.toFixed(decimals)}%`;
}

export function validateCurrency(currency: string): boolean {
  return currency in SUPPORTED_CURRENCIES;
}

export function getCurrencySymbol(currency: string): string {
  const config = SUPPORTED_CURRENCIES[currency];
  return config?.symbol || currency;
}

export function addCents(cents1: number, cents2: number): number {
  return cents1 + cents2;
}

export function subtractCents(cents1: number, cents2: number): number {
  return cents1 - cents2;
}

export function multiplyCents(cents: number, multiplier: number): number {
  return Math.round(cents * multiplier);
}

export function divideCents(cents: number, divisor: number): number {
  return Math.round(cents / divisor);
}

export function calculatePercentageOfCents(
  cents: number,
  basisPoints: number
): number {
  return Math.round((cents * basisPoints) / 10000);
}

export function convertCurrency(
  cents: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRate: number
): number {
  if (fromCurrency === toCurrency) {
    return cents;
  }
  
  const fromConfig = SUPPORTED_CURRENCIES[fromCurrency];
  const toConfig = SUPPORTED_CURRENCIES[toCurrency];
  
  if (!fromConfig || !toConfig) {
    throw new Error(`Unsupported currency conversion: ${fromCurrency} to ${toCurrency}`);
  }
  
  const amount = centsToDecimal(cents, fromCurrency);
  const convertedAmount = amount * exchangeRate;
  
  return decimalToCents(convertedAmount, toCurrency);
}