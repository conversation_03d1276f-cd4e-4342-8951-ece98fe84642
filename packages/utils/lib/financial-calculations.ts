import { calculatePercentageOfCents, addCents, subtractCents } from './currency';
import type { FinancialCalculations, MonetaryAmount } from './financial-types';

export class PolarFinancialCalculations implements FinancialCalculations {
  calculateNetAmount(grossCents: number, feeCents: number): number {
    return subtractCents(grossCents, feeCents);
  }

  calculateCommission(amountCents: number, basisPoints: number): number {
    return calculatePercentageOfCents(amountCents, basisPoints);
  }

  calculateFee(amountCents: number, basisPoints: number): number {
    return calculatePercentageOfCents(amountCents, basisPoints);
  }

  calculateTax(amountCents: number, taxBasisPoints: number): number {
    return calculatePercentageOfCents(amountCents, taxBasisPoints);
  }

  applyDiscount(amountCents: number, discountBasisPoints: number): number {
    const discountCents = calculatePercentageOfCents(amountCents, discountBasisPoints);
    return subtractCents(amountCents, discountCents);
  }
}

export const financialCalculations = new PolarFinancialCalculations();

export function calculateOrderTotal(
  items: Array<{ priceCents: number; quantity: number }>,
  discountBasisPoints: number = 0,
  taxBasisPoints: number = 0
): {
  subtotalCents: number;
  discountCents: number;
  taxCents: number;
  totalCents: number;
} {
  const subtotalCents = items.reduce(
    (total, item) => addCents(total, item.priceCents * item.quantity),
    0
  );

  const discountCents = discountBasisPoints > 0 
    ? financialCalculations.applyDiscount(subtotalCents, discountBasisPoints) - subtotalCents
    : 0;

  const afterDiscountCents = subtractCents(subtotalCents, Math.abs(discountCents));
  const taxCents = taxBasisPoints > 0 
    ? financialCalculations.calculateTax(afterDiscountCents, taxBasisPoints)
    : 0;

  const totalCents = addCents(afterDiscountCents, taxCents);

  return {
    subtotalCents,
    discountCents: Math.abs(discountCents),
    taxCents,
    totalCents,
  };
}

export function calculateAffiliateCommission(
  orderTotalCents: number,
  commissionBasisPoints: number
): number {
  return financialCalculations.calculateCommission(orderTotalCents, commissionBasisPoints);
}

export function calculatePaymentProcessorFee(
  amountCents: number,
  fixedFeeCents: number,
  percentageFeeBasisPoints: number
): number {
  const percentageFee = financialCalculations.calculateFee(amountCents, percentageFeeBasisPoints);
  return addCents(fixedFeeCents, percentageFee);
}

export function calculateWithdrawFee(
  amountCents: number,
  feeStructure: {
    fixedFeeCents: number;
    percentageFeeBasisPoints: number;
    minFeeCents: number;
    maxFeeCents: number;
  }
): number {
  const calculatedFee = calculatePaymentProcessorFee(
    amountCents,
    feeStructure.fixedFeeCents,
    feeStructure.percentageFeeBasisPoints
  );

  return Math.min(
    Math.max(calculatedFee, feeStructure.minFeeCents),
    feeStructure.maxFeeCents
  );
}

export function splitPayment(
  totalCents: number,
  splits: Array<{ recipientId: string; basisPoints: number }>
): Array<{ recipientId: string; amountCents: number }> {
  const totalBasisPoints = splits.reduce((sum, split) => sum + split.basisPoints, 0);
  
  if (totalBasisPoints !== 10000) {
    throw new Error('Split percentages must total 100% (10000 basis points)');
  }

  return splits.map(split => ({
    recipientId: split.recipientId,
    amountCents: calculatePercentageOfCents(totalCents, split.basisPoints),
  }));
}

export function calculateRefundAmount(
  originalAmountCents: number,
  refundBasisPoints: number = 10000,
  refundFeeCents: number = 0
): {
  refundAmountCents: number;
  refundFeeCents: number;
  netRefundCents: number;
} {
  const refundAmountCents = calculatePercentageOfCents(originalAmountCents, refundBasisPoints);
  const netRefundCents = subtractCents(refundAmountCents, refundFeeCents);

  return {
    refundAmountCents,
    refundFeeCents,
    netRefundCents,
  };
}

export function calculateCouponDiscount(
  orderCents: number,
  coupon: {
    type: 'FIXED' | 'PERCENTAGE';
    valueCents?: number;
    percentageBasisPoints?: number;
    minAmountCents?: number;
    maxDiscountCents?: number;
  }
): number {
  if (coupon.minAmountCents && orderCents < coupon.minAmountCents) {
    return 0;
  }

  let discountCents = 0;

  if (coupon.type === 'FIXED' && coupon.valueCents) {
    discountCents = coupon.valueCents;
  } else if (coupon.type === 'PERCENTAGE' && coupon.percentageBasisPoints) {
    discountCents = calculatePercentageOfCents(orderCents, coupon.percentageBasisPoints);
  }

  if (coupon.maxDiscountCents) {
    discountCents = Math.min(discountCents, coupon.maxDiscountCents);
  }

  return Math.min(discountCents, orderCents);
}

export function calculateBalanceAfterTransaction(
  currentBalanceCents: number,
  transactionAmountCents: number,
  transactionType: 'CREDIT' | 'DEBIT'
): number {
  if (transactionType === 'CREDIT') {
    return addCents(currentBalanceCents, transactionAmountCents);
  } else {
    return subtractCents(currentBalanceCents, transactionAmountCents);
  }
}

export function validateSufficientBalance(
  availableBalanceCents: number,
  requestedAmountCents: number
): boolean {
  return availableBalanceCents >= requestedAmountCents;
}

export function calculateCompoundInterest(
  principalCents: number,
  annualRateBasisPoints: number,
  compoundingPeriods: number,
  years: number
): number {
  const rate = annualRateBasisPoints / 10000;
  const amount = principalCents * Math.pow(1 + rate / compoundingPeriods, compoundingPeriods * years);
  return Math.round(amount);
}

export function calculateMonthlyPayment(
  principalCents: number,
  annualRateBasisPoints: number,
  months: number
): number {
  const monthlyRate = (annualRateBasisPoints / 10000) / 12;
  
  if (monthlyRate === 0) {
    return Math.round(principalCents / months);
  }
  
  const payment = principalCents * 
    (monthlyRate * Math.pow(1 + monthlyRate, months)) / 
    (Math.pow(1 + monthlyRate, months) - 1);
  
  return Math.round(payment);
}