import type { TransactionData, LedgerEntryData } from './financial-types';
import { validateTransaction, validateLedgerEntry, type ValidationResult } from './financial-validators';

export interface SecurityConfig {
  encryption: {
    algorithm: string;
    keyLength: number;
    ivLength: number;
  };
  auditLog: {
    enabled: boolean;
    retentionDays: number;
    sensitiveFields: string[];
  };
  transactionLimits: {
    maxDailyAmountCents: number;
    maxMonthlyAmountCents: number;
    maxSingleTransactionCents: number;
    minTransactionCents: number;
  };
  fraudDetection: {
    enabled: boolean;
    velocityChecks: boolean;
    amountThresholds: number[];
    suspiciousPatterns: string[];
  };
  compliance: {
    pciDssCompliant: boolean;
    gdprCompliant: boolean;
    soxCompliant: boolean;
    requiresKyc: boolean;
    requiresAml: boolean;
  };
}

export const defaultSecurityConfig: SecurityConfig = {
  encryption: {
    algorithm: 'AES-256-GCM',
    keyLength: 32,
    ivLength: 16,
  },
  auditLog: {
    enabled: true,
    retentionDays: 2555,
    sensitiveFields: [
      'amountCents',
      'accountNumber',
      'routingNumber',
      'cardNumber',
      'cvv',
      'externalId',
      'userId',
    ],
  },
  transactionLimits: {
    maxDailyAmountCents: ********0,
    maxMonthlyAmountCents: *********,
    maxSingleTransactionCents: ********,
    minTransactionCents: 100,
  },
  fraudDetection: {
    enabled: true,
    velocityChecks: true,
    amountThresholds: [100000, 500000, 1000000],
    suspiciousPatterns: [
      'RAPID_SUCCESSION',
      'ROUND_AMOUNTS',
      'UNUSUAL_HOURS',
      'GEOGRAPHIC_ANOMALY',
    ],
  },
  compliance: {
    pciDssCompliant: true,
    gdprCompliant: true,
    soxCompliant: true,
    requiresKyc: true,
    requiresAml: true,
  },
};

export interface AuditLogEntry {
  id: string;
  timestamp: Date;
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  changes: Record<string, { before: any; after: any }>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  riskScore?: number;
}

export interface SecurityValidationResult extends ValidationResult {
  riskScore: number;
  flags: string[];
  requiresManualReview: boolean;
}

export class FinancialSecurityValidator {
  private config: SecurityConfig;

  constructor(config: SecurityConfig = defaultSecurityConfig) {
    this.config = config;
  }

  validateTransactionSecurity(transaction: Partial<TransactionData>): SecurityValidationResult {
    const baseValidation = validateTransaction(transaction);
    const errors = [...baseValidation.errors];
    const flags: string[] = [];
    let riskScore = 0;
    let requiresManualReview = false;

    if (transaction.amountCents) {
      if (transaction.amountCents > this.config.transactionLimits.maxSingleTransactionCents) {
        errors.push('Transaction exceeds maximum single transaction limit');
        flags.push('AMOUNT_LIMIT_EXCEEDED');
        riskScore += 50;
        requiresManualReview = true;
      }

      if (transaction.amountCents < this.config.transactionLimits.minTransactionCents) {
        errors.push('Transaction below minimum transaction limit');
        flags.push('AMOUNT_TOO_LOW');
        riskScore += 10;
      }

      for (const threshold of this.config.fraudDetection.amountThresholds) {
        if (transaction.amountCents >= threshold) {
          flags.push(`HIGH_AMOUNT_${threshold}`);
          riskScore += 20;
          requiresManualReview = true;
          break;
        }
      }
    }

    if (transaction.type === 'WITHDRAWAL' && transaction.amountCents) {
      if (transaction.amountCents > 1000000) {
        flags.push('LARGE_WITHDRAWAL');
        riskScore += 30;
        requiresManualReview = true;
      }
    }

    if (transaction.status === 'FAILED' && transaction.type === 'PAYMENT') {
      flags.push('FAILED_PAYMENT');
      riskScore += 15;
    }

    if (!transaction.externalId && transaction.type === 'PAYMENT') {
      flags.push('MISSING_EXTERNAL_ID');
      riskScore += 25;
    }

    return {
      isValid: errors.length === 0,
      errors,
      riskScore,
      flags,
      requiresManualReview,
    };
  }

  validateLedgerEntrySecurity(entry: Partial<LedgerEntryData>): SecurityValidationResult {
    const baseValidation = validateLedgerEntry(entry);
    const errors = [...baseValidation.errors];
    const flags: string[] = [];
    let riskScore = 0;
    let requiresManualReview = false;

    if (entry.type === 'ADJUSTMENT') {
      flags.push('MANUAL_ADJUSTMENT');
      riskScore += 40;
      requiresManualReview = true;
    }

    if (entry.amountCents && entry.amountCents > 5000000) {
      flags.push('LARGE_LEDGER_ENTRY');
      riskScore += 25;
      requiresManualReview = true;
    }

    if (!entry.description || entry.description.length < 10) {
      flags.push('INSUFFICIENT_DESCRIPTION');
      riskScore += 15;
    }

    return {
      isValid: errors.length === 0,
      errors,
      riskScore,
      flags,
      requiresManualReview,
    };
  }

  createAuditLogEntry(
    userId: string,
    action: string,
    entityType: string,
    entityId: string,
    changes: Record<string, { before: any; after: any }>,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    }
  ): AuditLogEntry {
    const sanitizedChanges = this.sanitizeSensitiveData(changes);
    
    return {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      userId,
      action,
      entityType,
      entityId,
      changes: sanitizedChanges,
      ipAddress: metadata?.ipAddress,
      userAgent: metadata?.userAgent,
      sessionId: metadata?.sessionId,
      riskScore: this.calculateRiskScore(action, changes),
    };
  }

  private sanitizeSensitiveData(
    changes: Record<string, { before: any; after: any }>
  ): Record<string, { before: any; after: any }> {
    const sanitized = { ...changes };
    
    for (const field of this.config.auditLog.sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = {
          before: this.maskSensitiveValue(sanitized[field].before),
          after: this.maskSensitiveValue(sanitized[field].after),
        };
      }
    }
    
    return sanitized;
  }

  private maskSensitiveValue(value: any): string {
    if (typeof value === 'string') {
      if (value.length <= 4) return '***';
      return value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2);
    }
    
    if (typeof value === 'number') {
      return '***';
    }
    
    return '***';
  }

  private calculateRiskScore(
    action: string,
    changes: Record<string, { before: any; after: any }>
  ): number {
    let score = 0;
    
    const highRiskActions = ['DELETE', 'MANUAL_ADJUSTMENT', 'FORCE_SETTLEMENT'];
    if (highRiskActions.includes(action)) {
      score += 50;
    }
    
    const sensitiveFieldsChanged = Object.keys(changes).filter(field =>
      this.config.auditLog.sensitiveFields.includes(field)
    );
    
    score += sensitiveFieldsChanged.length * 10;
    
    return Math.min(score, 100);
  }

  validateComplianceRequirements(): ValidationResult {
    const errors: string[] = [];
    
    if (!this.config.compliance.pciDssCompliant) {
      errors.push('PCI DSS compliance is required for payment processing');
    }
    
    if (!this.config.compliance.gdprCompliant) {
      errors.push('GDPR compliance is required for EU users');
    }
    
    if (!this.config.encryption.algorithm.includes('AES-256')) {
      errors.push('AES-256 encryption is required for financial data');
    }
    
    if (this.config.auditLog.retentionDays < 2555) {
      errors.push('Audit log retention must be at least 7 years (2555 days)');
    }
    
    if (!this.config.fraudDetection.enabled) {
      errors.push('Fraud detection must be enabled');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  generateSecurityReport(): {
    complianceStatus: ValidationResult;
    securityScore: number;
    recommendations: string[];
  } {
    const complianceStatus = this.validateComplianceRequirements();
    let securityScore = 100;
    const recommendations: string[] = [];
    
    if (!complianceStatus.isValid) {
      securityScore -= complianceStatus.errors.length * 10;
    }
    
    if (!this.config.fraudDetection.velocityChecks) {
      securityScore -= 15;
      recommendations.push('Enable velocity checks for fraud detection');
    }
    
    if (this.config.transactionLimits.maxSingleTransactionCents > ********) {
      securityScore -= 10;
      recommendations.push('Consider lowering maximum single transaction limit');
    }
    
    if (!this.config.compliance.requiresKyc) {
      securityScore -= 20;
      recommendations.push('Implement KYC (Know Your Customer) requirements');
    }
    
    if (!this.config.compliance.requiresAml) {
      securityScore -= 20;
      recommendations.push('Implement AML (Anti-Money Laundering) checks');
    }
    
    return {
      complianceStatus,
      securityScore: Math.max(securityScore, 0),
      recommendations,
    };
  }
}

export const financialSecurity = new FinancialSecurityValidator();

export function encryptSensitiveData(data: string, key: string): string {
  return `encrypted:${Buffer.from(data).toString('base64')}`;
}

export function decryptSensitiveData(encryptedData: string, key: string): string {
  if (!encryptedData.startsWith('encrypted:')) {
    throw new Error('Invalid encrypted data format');
  }
  
  const base64Data = encryptedData.replace('encrypted:', '');
  return Buffer.from(base64Data, 'base64').toString('utf-8');
}

export function generateSecureId(): string {
  return crypto.randomUUID();
}

export function hashSensitiveData(data: string): string {
  return `hashed:${Buffer.from(data).toString('base64')}`;
}

export function validateDataIntegrity(
  data: Record<string, any>,
  expectedHash: string
): boolean {
  const dataString = JSON.stringify(data);
  const calculatedHash = hashSensitiveData(dataString);
  return calculatedHash === expectedHash;
}