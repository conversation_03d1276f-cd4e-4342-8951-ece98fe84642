# 🚀 Plano para Novo Fluxo de Onboarding - SupGateway

## 📋 Análise do Projeto Atual

### ✅ **O que já existe:**
- **Sistema de Onboarding Básico**: `OnboardingForm.tsx` e `OnboardingStep1.tsx`
- **Componentes UI Reutilizáveis**: Sistema completo de Shadcn UI + componentes customizados
- **Estrutura de Organizações**: Sistema completo de criação e gestão de organizações
- **Componentes Compartilhados**: `PageHeader`, `PageTabs`, `DataTable`, `ActionBar`
- **Sistema de Autenticação**: Better-auth configurado com sessões
- **Banco de Dados**: Schema Prisma com User, Organization, Member, etc.
- **Internacionalização**: Sistema i18n configurado
- **Estrutura Modular**: Organização clara em módulos (saas, ui, shared)

### 🔍 **O que precisa ser criado/modificado:**
- **Novo fluxo multi-step** com 6 etapas como na imagem
- **Componente de progresso lateral** com navegação visual
- **Validação de CPF** e outros campos brasileiros
- **Integração com sistema de organizações** existente
- **Novos campos de onboarding** no banco de dados

## 🎯 **Objetivo do Novo Onboarding**

Criar um fluxo de onboarding moderno e intuitivo que:
1. **Coleta informações essenciais** do usuário (CPF, objetivo, faturamento, etc.)
2. **Cria automaticamente** a organização do usuário
3. **Configura o ambiente** baseado nas respostas
4. **Redireciona** para o dashboard principal

## 🏗️ **Arquitetura Proposta**

### **1. Estrutura de Arquivos**
```
apps/web/modules/saas/onboarding/
├── components/
│   ├── OnboardingWizard.tsx          # Componente principal do wizard
│   ├── OnboardingSidebar.tsx         # Barra lateral com progresso
│   ├── OnboardingStep.tsx            # Componente base para cada step
│   ├── steps/
│   │   ├── DocumentStep.tsx          # Step 1: CPF
│   │   ├── ObjectiveStep.tsx         # Step 2: Objetivo
│   │   ├── RevenueStep.tsx           # Step 3: Faturamento
│   │   ├── SocialNetworkStep.tsx     # Step 4: Rede Social
│   │   ├── AccountTypeStep.tsx       # Step 5: Tipo de Conta (PF/PJ)
│   │   └── OriginStep.tsx            # Step 6: Como conheceu
│   └── OnboardingLayout.tsx          # Layout responsivo
├── hooks/
│   ├── useOnboardingSteps.ts         # Hook para gerenciar steps
│   └── useOnboardingData.ts          # Hook para dados do onboarding
├── lib/
│   ├── validation.ts                  # Validações (CPF, etc.)
│   ├── steps-config.ts                # Configuração dos steps
│   └── onboarding-api.ts              # API calls para salvar dados
└── types/
    └── onboarding.ts                  # Tipos TypeScript
```

### **2. Estrutura de Dados**
```typescript
interface OnboardingData {
  // Step 1: Documento
  cpf: string;

  // Step 2: Objetivo
  objective: 'digital_products' | 'affiliate_program' | 'teaching' | 'other';
  objectiveDescription?: string;

  // Step 3: Faturamento
  hasOnlineSales: boolean;
  monthlyRevenue?: number;
  salesChannels?: string[];

  // Step 4: Rede Social
  primarySocialNetwork: 'instagram' | 'youtube' | 'tiktok' | 'facebook' | 'other';
  socialNetworkUsername?: string;

  // Step 5: Tipo de Conta
  accountType: 'PF' | 'PJ';
  companyInfo?: {
    cnpj?: string;
    companyName?: string;
    tradingName?: string;
  };

  // Step 6: Origem
  origin: 'social_media' | 'search' | 'recommendation' | 'advertisement' | 'other';
  originDescription?: string;
}
```

## 🎨 **Design e UX**

### **1. Layout Principal**
- **Lado Esquerdo**: Barra de progresso vertical com logo e steps
- **Lado Direito**: Conteúdo do step atual com formulário
- **Responsivo**: Adaptação para mobile com sidebar colapsável

### **2. Componentes Visuais**
- **Progress Steps**: Círculos com ícones e estados (ativo, completo, pendente)
- **Formulários**: Campos com validação em tempo real
- **Navegação**: Botões "Anterior" e "Próximo" com estados
- **Logo**: Integração com sistema de branding existente

### **3. Estados e Feedback**
- **Validação**: Mensagens de erro contextuais
- **Loading**: Estados de carregamento durante submissão
- **Progresso**: Indicador visual de conclusão
- **Persistência**: Dados salvos automaticamente a cada step

## 🔧 **Implementação Técnica**

### **1. Componente Principal - OnboardingWizard**
```typescript
export function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(1);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({});

  const handleStepComplete = (stepData: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...stepData }));
    setCurrentStep(prev => prev + 1);
  };

  const handleComplete = async () => {
    // Criar organização e finalizar onboarding
    await createOrganizationFromOnboarding(onboardingData);
    await markOnboardingComplete();
    router.push('/app');
  };

  return (
    <OnboardingLayout>
      <OnboardingSidebar
        currentStep={currentStep}
        steps={onboardingSteps}
      />
      <OnboardingStep
        step={currentStep}
        data={onboardingData}
        onComplete={handleStepComplete}
        onFinish={handleComplete}
      />
    </OnboardingLayout>
  );
}
```

### **2. Sistema de Steps**
```typescript
const onboardingSteps = [
  {
    id: 1,
    title: 'Documento',
    subtitle: 'Para começar, precisamos do seu CPF',
    component: DocumentStep,
    validation: (data) => validateCPF(data.cpf),
  },
  {
    id: 2,
    title: 'Objetivo',
    subtitle: 'Qual seu principal objetivo no SupGateway?',
    component: ObjectiveStep,
    validation: (data) => !!data.objective,
  },
  // ... outros steps
];
```

### **3. Validações Brasileiras**
```typescript
// Validação de CPF
export function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');
  if (cleanCPF.length !== 11) return false;

  // Algoritmo de validação de CPF
  // ... implementação completa
}

// Validação de CNPJ
export function validateCNPJ(cnpj: string): boolean {
  // Implementação similar ao CPF
}
```

## 📱 **Responsividade e Mobile**

### **1. Breakpoints**
- **Desktop**: Layout lado a lado (sidebar + conteúdo)
- **Tablet**: Sidebar colapsável com toggle
- **Mobile**: Sidebar como overlay/modal

### **2. Componentes Mobile-First**
- **Touch-friendly**: Botões e inputs otimizados para touch
- **Gestos**: Swipe entre steps (opcional)
- **Navegação**: Breadcrumbs para mobile

## 🔄 **Integração com Sistema Existente**

### **1. Criação de Organização**
```typescript
async function createOrganizationFromOnboarding(data: OnboardingData) {
  const organization = await createOrganization({
    name: data.accountType === 'PF' ? data.cpf : data.companyInfo.companyName,
    slug: generateSlug(data),
    settings: {
      objective: data.objective,
      hasOnlineSales: data.hasOnlineSales,
      primarySocialNetwork: data.primarySocialNetwork,
      accountType: data.accountType,
    },
    branding: {
      // Configurações de branding baseadas no objetivo
    }
  });

  // Adicionar usuário como admin da organização
  await addMemberToOrganization({
    organizationId: organization.id,
    userId: currentUser.id,
    role: 'admin'
  });
}
```

### **2. Atualização do Usuário**
```typescript
async function markOnboardingComplete() {
  await authClient.updateUser({
    onboardingComplete: true,
    onboardingData: onboardingData, // Salvar dados para referência futura
  });
}
```

## 🚀 **Fases de Implementação**

### **Fase 1: Estrutura Base**
- [ ] Criar componentes base (OnboardingWizard, OnboardingSidebar)
- [ ] Implementar sistema de steps
- [ ] Criar layout responsivo

### **Fase 2: Steps Individuais**
- [ ] Implementar DocumentStep (CPF)
- [ ] Implementar ObjectiveStep
- [ ] Implementar RevenueStep
- [ ] Implementar SocialNetworkStep
- [ ] Implementar AccountTypeStep
- [ ] Implementar OriginStep

### **Fase 3: Integração e Validação**
- [ ] Integrar com sistema de organizações
- [ ] Implementar validações brasileiras
- [ ] Adicionar persistência de dados
- [ ] Testes de responsividade

### **Fase 4: Polimento e Deploy**
- [ ] Animações e transições
- [ ] Testes de usuário
- [ ] Otimizações de performance
- [ ] Deploy em produção

## 🎯 **Próximos Passos Imediatos**

1. **Criar estrutura de arquivos** conforme arquitetura proposta
2. **Implementar OnboardingWizard** como componente base
3. **Criar OnboardingSidebar** com design da imagem
4. **Implementar primeiro step** (DocumentStep) como prova de conceito
5. **Testar integração** com sistema de organizações existente

## 💡 **Vantagens da Abordagem**

- **Reutiliza componentes existentes** (PageHeader, PageTabs, etc.)
- **Integra com arquitetura atual** sem quebrar funcionalidades
- **Escalável** para futuras adições de steps
- **Consistente** com design system existente
- **Responsivo** desde o início
- **Internacionalizado** usando sistema i18n existente

---

**Status**: 📋 Plano criado
**Próximo**: 🚀 Implementar estrutura base
**Estimativa**: 2-3 semanas para implementação completa
