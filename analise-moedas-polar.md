# Análise de Moedas e Formatação - Plataforma Polar

## Visão Geral

A plataforma Polar implementa um sistema robusto de formatação monetária com foco em USD como moeda principal, mas com suporte a múltiplas moedas. Esta análise examina os padrões e implementações encontrados no código.

## Padrões de Armazenamento

### 1. Valores em Centavos
- **Padrão Principal**: Todos os valores monetários são armazenados em centavos (integers)
- **Tipo de Dados**: `BigInteger` no banco de dados, `int` no Python, `number` no TypeScript
- **Exemplo**: $10.50 é armazenado como `1050`

```python
# Modelo Transaction
amount: Mapped[int] = mapped_column(BigInteger, nullable=False)
account_amount: Mapped[int] = mapped_column(BigInteger, nullable=False)
tax_amount: Mapped[int] = mapped_column(BigInteger, nullable=False)
```

### 2. <PERSON><PERSON> Moeda
- **currency**: Moeda da transação (padrão: "usd")
- **account_currency**: Moeda da conta do usuário (pode diferir de USD)
- **price_currency**: Moeda dos preços de produtos

```python
currency: Mapped[str] = mapped_column(String(3), nullable=False)
account_currency: Mapped[str] = mapped_column(String(3), nullable=False)
```

## Utilitários de Formatação

### 1. Frontend (TypeScript)

#### Biblioteca Principal: `@polar-sh/ui/lib/money`
```typescript
export const formatCurrencyAndAmount = (
  cents: number,
  currency: string = 'usd',
  minimumFractionDigits?: number,
  notation?: 'standard' | 'scientific' | 'engineering' | 'compact',
): string => {
  const currencyNumberFormat = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits,
    notation,
  })
  return currencyNumberFormat.format(cents / 100)
}
```

#### Biblioteca de Checkout: `@polar-sh/checkout/utils/money`
```typescript
export const formatCurrencyNumber = (
  cents: number,
  currency: string = 'usd',
  minimumFractionDigits?: number,
  maximumFractionDigits?: number,
): string => {
  const currencyNumberFormat = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits,
  })
  return currencyNumberFormat.format(cents / 100)
}
```

#### Utilitário de Conversão
```typescript
export const getCentsInDollarString = (
  cents: number,
  showCents = false,
  pretty = false,
): string => {
  const dollars = cents / 100
  const precision = cents % 100 === 0 && !showCents ? 0 : 2

  if (pretty) {
    return dollars.toLocaleString('en-US', {
      maximumFractionDigits: precision,
      minimumFractionDigits: precision,
    })
  }
  return dollars.toFixed(precision)
}
```

### 2. Backend (Python)

#### Utilitário Principal
```python
def get_cents_in_dollar_string(cents: int) -> str:
    dollars = cents / 100
    if cents % 100 == 0:
        return str(round(dollars))
    return f"{round(dollars, 2):.2f}"
```

#### Formatação com Babel
```python
from babel.numbers import format_currency as _format_currency

def format_currency(amount: int, currency: str) -> str:
    return _format_currency(amount / 100, currency.upper(), locale="en_US")
```

## Configurações de Locale

### Padrão de Internacionalização
- **Locale Principal**: `en-US` para formatação
- **Moeda Padrão**: `USD` em praticamente todos os contextos
- **Formatação**: Utiliza `Intl.NumberFormat` no frontend e `babel.numbers` no backend

### Exemplos de Uso
```typescript
// Métricas sempre em USD
formatCurrencyAndAmount(value, 'usd', 0, 'compact')

// Balanço de conta em USD
formatCurrencyAndAmount(summary?.balance.amount ?? 0, 'USD', 0)

// Transações respeitam a moeda da transação
formatCurrencyAndAmount(amount, transaction.currency)
```

## Estrutura de Preços

### Tipos de Preço
```python
class ProductPriceAmountType(StrEnum):
    fixed = "fixed"           # Preço fixo
    custom = "custom"         # Preço customizável
    free = "free"             # Gratuito
    metered_unit = "metered_unit"  # Por unidade medida
```

### Preços Medidos
```python
class ProductPriceMeteredUnit(ProductPrice, HasPriceCurrency, NewProductPrice):
    unit_amount: Mapped[Decimal] = mapped_column(
        Numeric(17, 12),  # 12 casas decimais, 17 dígitos total
        nullable=True,
    )
    cap_amount: Mapped[int | None] = mapped_column(Integer, nullable=True)
```

## Taxas e Processamento

### Tipos de Taxa
```python
class ProcessorFeeType(StrEnum):
    payment = "payment"
    refund = "refund"
    dispute = "dispute"
    tax = "tax"
    subscription = "subscription"
    invoice = "invoice"
    cross_border_transfer = "cross_border_transfer"
    payout = "payout"
```

### Taxas da Plataforma
- **Taxa Base**: 4% + 40¢ por transação
- **Cartões Internacionais**: +1.5%
- **Assinaturas**: +0.5%
- **Disputas**: $15
- **Saques**: $2/mês + 0.25% + $0.25 por saque
- **Conversão de Moeda**: 0.25% (UE), 1% (outros países)

## Componentes de Interface

### Componentes que Usam Formatação
1. **AmountLabel**: Exibe valores formatados
2. **TransactionsList**: Lista de transações com valores
3. **CheckoutForm**: Formulário de checkout com preços
4. **PayoutModal**: Modal de saque com valores
5. **MetricCharts**: Gráficos de métricas financeiras

### Padrão de Uso
```typescript
// Componente típico
import { formatCurrencyAndAmount } from '@polar-sh/ui/lib/money'

// Uso padrão
{formatCurrencyAndAmount(amount, currency, minimumFractionDigits)}

// Para métricas (sempre USD)
{formatCurrencyAndAmount(value, 'usd', 0)}

// Para valores compactos
{formatCurrencyAndAmount(value, 'usd', 0, 'compact')}
```

## Considerações de Implementação

### Vantagens do Padrão Polar
1. **Precisão**: Armazenamento em centavos evita problemas de ponto flutuante
2. **Consistência**: Uso uniforme de `en-US` como locale
3. **Flexibilidade**: Suporte a múltiplas moedas na estrutura
4. **Performance**: Formatação eficiente com `Intl.NumberFormat`

### Padrões Recomendados
1. **Sempre armazenar em centavos** (integers)
2. **Usar locale consistente** (`en-US`)
3. **Separar moeda da transação e da conta**
4. **Implementar formatação centralizada**
5. **Suportar notação compacta para métricas**

### Estrutura de Banco
```sql
-- Exemplo de estrutura
CREATE TABLE transactions (
    currency VARCHAR(3) NOT NULL,           -- Moeda da transação
    amount BIGINT NOT NULL,                 -- Valor em centavos
    account_currency VARCHAR(3) NOT NULL,   -- Moeda da conta
    account_amount BIGINT NOT NULL,         -- Valor na moeda da conta
    tax_amount BIGINT NOT NULL              -- Taxa em centavos
);
```

## Aplicação no SupGateway

### Recomendações
1. **Adotar armazenamento em centavos** para precisão
2. **Implementar formatação centralizada** similar ao Polar
3. **Suportar BRL e USD** com conversão automática
4. **Usar `Intl.NumberFormat`** para formatação consistente
5. **Separar moeda de transação e conta** para flexibilidade

### Exemplo de Implementação
```typescript
// Utilitário para SupGateway
export const formatCurrency = (
  cents: number,
  currency: string = 'BRL',
  locale: string = 'pt-BR',
  options?: Intl.NumberFormatOptions
): string => {
  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    ...options
  })
  return formatter.format(cents / 100)
}
```

Esta análise fornece uma base sólida para implementar um sistema de moedas robusto e consistente no SupGateway, seguindo as melhores práticas observadas na plataforma Polar.

## Implementação no SupGateway

### Estrutura Implementada

O SupGateway foi atualizado seguindo os padrões identificados na análise do Polar:

#### 1. Schema de Banco Atualizado

```prisma
// Modelo AffiliateLink atualizado
model AffiliateLink {
  earningsCents Int      // Valor em centavos (antes: earnings Decimal)
  currency      String   @default("BRL") // Suporte a múltiplas moedas
}

// Modelos de porcentagem atualizados
model CoProducerInvitation {
  percentageBasisPoints Int  // Porcentagem em pontos base (antes: percentage Decimal)
}

model CoProducer {
  percentageBasisPoints Int  // Porcentagem em pontos base
}

model TransactionFee {
  percentageBasisPoints Int  // Porcentagem em pontos base
}
```

#### 2. Utilitários Financeiros

**Formatação Monetária** (`packages/utils/lib/currency.ts`):
```typescript
// Conversão centavos <-> decimal
export const centsToDecimal = (cents: number): number => cents / 100
export const decimalToCents = (decimal: number): number => Math.round(decimal * 100)

// Formatação de moeda
export const formatCurrency = (
  cents: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(cents / 100)
}

// Formatação compacta para métricas
export const formatCurrencyCompact = (
  cents: number,
  currency: string = 'USD'
): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    notation: 'compact'
  }).format(cents / 100)
}

// Conversão pontos base <-> porcentagem
export const basisPointsToPercentage = (basisPoints: number): number => basisPoints / 100
export const percentageToBasisPoints = (percentage: number): number => Math.round(percentage * 100)
```

**Cálculos Financeiros** (`packages/utils/lib/financial-calculations.ts`):
```typescript
// Cálculo de comissões
export const calculateCommission = (
  amountCents: number,
  percentageBasisPoints: number
): number => {
  const percentage = basisPointsToPercentage(percentageBasisPoints)
  return Math.round((amountCents * percentage) / 100)
}

// Cálculo de taxas
export const calculateTransactionFee = (
  amountCents: number,
  feeBasisPoints: number,
  fixedFeeCents: number = 0
): number => {
  const variableFee = calculateCommission(amountCents, feeBasisPoints)
  return variableFee + fixedFeeCents
}

// Operações aritméticas com centavos
export const addCents = (...amounts: number[]): number => {
  return amounts.reduce((sum, amount) => sum + amount, 0)
}

export const subtractCents = (amount: number, ...deductions: number[]): number => {
  const totalDeductions = deductions.reduce((sum, deduction) => sum + deduction, 0)
  return amount - totalDeductions
}
```

**Validadores Financeiros** (`packages/utils/lib/financial-validators.ts`):
```typescript
// Validação de montantes monetários
export const validateMonetaryAmount = (amount: MonetaryAmount): ValidationResult => {
  if (amount.amountCents < 0) {
    return { isValid: false, errors: ['Amount cannot be negative'] }
  }
  if (!validateCurrency(amount.currency)) {
    return { isValid: false, errors: ['Invalid currency code'] }
  }
  return { isValid: true, errors: [] }
}

// Validação de pontos base
export const validateBasisPoints = (basisPoints: number): ValidationResult => {
  if (basisPoints < 0 || basisPoints > 10000) {
    return { isValid: false, errors: ['Basis points must be between 0 and 10000'] }
  }
  return { isValid: true, errors: [] }
}
```

**Segurança Financeira** (`packages/utils/lib/financial-security.ts`):
```typescript
// Validação de segurança para transações
export class FinancialSecurityValidator {
  validateTransaction(transaction: TransactionData): SecurityValidationResult {
    const errors: string[] = []

    // Validar limites de transação
    if (transaction.amountCents > this.config.maxTransactionAmount) {
      errors.push('Transaction amount exceeds maximum limit')
    }

    // Validar moeda
    if (!this.config.allowedCurrencies.includes(transaction.currency)) {
      errors.push('Currency not allowed')
    }

    return {
      isValid: errors.length === 0,
      errors,
      riskScore: this.calculateRiskScore(transaction),
      auditLog: this.createAuditLogEntry('transaction_validation', transaction)
    }
  }
}
```

#### 3. Tipos TypeScript

**Interfaces Financeiras** (`packages/utils/lib/financial-types.ts`):
```typescript
export interface MonetaryAmount {
  amountCents: number
  currency: string
}

export interface TransactionData extends MonetaryAmount {
  id: string
  type: TransactionType
  status: TransactionStatus
  timestamp: Date
  description?: string
  metadata?: Record<string, any>
}

export interface CommissionData {
  percentageBasisPoints: number
  fixedAmountCents?: number
  currency: string
}
```

### Benefícios da Implementação

1. **Precisão Financeira**: Armazenamento em centavos elimina problemas de ponto flutuante
2. **Suporte Multi-moeda**: Estrutura preparada para USD, BRL e outras moedas
3. **Segurança**: Validadores e auditoria integrados
4. **Consistência**: Padrões uniformes seguindo as melhores práticas do Polar
5. **Escalabilidade**: Arquitetura modular e extensível

### Próximos Passos

1. Migração dos dados existentes para o novo formato
2. Atualização dos componentes de interface para usar os novos utilitários
3. Implementação de testes para validar os cálculos financeiros
4. Configuração de auditoria e monitoramento de transações

Esta implementação garante que o SupGateway tenha uma base financeira sólida, seguindo as melhores práticas identificadas na análise da plataforma Polar.
