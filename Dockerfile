# Dockerfile para Google Cloud Run - Monorepo
FROM node:22-alpine AS base

# Instalar dependências do sistema
RUN apk add --no-cache libc6-compat

# Configurar pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app

# Stage 1: Build da aplicação
FROM base AS builder

# Copiar arquivos de configuração do monorepo
COPY package.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY turbo.json ./

# Copiar workspaces essenciais
COPY config ./config
COPY packages ./packages
COPY tooling ./tooling
COPY apps/web ./apps/web

# Instalar todas as dependências dos workspaces
RUN pnpm install --no-frozen-lockfile

# Build da aplicação
RUN pnpm turbo run build --filter=@repo/web

# Stage 2: Runtime de produção
FROM node:22-alpine AS runner

# Instalar dependências do sistema
RUN apk add --no-cache libc6-compat

# Criar usuário não-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

WORKDIR /app

# Configurar ambiente de produção
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Copiar aplicação buildada
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# Mudar para usuário não-root
USER nextjs

# Expor porta
EXPOSE 3000

# Health check para Google Cloud Run
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando de inicialização
CMD ["node", "apps/web/server.js"]
