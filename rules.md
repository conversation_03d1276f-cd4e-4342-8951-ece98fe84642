# AugmentCode Rules for SupGateway Project

## Project Overview
This is a monorepo Next.js application built with TypeScript, featuring a SaaS platform with AI capabilities, payment processing, and multi-tenant architecture.

## Key Technologies & Principles

### Core Technologies
- **Frontend**: Next.js 15 App Router, React 19, TypeScript 5.8
- **UI Framework**: Shadcn UI, Radix UI, Tailwind CSS 4.1
- **State Management**: Jo<PERSON>, React Query (TanStack Query)
- **Forms**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS with custom theme variables
- **Internationalization**: next-intl
- **Database**: Prisma with Drizzle ORM
- **Authentication**: better-auth
- **Payments**: Multiple providers (Stripe, LemonSqueezy, Polar, Creem)
- **Storage**: AWS S3
- **Code Quality**: Biome for linting and formatting

### Code Generation Principles
- Write concise, technical TypeScript code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Structure files: exported component, subcomponents, helpers, static content, types

## Project Structure

### Frontend Application (`apps/web/app`)
- **Marketing**: Public marketing pages and landing pages
- **SaaS App**: Main application with dashboard, settings, and features
- **Auth**: Authentication flows (login, signup, password reset)
- **API Routes**: Backend API endpoints

### Packages (`packages/`)
- **ai**: AI-related functionality and chatbot features
- **api**: API routes and backend logic
- **auth**: Authentication configuration and helpers
- **database**: Database schema and queries
- **i18n**: Internationalization and translations
- **logs**: Logging configuration
- **mail**: Email providers and templates
- **payments**: Payment processing and providers
- **storage**: File storage providers
- **utils**: Utility functions

### Modules (`apps/web/modules/`)
- **ui**: Reusable UI components (Shadcn UI + custom)
- **saas**: SaaS-specific components and logic
- **marketing**: Marketing page components
- **shared**: Shared components and utilities
- **analytics**: Analytics and tracking

## Coding Standards

### TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types
- Avoid enums; use maps instead
- Use functional components with TypeScript interfaces
- Follow strict TypeScript configuration

### Naming Conventions
- Use lowercase with dashes for directories (e.g., `components/auth-wizard`)
- Use PascalCase for component names
- Use camelCase for variables and method names
- Favor named exports for components

### Syntax and Formatting
- Use the "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals
- Use declarative JSX
- Follow Biome formatting rules

### UI and Styling
- Use Shadcn UI, Radix UI, and Tailwind for components
- Implement responsive design with mobile-first approach
- Use the `cn` function for class name concatenation
- Global theme variables defined in `tooling/tailwind/theme.css`

## Performance Guidelines
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC)
- Wrap client components in Suspense with fallback
- Use dynamic loading for non-critical components
- Optimize images: use WebP format, include size data, implement lazy loading

## Component Patterns

### UI Components
- Extend Shadcn UI components when possible
- Use `class-variance-authority` for component variants
- Implement proper accessibility with Radix UI primitives
- Use the `cn` utility for conditional class names

### Form Components
- Use React Hook Form with Zod validation
- Implement proper error handling and loading states
- Use controlled components with proper state management

### Layout Components
- Implement responsive layouts with Tailwind CSS
- Use CSS Grid and Flexbox appropriately
- Follow mobile-first responsive design principles

## Database and API
- Use Prisma for database operations
- Implement proper error handling and validation
- Use Zod schemas for API validation
- Follow RESTful API design principles

## Authentication and Security
- Use better-auth for authentication
- Implement proper role-based access control
- Use environment variables for sensitive configuration
- Implement proper CSRF protection

## Testing and Quality
- Write unit tests for critical functionality
- Use Playwright for E2E testing
- Follow Biome linting rules
- Implement proper error boundaries

## Deployment and Configuration
- Use environment variables for configuration
- Implement proper logging and monitoring
- Use Turbo for monorepo build optimization
- Follow Next.js best practices for production builds

## Internationalization
- Use next-intl for all user-facing text
- Implement proper locale detection
- Support multiple languages (currently Portuguese and English)
- Use proper date and number formatting

## Payment Integration
- Support multiple payment providers
- Implement proper webhook handling
- Use secure payment processing practices
- Handle payment failures gracefully

## File Structure Examples

### Component File Structure
```typescript
// Component file structure
export { ComponentName } from './ComponentName';

// Subcomponents
function SubComponent() { ... }

// Helpers
function helperFunction() { ... }

// Types
interface ComponentProps { ... }

// Static content
const staticData = { ... };
```

### Module Organization
```
modules/
├── ui/
│   ├── components/     # Reusable UI components
│   └── lib/           # UI utilities
├── saas/
│   ├── components/    # SaaS-specific components
│   ├── hooks/         # Custom hooks
│   └── lib/           # SaaS utilities
└── shared/
    ├── components/    # Shared components
    └── lib/           # Shared utilities
```

## Common Patterns

### State Management
- Use Jotai for global state
- Use React Query for server state
- Implement proper loading and error states

### API Calls
- Use React Query for data fetching
- Implement proper error handling
- Use optimistic updates when appropriate

### Form Handling
- Use React Hook Form with Zod
- Implement proper validation
- Handle form submission states

### Responsive Design
- Mobile-first approach with Tailwind
- Use responsive breakpoints consistently
- Test on multiple device sizes

## Best Practices
- Follow Next.js 15 App Router patterns
- Use React Server Components when possible
- Implement proper error boundaries
- Use TypeScript strict mode
- Follow accessibility guidelines
- Implement proper SEO practices
- Use proper semantic HTML
- Implement proper loading states
- Handle errors gracefully
- Use proper TypeScript types
- Follow React best practices
- Implement proper testing
- Use proper logging
- Follow security best practices
- Implement proper monitoring
- Use proper deployment practices
