#!/bin/bash

# Teste rápido do Docker
echo "🧪 Teste Rápido do Docker"
echo ""

# Verificar arquivos essenciais
echo "📁 Verificando arquivos essenciais:"
ls -la pnpm-lock.yaml package.json pnpm-workspace.yaml turbo.json

echo ""
echo "📦 Verificando Dockerfiles disponíveis:"
ls -la apps/web/Dockerfile*

echo ""
echo "🚫 Verificando .dockerignore:"
head -10 .dockerignore

echo ""
echo "🔍 Testando contexto Docker:"
echo "Arquivos que seriam incluídos:"
find . -maxdepth 1 -name "*.yaml" -o -name "*.json" | head -10

echo ""
echo "✅ Para testar o Dockerfile.simple:"
echo "docker build -f apps/web/Dockerfile.simple . --no-cache -t test-image"
