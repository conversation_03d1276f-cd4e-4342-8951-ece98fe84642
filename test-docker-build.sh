#!/bin/bash

# Script para testar o build Docker localmente
# Este script testa se o Dockerfile está funcionando corretamente

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 Testando build Docker local...${NC}"

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker não está rodando. Inicie o Docker e tente novamente.${NC}"
    exit 1
fi

# Nome da imagem para teste
IMAGE_NAME="supgateway-test"
CONTAINER_NAME="supgateway-test-container"

# Limpar containers e imagens anteriores
echo -e "${YELLOW}🧹 Limpando containers e imagens anteriores...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build da imagem
echo -e "${BLUE}🔨 Fazendo build da imagem Docker...${NC}"
if docker build -t $IMAGE_NAME .; then
    echo -e "${GREEN}✅ Build da imagem Docker bem-sucedido!${NC}"
else
    echo -e "${RED}❌ Falha no build da imagem Docker${NC}"
    exit 1
fi

# Verificar se a imagem foi criada
if docker images | grep -q $IMAGE_NAME; then
    echo -e "${GREEN}✅ Imagem criada com sucesso${NC}"
else
    echo -e "${RED}❌ Imagem não foi criada${NC}"
    exit 1
fi

# Executar container
echo -e "${BLUE}🚀 Iniciando container...${NC}"
docker run -d \
    --name $CONTAINER_NAME \
    -p 3000:3000 \
    --env-file .env.example \
    $IMAGE_NAME

# Aguardar container inicializar
echo -e "${YELLOW}⏳ Aguardando container inicializar...${NC}"
sleep 10

# Verificar se container está rodando
if docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${GREEN}✅ Container está rodando${NC}"
else
    echo -e "${RED}❌ Container não está rodando${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Testar health check
echo -e "${BLUE}🏥 Testando health check...${NC}"
if curl -f http://localhost:3000/api/health &>/dev/null; then
    echo -e "${GREEN}✅ Health check passou${NC}"
    echo -e "${GREEN}📊 Resposta do health check:${NC}"
    curl -s http://localhost:3000/api/health | jq . 2>/dev/null || curl -s http://localhost:3000/api/health
else
    echo -e "${YELLOW}⚠️  Health check falhou (pode ser normal se não houver endpoint /api/health)${NC}"
fi

# Testar se a aplicação responde
echo -e "${BLUE}🌐 Testando se a aplicação responde...${NC}"
if curl -f http://localhost:3000 &>/dev/null; then
    echo -e "${GREEN}✅ Aplicação está respondendo na porta 3000${NC}"
else
    echo -e "${YELLOW}⚠️  Aplicação não respondeu na porta 3000${NC}"
fi

# Mostrar logs do container
echo -e "${BLUE}📋 Logs do container:${NC}"
docker logs $CONTAINER_NAME --tail 20

# Parar e limpar
echo -e "${YELLOW}🛑 Parando e limpando...${NC}"
docker stop $CONTAINER_NAME
docker rm $CONTAINER_NAME
docker rmi $IMAGE_NAME

echo -e "${GREEN}🎉 Teste Docker concluído com sucesso!${NC}"
echo -e "${GREEN}✅ O Dockerfile está funcionando corretamente${NC}"
echo -e "${BLUE}🚀 Agora você pode fazer deploy para o Google Cloud Run${NC}"
