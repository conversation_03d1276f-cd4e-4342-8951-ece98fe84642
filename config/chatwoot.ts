export const chatwootConfig = {
  baseUrl: process.env.CHATWOOT_BASE_URL || "https://app.chatwoot.com",
  accountId: process.env.CHATWOOT_ACCOUNT_ID || "",
  apiAccessToken: process.env.CHATWOOT_API_ACCESS_TOKEN || "",

  // IDs dos inboxes para diferentes canais
  inboxIds: {
    whatsapp: process.env.CHATWOOT_WHATSAPP_INBOX_ID || "1",
    sms: process.env.CHATWOOT_SMS_INBOX_ID || "2",
    email: process.env.CHATWOOT_EMAIL_INBOX_ID || "3",
  },

  // Configurações de webhook
  webhooks: {
    enabled: process.env.CHATWOOT_WEBHOOKS_ENABLED === "true",
    secret: process.env.CHATWOOT_WEBHOOK_SECRET || "",
    events: [
      "conversation_created",
      "conversation_updated",
      "conversation_resolved",
      "message_created",
      "contact_created",
      "contact_updated",
    ],
  },

  // Configurações de sincronização
  sync: {
    enabled: process.env.CHATWOOT_SYNC_ENABLED === "true",
    interval: parseInt(process.env.CHATWOOT_SYNC_INTERVAL || "300000"), // 5 minutos
    batchSize: parseInt(process.env.CHATWOOT_SYNC_BATCH_SIZE || "100"),
  },

  // Configurações de cache
  cache: {
    enabled: process.env.CHATWOOT_CACHE_ENABLED === "true",
    ttl: parseInt(process.env.CHATWOOT_CACHE_TTL || "300"), // 5 minutos
  },

  // Configurações de rate limiting
  rateLimit: {
    enabled: process.env.CHATWOOT_RATE_LIMIT_ENABLED === "true",
    maxRequests: parseInt(process.env.CHATWOOT_RATE_LIMIT_MAX_REQUESTS || "100"),
    windowMs: parseInt(process.env.CHATWOOT_RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutos
  },

  // Configurações de notificações
  notifications: {
    enabled: process.env.CHATWOOT_NOTIFICATIONS_ENABLED === "true",
    channels: {
      email: process.env.CHATWOOT_NOTIFY_EMAIL === "true",
      slack: process.env.CHATWOOT_NOTIFY_SLACK === "true",
      webhook: process.env.CHATWOOT_NOTIFY_WEBHOOK === "true",
    },
  },

  // Configurações de automação
  automation: {
    enabled: process.env.CHATWOOT_AUTOMATION_ENABLED === "true",
    rules: {
      autoAssign: process.env.CHATWOOT_AUTO_ASSIGN === "true",
      autoClose: process.env.CHATWOOT_AUTO_CLOSE === "true",
      autoTag: process.env.CHATWOOT_AUTO_TAG === "true",
    },
  },
} as const;

export type ChatwootConfig = typeof chatwootConfig;
