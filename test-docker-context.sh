#!/bin/bash

# Script para testar o contexto do Docker
# Uso: ./test-docker-context.sh

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Teste do Contexto Docker${NC}"
echo ""

# Verificar arquivos essenciais
echo -e "${BLUE}📁 Verificando arquivos essenciais:${NC}"
ESSENTIAL_FILES=("package.json" "pnpm-lock.yaml" "pnpm-workspace.yaml" "turbo.json")

for file in "${ESSENTIAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file existe${NC}"
    else
        echo -e "${RED}❌ $file não encontrado${NC}"
    fi
done

echo ""

# Verificar estrutura dos workspaces
echo -e "${BLUE}📦 Verificando estrutura dos workspaces:${NC}"
if [ -f "apps/web/package.json" ]; then
    echo -e "${GREEN}✅ apps/web/package.json existe${NC}"
else
    echo -e "${RED}❌ apps/web/package.json não encontrado${NC}"
fi

if [ -d "packages" ]; then
    PACKAGE_COUNT=$(find packages -name "package.json" | wc -l)
    echo -e "${GREEN}✅ packages/ contém $PACKAGE_COUNT package.json${NC}"
else
    echo -e "${RED}❌ packages/ não encontrado${NC}"
fi

if [ -f "config/package.json" ]; then
    echo -e "${GREEN}✅ config/package.json existe${NC}"
else
    echo -e "${RED}❌ config/package.json não encontrado${NC}"
fi

if [ -d "tooling" ]; then
    TOOLING_COUNT=$(find tooling -name "package.json" | wc -l)
    echo -e "${GREEN}✅ tooling/ contém $TOOLING_COUNT package.json${NC}"
else
    echo -e "${RED}❌ tooling/ não encontrado${NC}"
fi

echo ""

# Verificar .dockerignore
echo -e "${BLUE}🚫 Verificando .dockerignore:${NC}"
if [ -f ".dockerignore" ]; then
    echo -e "${GREEN}✅ .dockerignore existe${NC}"
    echo "Conteúdo do .dockerignore:"
    cat .dockerignore
else
    echo -e "${RED}❌ .dockerignore não encontrado${NC}"
fi

echo ""

# Simular contexto do Docker
echo -e "${BLUE}🐳 Simulando contexto do Docker:${NC}"
echo "Arquivos que seriam incluídos no contexto:"

# Listar arquivos que não estão sendo ignorados
find . -type f \
    ! -path "./.git/*" \
    ! -path "./node_modules/*" \
    ! -path "./.next/*" \
    ! -path "./dist/*" \
    ! -path "./out/*" \
    ! -path "./.turbo/*" \
    ! -path "./coverage/*" \
    ! -path "./test-results/*" \
    ! -path "./playwright-report/*" \
    ! -name "*.log" \
    ! -name "*.tsbuildinfo" \
    ! -name ".eslintcache" \
    ! -name ".DS_Store" \
    ! -name "Thumbs.db" \
    ! -name "Dockerfile*" \
    ! -name ".dockerignore" \
    | head -20

echo ""
echo -e "${BLUE}📊 Resumo:${NC}"
echo "Para resolver o problema do pnpm-lock.yaml:"
echo "1. Verifique se o arquivo existe na raiz"
echo "2. Confirme que não está sendo excluído pelo .dockerignore"
echo "3. Use o Dockerfile.minimal que é mais simples"
echo "4. Execute: ./deploy-cloud-run.sh SEU_PROJECT_ID"
