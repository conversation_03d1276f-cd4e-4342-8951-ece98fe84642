# Node modules (will be installed during build)
node_modules
**/node_modules

# Build outputs (will be generated during build)
.next
**/.next
dist
**/dist
out
**/out

# Development files
.env*
!.env.example
.env.local*

# Git
.git
.gitignore

# Documentation
README.md
**/README.md
docs/
**/docs/
_docs/
**/_docs/

# Test files
coverage/
**/coverage/
.nyc_output
**/.nyc_output
test-results/
playwright-report/
playwright/.cache/
tests/
**/tests/
*.test.*
*.spec.*

# Logs
logs/
**/logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache directories
.cache/
**/.cache/
.turbo/
**/.turbo/
.pnpm-store/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# TypeScript
*.tsbuildinfo
.eslintcache

# Temporary files
tmp/
temp/

# Docker files (avoid recursion)
Dockerfile*
.dockerignore

# Deployment files
deploy-*.sh
cloud-run-*.yaml
*.md
