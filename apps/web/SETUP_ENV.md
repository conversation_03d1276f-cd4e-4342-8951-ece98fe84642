# 🚀 Configuração do Ambiente - SupGateway

Este documento explica como configurar as variáveis de ambiente necessárias para que o build do SupGateway funcione corretamente.

## 📋 Pré-requisitos

- Node.js 18+
- PostgreSQL
- Conta no Resend (para emails)

## 🔧 Configuração Rápida

### 1. Copiar arquivo de exemplo

```bash
# Na pasta apps/web
cp env.local.example .env.local
```

### 2. Editar .env.local

Abra o arquivo `.env.local` e configure as variáveis obrigatórias:

```bash
# OBRIGATÓRIO - Chave secreta para autenticação
BETTER_AUTH_SECRET=SupGateway-secret-key-2024-minimum-32-chars-long

# OBRIGATÓRIO - URL do banco de dados
DATABASE_URL=postgresql://postgres:password@localhost:5432/SupGateway

# OBRIGATÓRIO - Chave da API Resend
RESEND_API_KEY=re_1234567890abcdef
```

### 3. Testar o build

```bash
pnpm run build
```

## 🔑 Variáveis Obrigatórias

### Autenticação
- `BETTER_AUTH_SECRET` - Chave secreta para criptografia (mínimo 32 caracteres)

### Banco de Dados
- `DATABASE_URL` - URL de conexão com PostgreSQL

### Email
- `RESEND_API_KEY` - Chave da API Resend para envio de emails

## ⚠️ Variáveis Opcionais (mas recomendadas)

### Chatwoot (Suporte ao Cliente)
```bash
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_ACCOUNT_ID=1
CHATWOOT_API_ACCESS_TOKEN=dummy-token
CHATWOOT_WEBHOOKS_ENABLED=false
```

### Provedores Sociais (Google/GitHub)
```bash
GOOGLE_CLIENT_ID=dummy-google-client-id
GOOGLE_CLIENT_SECRET=dummy-google-client-secret
GITHUB_CLIENT_ID=dummy-github-client-id
GITHUB_CLIENT_SECRET=dummy-github-client-secret
```

### Stripe (Pagamentos)
```bash
STRIPE_SECRET_KEY=sk_test_dummy
STRIPE_WEBHOOK_SECRET=whsec_dummy
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_dummy
```

## 🐛 Solução de Problemas

### Erro: "You are using the default secret"
**Solução**: Configure `BETTER_AUTH_SECRET` com uma chave de pelo menos 32 caracteres.

### Erro: "Configuração do Chatwoot incompleta"
**Solução**: Configure as variáveis do Chatwoot ou use valores dummy como no exemplo.

### Erro: "Social provider missing clientId or clientSecret"
**Solução**: Configure as variáveis dos provedores sociais ou use valores dummy.

### Erro: "Failed to collect page data"
**Solução**: Verifique se todas as variáveis obrigatórias estão configuradas.

## 🔒 Segurança

- **NUNCA** commite o arquivo `.env.local` no Git
- Use chaves secretas únicas e seguras em produção
- Rotacione as chaves regularmente
- Use variáveis de ambiente diferentes para cada ambiente (dev, staging, prod)

## 📚 Recursos Adicionais

- [Documentação do Better Auth](https://better-auth.com/)
- [Documentação do Resend](https://resend.com/docs)
- [Documentação do Next.js](https://nextjs.org/docs)

## 🆘 Suporte

Se você encontrar problemas:

1. Verifique se todas as variáveis obrigatórias estão configuradas
2. Confirme se o banco de dados está rodando
3. Verifique os logs de erro para mais detalhes
4. Abra uma issue no repositório com os detalhes do erro
