'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import { Label } from '@ui/components/label';
import { CheckoutFormData } from './types';
import { CreditCardForm } from './credit-card-form';
import { OrderBumps } from './order-bumps';
import { CouponForm } from './coupon-form';
import { CreditCard, QrCode, Receipt } from 'lucide-react';
import { formatCurrency } from '../../../../lib/utils';

interface PaymentFormProps {
	loading: boolean;
	totalAmount: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
	acceptedPayments?: string[];
	offers?: any[];
	selectedBumps: string[];
	onBumpChange: (bumps: string[]) => void;
}

export function PaymentForm({
	loading,
	totalAmount,
	installmentsLimit,
	enableInstallments,
	acceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'],
	offers,
	selectedBumps,
	onBumpChange,
}: PaymentFormProps) {
	const {
		watch,
		setValue,
		formState: { errors },
	} = useFormContext<CheckoutFormData>();

	const paymentMethod = watch('paymentMethod');

	const paymentOptions = [
		{
			value: 'CREDIT_CARD',
			label: 'Cartão de Crédito',
			icon: CreditCard,
			disabled: !acceptedPayments.includes('CREDIT_CARD'),
		},
		{
			value: 'PIX',
			label: 'PIX',
			icon: QrCode,
			disabled: !acceptedPayments.includes('PIX'),
		},
		{
			value: 'BOLETO',
			label: 'Boleto Bancário',
			icon: Receipt,
			disabled: !acceptedPayments.includes('BOLETO'),
		},
	];

	return (
		<div className='space-y-6'>
			{offers && offers.length > 0 && (
				<OrderBumps
					offers={offers}
				/>
			)}

			<CouponForm />

			<Card>
				<CardHeader>
					<CardTitle>Forma de pagamento</CardTitle>
				</CardHeader>
				<CardContent className='space-y-6'>
					<RadioGroup
						value={paymentMethod}
						onValueChange={(value) => setValue('paymentMethod', value as any)}
						className='grid gap-4'
					>
						{paymentOptions.map((option) => {
							const Icon = option.icon;
							return (
								<div
									key={option.value}
									className={`flex items-center space-x-3 rounded-lg border p-4 cursor-pointer transition-colors ${
										option.disabled
											? 'opacity-50 cursor-not-allowed'
											: paymentMethod === option.value
											? 'border-primary bg-primary/5'
											: 'hover:border-primary/50'
									}`}
									onClick={() => {
										if (!option.disabled) {
											setValue('paymentMethod', option.value as any);
										}
									}}
								>
									<RadioGroupItem
										value={option.value}
										id={option.value}
										disabled={option.disabled}
									/>
									<Icon className='h-5 w-5' />
									<Label
										htmlFor={option.value}
										className='flex-1 cursor-pointer'
									>
										{option.label}
									</Label>
								</div>
							);
						})}
					</RadioGroup>

					{paymentMethod === 'CREDIT_CARD' && (
						<CreditCardForm
							totalAmount={totalAmount}
							installmentsLimit={installmentsLimit}
							enableInstallments={enableInstallments}
						/>
					)}

					{paymentMethod === 'PIX' && (
						<div className='rounded-lg bg-blue-50 p-4 text-sm text-blue-800'>
							<p className='font-medium'>Pagamento via PIX</p>
							<p className='mt-1'>
								Após finalizar o pedido, você receberá o código PIX para
								pagamento instantâneo.
							</p>
						</div>
					)}

					{paymentMethod === 'BOLETO' && (
						<div className='rounded-lg bg-orange-50 p-4 text-sm text-orange-800'>
							<p className='font-medium'>Pagamento via Boleto</p>
							<p className='mt-1'>
								O boleto será gerado após a finalização do pedido e pode
								levar até 3 dias úteis para compensação.
							</p>
						</div>
					)}

					<Button
						type='submit'
						size='lg'
						className='w-full'
						disabled={loading}
					>
						{loading
							? 'Processando...'
							: `Finalizar Pedido - ${formatCurrency(totalAmount)}`}
					</Button>
				</CardContent>
			</Card>
		</div>
	);
}
