'use client';


import { useRouter, useSearchParams } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { CheckoutFormData, checkoutFormSchema } from './types';
import { CustomerForm } from './customer-form';
import { PaymentForm } from './payment-form';
import { useState, useEffect } from 'react';
import { CheckoutSummaryCard } from './checkout-summary-card';
import { toast } from 'sonner';

interface CheckoutFormProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		type: 'COURSE' | 'MENTORSHIP' | 'EBOOK' | 'SUBSCRIPTION' | 'BUNDLE';
		price: number;
		regularPrice?: number | null;
		installmentsLimit: number;
		enableInstallments?: boolean;
		thumbnail?: string | null;
		checkoutBanner?: string | null;
		checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
		acceptedPayments?: string[];
		checkoutSettings?: any;
		customCheckoutUrl?: string | null;
		successUrl?: string | null;
		cancelUrl?: string | null;
		termsUrl?: string | null;
		offers?: {
			id: string;
			title: string;
			description?: string | null;
			price: number;
			type: string;
		}[];
	};
}

export function CheckoutForm({ product }: CheckoutFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();


	const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
	const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
	const [formStartTime] = useState(Date.now());
	const [isProcessing, setIsProcessing] = useState(false);

	const getDefaultValues = (): Partial<CheckoutFormData> => {
		const email = searchParams.get('email');
		return {
			customerData: {
				email: email || '',
				name: '',
				cpf: '',
				phone: '',
			},
			paymentMethod: 'CREDIT_CARD',
			productId: product.id,
			creditCard: {
				cardNumber: '',
				cardHolder: '',
				cardExpiry: '',
				cardCvv: '',
				installments: 1,
			},
		};
	};

	const methods = useForm({
		defaultValues: getDefaultValues(),
		mode: 'onBlur',
	});

	useEffect(() => {
		console.log('Checkout iniciado:', {
			productId: product.id,
			productTitle: product.title,
			productPrice: product.price,
			timestamp: Date.now(),
		});
	}, []);

	const calculateTotal = (basePrice: number, bumps: string[], offers?: any[]) => {
		if (appliedCoupon) {
			return appliedCoupon.finalPrice;
		}

		let total = basePrice;
		if (bumps.length > 0 && offers) {
			total += offers
				.filter((o) => bumps.includes(o.id))
				.reduce((acc, offer) => acc + offer.price, 0);
		}
		return total;
	};

	const onSubmit = async (data: any) => {
		setIsProcessing(true);
		try {
			console.log('Processando pagamento:', data);

			// Mock do processamento de pagamento
			await new Promise(resolve => setTimeout(resolve, 2000));

			if (data.paymentMethod === 'PIX') {
				router.push(`/checkout/pix?orderId=mock-order-${Date.now()}`);
			} else {
				router.push(`/checkout/success?orderId=mock-order-${Date.now()}`);
			}

			toast.success("Seu pedido foi processado com sucesso!");
		} catch (error) {
			console.error('Erro no processamento:', error);
			toast.error("Ocorreu um erro ao processar seu pagamento. Tente novamente.");
		} finally {
			setIsProcessing(false);
		}
	};

	const totalAmount = calculateTotal(product.price, selectedBumps, product.offers);

	return (
		<FormProvider {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className='space-y-6'>
				<div className='grid gap-6 lg:grid-cols-3'>
					<div className='lg:col-span-2 space-y-6'>
						<CustomerForm />
						<PaymentForm
							loading={isProcessing}
							totalAmount={totalAmount}
							installmentsLimit={product.installmentsLimit}
							enableInstallments={product.enableInstallments}
							acceptedPayments={product.acceptedPayments}
							offers={product.offers}
							selectedBumps={selectedBumps}
							onBumpChange={setSelectedBumps}
						/>
					</div>

					<div className='lg:col-span-1'>
						<CheckoutSummaryCard
						product={{...product, creator: { id: 'creator-1', name: 'Creator' }}}
						offers={(product.offers || []).map(o => ({...o, description: o.description || null, type: 'ORDER_BUMP' as const}))}
					/>
					</div>
				</div>
			</form>
		</FormProvider>
	);
}
