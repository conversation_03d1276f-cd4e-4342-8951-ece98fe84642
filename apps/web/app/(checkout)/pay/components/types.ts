import { z } from "zod";

export const customerDataSchema = z.object({
  name: z.string().optional().default(""),
  email: z.string().optional().default(""),
  phone: z.string().optional().default(""),
  cpf: z.string().optional().default(""),
});

export const customerDataSubmitSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").refine((val) => val.trim().length >= 3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().min(1, "Email é obrigatório").email("Email inválido"),
  phone: z.string().min(1, "Telefone é obrigatório").refine((val) => val.replace(/\D/g, "").length >= 10, "Telefone deve ter pelo menos 10 dígitos"),
  cpf: z.string().min(1, "CPF é obrigatório").refine((val) => val.replace(/\D/g, "").length === 11, "CPF deve ter 11 dígitos"),
});

export const creditCardSchema = z.object({
  cardNumber: z.string().min(16, "Número do cartão inválido"),
  cardHolder: z.string().min(3, "Nome do titular inválido"),
  cardExpiry: z.string().min(5, "Data de validade inválida"),
  cardCvv: z.string().min(3, "CVV inválido"),
  installments: z.number().min(1).max(12).default(1),
});

export const checkoutFormSchema = z.object({
  customerData: customerDataSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.union([
    z.object({
      cardNumber: z.string().min(16, "Número do cartão inválido"),
      cardHolder: z.string().min(3, "Nome do titular inválido"),
      cardExpiry: z.string().min(5, "Data de validade inválida"),
      cardCvv: z.string().min(3, "CVV inválido"),
      installments: z.number().min(1).max(12).default(1),
    }).optional(),
    z.undefined()
  ]).optional().superRefine((val, ctx) => {
    const paymentMethod = (ctx as any).path?.[1]?.input?.paymentMethod;
    if (paymentMethod === "CREDIT_CARD" && !val) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
      });
    }
  }),
  productId: z.string(),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
});

export const checkoutFormSubmitSchema = z.object({
  customerData: customerDataSubmitSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.optional(z.object({
    cardNumber: z.string().min(16, "Número do cartão inválido"),
    cardHolder: z.string().min(3, "Nome do titular inválido"),
    cardExpiry: z.string().min(5, "Data de validade inválida"),
    cardCvv: z.string().min(3, "CVV inválido"),
    installments: z.number().min(1).max(12).default(1),
  })),
  productId: z.string().min(1, "ID do produto é obrigatório"),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
}).refine((data) => {
  if (data.paymentMethod === "CREDIT_CARD" && !data.creditCard) {
    return false;
  }
  return true;
}, {
  message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
  path: ["creditCard"],
});

export type CustomerData = z.infer<typeof customerDataSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

export interface CheckoutProduct {
  id: string;
  title: string;
  description?: string | null;
  type: 'COURSE' | 'MENTORSHIP' | 'EBOOK' | 'SUBSCRIPTION' | 'BUNDLE';
  price: number;
  installmentsLimit: number;
  thumbnail?: string | null;
  offers?: {
    id: string;
    title: string;
    description?: string | null;
    price: number;
    type: string;
  }[];
  regularPrice?: number | null;
  enableInstallments?: boolean;
  checkoutBanner?: string | null;
  checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
  acceptedPayments?: string[];
  checkoutSettings?: any;
  customCheckoutUrl?: string | null;
  successUrl?: string | null;
  cancelUrl?: string | null;
  termsUrl?: string | null;
  creator: {
    id: string;
    name: string;
  };
}

export interface Offer {
  id: string;
  title: string;
  description: string | null;
  price: number;
  type: 'ORDER_BUMP' | 'UPSELL' | 'DOWNSELL';
  thumbnail?: string | null;
}

export interface ProductOffer {
  id: string;
  title: string;
  description?: string;
  price: number;
  type: "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  thumbnail?: string;
  originalPrice?: number;
}

export interface PaymentOption {
  method: "CREDIT_CARD" | "PIX";
  title: string;
  description: string;
  icon: React.ReactNode;
}

export type OrderStatus =
  | "PENDING"
  | "PROCESSING"
  | "PAID"
  | "FAILED"
  | "REFUNDED"
  | "CANCELLED";

export interface Order {
  id: string;
  status: OrderStatus;
  amount: number;
  productId: string;
  customerData: CustomerData;
  paymentMethod: "CREDIT_CARD" | "PIX";
  paymentId?: string;
  orderBumps?: ProductOffer[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AsaasPaymentResponse {
  id: string;
  status: string;
  paymentLink?: string;
  pixQrCode?: {
    encodedImage: string;
    payload: string;
  };
}

export interface PaymentProcessingResult {
  orderId: string;
  status: OrderStatus;
  paymentMethod: "CREDIT_CARD" | "PIX";
  pixCode?: {
    qrCode: string;
    payload: string;
  };
  successUrl?: string;
  errorUrl?: string;
}

export interface CreditCardDisplayProps {
  number?: string;
  name?: string;
  expiry?: string;
  cvc?: string;
  focused?: "number" | "name" | "expiry" | "cvc" | null;
  className?: string;
}

export interface CreditCardInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  error?: string;
  value?: string;
  onChange?: (value: string) => void;
  onCardTypeChange?: (type: CardType) => void;
}

export type CardType = "visa" | "mastercard" | "amex" | "elo" | "";

export interface FormFieldProps {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  helpText?: string;
}

export interface OrderBumpListProps {
  offers: ProductOffer[];
  selected: string[];
  onSelect: (selected: string[]) => void;
}

export interface PaymentFormProps {
  loading: boolean;
  price: number;
  installmentsLimit: number;
  onPaymentMethodChange?: (method: "CREDIT_CARD" | "PIX") => void;
}

export interface ProductSummaryProps {
  product: CheckoutProduct;
  selectedBumps?: ProductOffer[];
  couponApplied?: {
    code: string;
    discountAmount: number;
  };
}
