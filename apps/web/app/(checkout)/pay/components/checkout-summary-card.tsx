"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@ui/components/card";
import { useFormContext } from "react-hook-form";
import type { CheckoutFormData, CheckoutProduct, Offer } from "./types";
import { formatCurrency } from "../../../../lib/utils";

interface CheckoutSummaryCardProps {
  product: CheckoutProduct;
  offers: Offer[];
}

export function CheckoutSummaryCard({ product, offers }: CheckoutSummaryCardProps) {
  const { watch } = useFormContext<CheckoutFormData>();
  const selectedOrderBumps = watch("orderBumpIds") || [];
  const couponCode = watch("couponCode");

  const selectedOffers = offers.filter(offer => 
    selectedOrderBumps.includes(offer.id)
  );

  const subtotal = product.price + selectedOffers.reduce((sum, offer) => sum + offer.price, 0);
  const discount = couponCode === "DESCONTO10" ? subtotal * 0.1 : 0;
  const total = subtotal - discount;

  return (
    <Card className="sticky top-4">
      <CardHeader>
        <CardTitle>Resumo do pedido</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-xs">IMG</span>
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm">{product.title}</h3>
              {product.description && (
                <p className="text-xs text-gray-600 mt-1">
                  {product.description}
                </p>
              )}
              <p className="font-semibold text-sm mt-1">
                {formatCurrency(product.price)}
              </p>
            </div>
          </div>

          {selectedOffers.map((offer) => (
            <div key={offer.id} className="flex justify-between items-center text-sm border-t pt-2">
              <span>{offer.title}</span>
              <span className="font-medium">
                {formatCurrency(offer.price)}
              </span>
            </div>
          ))}
        </div>

        <div className="border-t pt-3 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>
          
          {discount > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Desconto (10%)</span>
              <span>-{formatCurrency(discount)}</span>
            </div>
          )}
          
          <div className="flex justify-between font-semibold text-lg border-t pt-2">
            <span>Total</span>
            <span className="text-green-600">{formatCurrency(total)}</span>
          </div>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p>✓ Compra 100% segura</p>
          <p>✓ Garantia de 7 dias</p>
          <p>✓ Suporte 24/7</p>
        </div>
      </CardContent>
    </Card>
  );
}