"use client";

import { Checkbox } from "@ui/components/checkbox";
import { Label } from "@ui/components/label";
import { useFormContext } from "react-hook-form";
import type { CheckoutFormData, ProductOffer } from "./types";
import { formatCurrency } from "../../../../lib/utils";

interface OrderBumpsProps {
  offers: ProductOffer[];
}

export function OrderBumps({ offers }: OrderBumpsProps) {
  const { watch, setValue } = useFormContext<CheckoutFormData>();
  const selectedOrderBumps = watch("orderBumpIds") || [];

  const handleOrderBumpChange = (offerId: string, checked: boolean) => {
    if (checked) {
      setValue("orderBumpIds", [...selectedOrderBumps, offerId]);
    } else {
      setValue("orderBumpIds", selectedOrderBumps.filter(id => id !== offerId));
    }
  };

  if (!offers.length) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg">Ofertas especiais</h3>
      {offers.map((offer) => (
        <div
          key={offer.id}
          className="border rounded-lg p-4 space-y-3"
        >
          <div className="flex items-start space-x-3">
            <Checkbox
              id={`order-bump-${offer.id}`}
              checked={selectedOrderBumps.includes(offer.id)}
              onCheckedChange={(checked: boolean) =>
                handleOrderBumpChange(offer.id, checked)
              }
            />
            <div className="flex-1">
              <Label
                htmlFor={`order-bump-${offer.id}`}
                className="font-medium cursor-pointer"
              >
                {offer.title}
              </Label>
              {offer.description && (
                <p className="text-sm text-gray-600 mt-1">
                  {offer.description}
                </p>
              )}
              <div className="flex items-center space-x-2 mt-2">
                {offer.originalPrice && offer.originalPrice > offer.price && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatCurrency(offer.originalPrice)}
                  </span>
                )}
                <span className="font-semibold text-green-600">
                  {formatCurrency(offer.price)}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
