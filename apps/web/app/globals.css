@import "tailwindcss";

:root {
	--fd-banner-height: 4.5rem;
}

@layer base {
	* {
		@apply border-border;
	}
}

@layer utilities {
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}

	.no-scrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
}

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply !bg-card top-[4.5rem] md:!h-[calc(100dvh-4.5rem)];

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply !no-underline;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply !bg-muted;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}
