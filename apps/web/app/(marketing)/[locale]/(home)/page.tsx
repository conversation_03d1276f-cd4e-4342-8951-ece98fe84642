import { Benefits } from "@marketing/home/<USER>/Benefits";
import { <PERSON> } from "@marketing/home/<USER>/Hero";
import { PaymentGateways } from "@marketing/home/<USER>/PaymentGateways";
import { Testimonials } from "@marketing/home/<USER>/Testimonials";
import { setRequestLocale } from "next-intl/server";

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<main className="">
			<Hero />
			<Benefits />
			<PaymentGateways />
			<Testimonials />
		</main>
	);
}
