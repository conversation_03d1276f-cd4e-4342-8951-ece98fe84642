import { config } from "@repo/config";
import { SignupForm } from "@saas/auth/components/SignupForm";
import { getInvitation } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { withQuery } from "ufo";
import { Logo } from "@shared/components/Logo";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("auth.signup.title"),
	};
}

export default async function SignupPage({
	searchParams,
}: {
	searchParams: Promise<{
		[key: string]: string | string[] | undefined;
		invitationId?: string;
	}>;
}) {
	const params = await searchParams;
	const { invitationId } = params;

	if (!(config.auth.enableSignup || invitationId)) {
		redirect(withQuery("/auth/login", params));
	}

	if (invitationId) {
		const invitation = await getInvitation(invitationId);

		if (
			!invitation ||
			invitation.status !== "pending" ||
			invitation.expiresAt.getTime() < Date.now()
		) {
			redirect(withQuery("/auth/login", params));
		}

		return (
			<>
				<div className="flex flex-col gap-y-8 text-center">
					<div className="flex justify-center">
						<Logo className="size-15" />
					</div>
					<div className="flex flex-col gap-4">
						<h1 className="text-2xl font-bold text-foreground">
							Aceitar convite
						</h1>
						<p className="text-lg text-muted-foreground">
							Complete seu cadastro para aceitar o convite
						</p>
					</div>
				</div>
				<SignupForm prefillEmail={invitation.email} />
			</>
		);
	}

	return (
		<>
			<div className="flex flex-col gap-y-8 text-center">
				<div className="flex justify-center">
					<Logo className="size-15" />
				</div>
				<div className="flex flex-col gap-4">
					<h1 className="text-2xl font-bold text-foreground">
						Criar conta
					</h1>
					<p className="text-lg text-muted-foreground">
						Preencha os dados abaixo para criar sua conta
					</p>
				</div>
			</div>
			<SignupForm />
		</>
	);
}
