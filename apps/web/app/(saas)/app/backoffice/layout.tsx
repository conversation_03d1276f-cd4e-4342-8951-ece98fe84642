import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { AdminSidebar } from "@saas/admin/components/AdminSidebar";
import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function AdminLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// Verifica se o usuário tem permissões de admin (incluindo SUPER_ADMIN)
	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	return (
		<AppWrapper>
			<div className="flex gap-6">
				<AdminSidebar />
				<main className="flex-1">
					{children}
				</main>
			</div>
		</AppWrapper>
	);
}
