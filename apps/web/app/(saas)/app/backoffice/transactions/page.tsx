export default function BackofficeTransactionsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Transações</h1>
        <div className="flex gap-2">
          <button className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50">
            Exportar
          </button>
          <button className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50">
            Filtros Avançados
          </button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Volume Total</h3>
          <p className="text-2xl font-bold">R$ 2.847.392</p>
          <p className="text-xs text-green-600">+12.5% este mês</p>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Transações Hoje</h3>
          <p className="text-2xl font-bold">1.247</p>
          <p className="text-xs text-green-600">+8.2% vs ontem</p>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Taxa de Sucesso</h3>
          <p className="text-2xl font-bold">97.8%</p>
          <p className="text-xs text-green-600">+0.3% este mês</p>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Ticket Médio</h3>
          <p className="text-2xl font-bold">R$ 127,50</p>
          <p className="text-xs text-red-600">-2.1% este mês</p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border bg-card p-6">
          <h3 className="text-lg font-semibold mb-4">Transações por Método</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm">PIX</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">R$ 1.423.196</p>
                <p className="text-xs text-muted-foreground">50.0%</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">Cartão de Crédito</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">R$ 853.918</p>
                <p className="text-xs text-muted-foreground">30.0%</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-sm">Cartão de Débito</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">R$ 426.959</p>
                <p className="text-xs text-muted-foreground">15.0%</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span className="text-sm">Boleto</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">R$ 142.320</p>
                <p className="text-xs text-muted-foreground">5.0%</p>
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-card p-6">
          <h3 className="text-lg font-semibold mb-4">Status das Transações</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">Aprovadas</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">2.784</p>
                <p className="text-xs text-muted-foreground">97.8%</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-sm">Pendentes</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">34</p>
                <p className="text-xs text-muted-foreground">1.2%</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm">Rejeitadas</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">28</p>
                <p className="text-xs text-muted-foreground">1.0%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg border bg-card">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Transações Recentes</h3>
            <div className="flex gap-2">
              <input 
                type="text" 
                placeholder="Buscar por ID, email..." 
                className="px-3 py-2 border rounded-md text-sm"
              />
              <select className="px-3 py-2 border rounded-md text-sm">
                <option>Todos os status</option>
                <option>Aprovada</option>
                <option>Pendente</option>
                <option>Rejeitada</option>
                <option>Cancelada</option>
              </select>
              <select className="px-3 py-2 border rounded-md text-sm">
                <option>Todos os métodos</option>
                <option>PIX</option>
                <option>Cartão de Crédito</option>
                <option>Cartão de Débito</option>
                <option>Boleto</option>
              </select>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">ID</th>
                  <th className="text-left py-2">Cliente</th>
                  <th className="text-left py-2">Organização</th>
                  <th className="text-left py-2">Valor</th>
                  <th className="text-left py-2">Método</th>
                  <th className="text-left py-2">Status</th>
                  <th className="text-left py-2">Data</th>
                  <th className="text-left py-2">Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3">
                    <span className="font-mono text-sm">#TXN-001247</span>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">João Silva</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">TechCorp</p>
                      <p className="text-sm text-muted-foreground">techcorp</p>
                    </div>
                  </td>
                  <td className="py-3">
                    <span className="font-semibold">R$ 299,90</span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      PIX
                    </span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      Aprovada
                    </span>
                  </td>
                  <td className="py-3 text-sm text-muted-foreground">há 5 min</td>
                  <td className="py-3">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">
                      Ver Detalhes
                    </button>
                  </td>
                </tr>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3">
                    <span className="font-mono text-sm">#TXN-001246</span>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">Maria Santos</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">EcoStore</p>
                      <p className="text-sm text-muted-foreground">ecostore</p>
                    </div>
                  </td>
                  <td className="py-3">
                    <span className="font-semibold">R$ 89,90</span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      Cartão
                    </span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                      Pendente
                    </span>
                  </td>
                  <td className="py-3 text-sm text-muted-foreground">há 12 min</td>
                  <td className="py-3">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">
                      Ver Detalhes
                    </button>
                  </td>
                </tr>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3">
                    <span className="font-mono text-sm">#TXN-001245</span>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">Pedro Costa</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </td>
                  <td className="py-3">
                    <div>
                      <p className="font-medium">FitTech</p>
                      <p className="text-sm text-muted-foreground">fittech</p>
                    </div>
                  </td>
                  <td className="py-3">
                    <span className="font-semibold">R$ 149,90</span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                      Boleto
                    </span>
                  </td>
                  <td className="py-3">
                    <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                      Rejeitada
                    </span>
                  </td>
                  <td className="py-3 text-sm text-muted-foreground">há 25 min</td>
                  <td className="py-3">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">
                      Ver Detalhes
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}