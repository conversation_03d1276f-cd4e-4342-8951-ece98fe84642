import { getOrganizationList, getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { UsersIcon, BuildingIcon, CreditCardIcon, ActivityIcon, TrendingUpIcon, DollarSignIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { countAllUsers } from "@repo/database";
import { db } from "@repo/database/prisma/client";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function AdminPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const [organizations, totalUsers, totalTransactions, monthlyRevenue] = await Promise.all([
		getOrganizationList(),
		countAllUsers(),
		db.transaction.count(),
		db.transaction.aggregate({
			where: {
				createdAt: {
					gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
				},
				status: "COMPLETED",
			},
			_sum: { amountCents: true },
		}),
	]);

	const stats = [
		{
			title: "Organizações",
			value: organizations.length,
			description: "Total de organizações",
			icon: BuildingIcon,
			trend: "+12%",
			trendUp: true,
		},
		{
			title: "Usuários",
			value: totalUsers,
			description: "Total de usuários",
			icon: UsersIcon,
			trend: "+8%",
			trendUp: true,
		},
		{
			title: "Transações",
			value: totalTransactions,
			description: "Total de transações",
			icon: CreditCardIcon,
			trend: "+23%",
			trendUp: true,
		},
		{
			title: "Receita Mensal",
			value: `R$ ${((monthlyRevenue._sum?.amountCents || 0) / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
			description: "Receita do mês atual",
			icon: DollarSignIcon,
			trend: "+15%",
			trendUp: true,
		},
	];

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Painel Administrativo</h1>
				<p className="text-muted-foreground">
					Gerencie seu sistema e monitore as atividades.
				</p>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{stats.map((stat) => (
					<Card key={stat.title}>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								{stat.title}
							</CardTitle>
							<stat.icon className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stat.value}</div>
							<div className="flex items-center justify-between">
								<p className="text-xs text-muted-foreground">
									{stat.description}
								</p>
								<div className={`flex items-center text-xs ${
									stat.trendUp ? 'text-green-600' : 'text-red-600'
								}`}>
									<TrendingUpIcon className={`h-3 w-3 mr-1 ${
										!stat.trendUp ? 'rotate-180' : ''
									}`} />
									{stat.trend}
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader>
						<CardTitle>Organizações Recentes</CardTitle>
						<CardDescription>
							Últimas organizações criadas no sistema.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							{organizations.slice(0, 5).map((org) => (
								<div
									key={org.id}
									className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
								>
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
											<BuildingIcon className="h-4 w-4 text-primary" />
										</div>
										<div>
											<p className="font-medium text-sm">{org.name}</p>
											<p className="text-xs text-muted-foreground">
												@{org.slug}
											</p>
										</div>
									</div>
									<span className="text-xs text-muted-foreground">
										{new Date(org.createdAt).toLocaleDateString('pt-BR')}
									</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Status do Sistema</CardTitle>
						<CardDescription>
							Monitoramento em tempo real.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between p-3 rounded-lg border">
								<div className="flex items-center gap-2">
									<div className="w-2 h-2 rounded-full bg-green-500"></div>
									<span className="text-sm font-medium">Sistema Online</span>
								</div>
								<span className="text-xs text-green-600 font-medium">100%</span>
							</div>
							<div className="flex items-center justify-between p-3 rounded-lg border">
								<div className="flex items-center gap-2">
									<ActivityIcon className="h-4 w-4 text-blue-500" />
									<span className="text-sm font-medium">Usuários Ativos</span>
								</div>
								<span className="text-xs text-muted-foreground">{Math.floor(totalUsers * 0.15)}</span>
							</div>
							<div className="flex items-center justify-between p-3 rounded-lg border">
								<span className="text-sm font-medium">Última Atualização</span>
								<span className="text-xs text-muted-foreground">
									{new Date().toLocaleString('pt-BR')}
								</span>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Atividade Recente</CardTitle>
						<CardDescription>
							Últimas ações no sistema.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center gap-3 p-2">
								<div className="w-2 h-2 rounded-full bg-green-500"></div>
								<div className="flex-1">
									<p className="text-sm font-medium">Nova organização criada</p>
									<p className="text-xs text-muted-foreground">há 2 minutos</p>
								</div>
							</div>
							<div className="flex items-center gap-3 p-2">
								<div className="w-2 h-2 rounded-full bg-blue-500"></div>
								<div className="flex-1">
									<p className="text-sm font-medium">Usuário registrado</p>
									<p className="text-xs text-muted-foreground">há 5 minutos</p>
								</div>
							</div>
							<div className="flex items-center gap-3 p-2">
								<div className="w-2 h-2 rounded-full bg-yellow-500"></div>
								<div className="flex-1">
									<p className="text-sm font-medium">Transação processada</p>
									<p className="text-xs text-muted-foreground">há 8 minutos</p>
								</div>
							</div>
							<div className="flex items-center gap-3 p-2">
								<div className="w-2 h-2 rounded-full bg-purple-500"></div>
								<div className="flex-1">
									<p className="text-sm font-medium">Configuração atualizada</p>
									<p className="text-xs text-muted-foreground">há 12 minutos</p>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
