import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  WebhookIcon,
  BotIcon,
  ZapIcon,
  GlobeIcon,
  MailIcon,
  DatabaseIcon,
  SettingsIcon,
  ActivityIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
  PlusIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function AdminIntegrationsPage() {
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  if (!isAdmin(session.user)) {
    redirect("/app");
  }

  const integracoes = [
    {
      id: 'webhooks',
      nome: 'Webhooks Globais',
      descricao: '<PERSON><PERSON><PERSON><PERSON> webhooks para eventos do sistema',
      icone: WebhookIcon,
      status: 'ativo',
      configuracoes: 15,
      ultimaExecucao: '2 min atrás',
      taxaSucesso: 98.5
    },
    {
      id: 'n8n',
      nome: 'n8n Workflows',
      descricao: 'Automações e workflows visuais',
      icone: BotIcon,
      status: 'ativo',
      configuracoes: 8,
      ultimaExecucao: '5 min atrás',
      taxaSucesso: 95.2
    },
    {
      id: 'zapier',
      nome: 'Zapier Integration',
      descricao: 'Conecte com milhares de aplicações',
      icone: ZapIcon,
      status: 'inativo',
      configuracoes: 0,
      ultimaExecucao: 'Nunca',
      taxaSucesso: 0
    },
    {
      id: 'api',
      nome: 'API Management',
      descricao: 'Gerencie chaves e acessos da API',
      icone: GlobeIcon,
      status: 'ativo',
      configuracoes: 25,
      ultimaExecucao: '1 min atrás',
      taxaSucesso: 99.1
    },
    {
      id: 'email',
      nome: 'Email Providers',
      descricao: 'Configurações de provedores de email',
      icone: MailIcon,
      status: 'ativo',
      configuracoes: 3,
      ultimaExecucao: '10 min atrás',
      taxaSucesso: 97.8
    },
    {
      id: 'database',
      nome: 'Database Sync',
      descricao: 'Sincronização com bancos externos',
      icone: DatabaseIcon,
      status: 'manutencao',
      configuracoes: 2,
      ultimaExecucao: '1 hora atrás',
      taxaSucesso: 85.0
    }
  ];

  const estatisticas = [
    {
      titulo: 'Total de Integrações',
      valor: integracoes.length,
      descricao: 'Integrações disponíveis',
      icone: WebhookIcon
    },
    {
      titulo: 'Integrações Ativas',
      valor: integracoes.filter(i => i.status === 'ativo').length,
      descricao: 'Funcionando normalmente',
      icone: CheckCircleIcon
    },
    {
      titulo: 'Configurações',
      valor: integracoes.reduce((acc, i) => acc + i.configuracoes, 0),
      descricao: 'Total de configurações',
      icone: SettingsIcon
    },
    {
      titulo: 'Taxa de Sucesso Média',
      valor: `${(integracoes.reduce((acc, i) => acc + i.taxaSucesso, 0) / integracoes.length).toFixed(1)}%`,
      descricao: 'Média de sucesso',
      icone: ActivityIcon
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ativo':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'inativo':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'manutencao':
        return <AlertCircleIcon className="h-4 w-4 text-yellow-500" />;
      default:
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ativo':
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Ativo</Badge>;
      case 'inativo':
        return <Badge className="bg-red-100 text-red-800 border border-red-200">Inativo</Badge>;
      case 'manutencao':
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Manutenção</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Desconhecido</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Integrações</h1>
          <p className="text-muted-foreground">
            Gerencie todas as integrações e automações do sistema
          </p>
        </div>
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Nova Integração
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {estatisticas.map((stat) => {
          const IconComponent = stat.icone;
          return (
            <Card key={stat.titulo}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.titulo}
                </CardTitle>
                <IconComponent className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.valor}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.descricao}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {integracoes.map((integracao) => {
          const IconeComponent = integracao.icone;
          return (
            <Card key={integracao.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconeComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{integracao.nome}</CardTitle>
                      <CardDescription>{integracao.descricao}</CardDescription>
                    </div>
                  </div>
                  {getStatusIcon(integracao.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Status</span>
                    {getStatusBadge(integracao.status)}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Configurações</p>
                      <p className="font-medium">{integracao.configuracoes}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Taxa de Sucesso</p>
                      <p className="font-medium">{integracao.taxaSucesso}%</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">Última Execução</p>
                    <p className="text-sm font-medium">{integracao.ultimaExecucao}</p>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <SettingsIcon className="h-4 w-4 mr-2" />
                      Configurar
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <ActivityIcon className="h-4 w-4 mr-2" />
                      Logs
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Eventos Recentes</CardTitle>
            <CardDescription>Últimas execuções de integrações</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  integracao: 'Webhook - Novo Usuário',
                  status: 'sucesso',
                  tempo: '2 min atrás',
                  organizacao: 'Empresa ABC'
                },
                {
                  integracao: 'n8n - Sync Dados',
                  status: 'sucesso',
                  tempo: '5 min atrás',
                  organizacao: 'Tech Corp'
                },
                {
                  integracao: 'API - Consulta Externa',
                  status: 'erro',
                  tempo: '8 min atrás',
                  organizacao: 'StartupXYZ'
                },
                {
                  integracao: 'Email - Notificação',
                  status: 'sucesso',
                  tempo: '12 min atrás',
                  organizacao: 'Global Inc'
                }
              ].map((evento, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {evento.status === 'sucesso' ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircleIcon className="h-4 w-4 text-red-500" />
                    )}
                    <div>
                      <p className="font-medium text-sm">{evento.integracao}</p>
                      <p className="text-xs text-muted-foreground">{evento.organizacao}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">{evento.tempo}</p>
                    <Badge className={`text-xs ${
                      evento.status === 'sucesso'
                        ? 'bg-green-100 text-green-800 border border-green-200'
                        : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {evento.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configurações Globais</CardTitle>
            <CardDescription>Configurações que afetam todas as integrações</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Rate Limiting</p>
                  <p className="text-sm text-muted-foreground">Limite de requisições por minuto</p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                  1000/min
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Timeout Global</p>
                  <p className="text-sm text-muted-foreground">Tempo limite para execuções</p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                  30s
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Retry Policy</p>
                  <p className="text-sm text-muted-foreground">Tentativas em caso de falha</p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                  3x
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Logs Retention</p>
                  <p className="text-sm text-muted-foreground">Tempo de retenção dos logs</p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                  30 dias
                </Badge>
              </div>

              <Button variant="outline" className="w-full">
                <SettingsIcon className="h-4 w-4 mr-2" />
                Editar Configurações
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
