"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import { 
  ArrowLeftIcon,
  CopyIcon,
  DownloadIcon,
  RefreshCwIcon,
  MoreHorizontalIcon,
  CreditCardIcon,
  UserIcon,
  PackageIcon,
  CalendarIcon,
  DollarSignIcon
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Sale } from "@/modules/saas/sales/components/SalesTable";

interface ClientPageProps {
  organization: {
    id: string;
    slug: string;
    name: string;
    createdAt: Date;
  };
  saleId: string;
}

// Mock function to get sale by ID - in real implementation, this would be an API call
const getSaleById = async (saleId: string): Promise<Sale | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock sale data
  return {
    id: saleId,
    transactionId: `TXN-${saleId.toUpperCase()}`,
    customer: {
      id: "customer-1",
      name: "João Silva",
      email: "<EMAIL>",
      avatar: undefined,
    },
    product: {
      id: "product-1",
      name: "Produto Premium",
      isArchived: false,
    },
    amount: 29900, // R$ 299.00
    currency: "BRL",
    netAmount: 28405, // R$ 284.05 (after fees)
    status: "approved",
    paymentMethod: "pix",
    createdAt: new Date("2024-01-15T10:30:00"),
    paidAt: new Date("2024-01-15T10:32:00"),
    metadata: {
      customerDocument: "123.456.789-00",
      customerPhone: "+55 11 99999-9999",
      paymentId: "pix-payment-123",
      gateway: "mercadopago",
    },
  };
};

const statusConfig = {
  pending: { label: "Pendente", status: "warning" as const },
  approved: { label: "Aprovado", status: "success" as const },
  failed: { label: "Falhou", status: "error" as const },
  refunded: { label: "Reembolsado", status: "info" as const },
  cancelled: { label: "Cancelado", status: "error" as const },
};

const paymentMethodConfig = {
  pix: { label: "PIX", icon: DollarSignIcon },
  card: { label: "Cartão de Crédito", icon: CreditCardIcon },
  boleto: { label: "Boleto Bancário", icon: CalendarIcon },
};

const ClientPage: React.FC<ClientPageProps> = ({
  organization,
  saleId,
}) => {
  const router = useRouter();
  const [sale, setSale] = useState<Sale | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSale = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const saleData = await getSaleById(saleId);
        
        if (!saleData) {
          setError("Venda não encontrada");
          return;
        }
        
        setSale(saleData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro ao carregar venda");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSale();
  }, [saleId]);

  const formatCurrency = (amount: number, currency: string = "BRL") => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: currency,
    }).format(amount / 100);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // In a real app, you'd show a toast notification here
  };

  const handleBack = () => {
    router.push(`/app/${organization.slug}/sales`);
  };

  const handleRefund = () => {
    // Implement refund functionality
    console.log("Refund sale:", saleId);
  };

  const handleDownloadReceipt = () => {
    // Implement receipt download
    console.log("Download receipt for sale:", saleId);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Voltar
          </Button>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="h-48 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !sale) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Voltar
          </Button>
        </div>
        <Card>
          <CardContent className="py-16 text-center">
            <h3 className="text-lg font-medium mb-2">Venda não encontrada</h3>
            <p className="text-muted-foreground">
              {error || "A venda solicitada não foi encontrada."}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statusInfo = statusConfig[sale.status];
  const paymentInfo = paymentMethodConfig[sale.paymentMethod];
  const PaymentIcon = paymentInfo.icon;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Venda #{sale.id}</h1>
            <p className="text-sm text-muted-foreground">
              {format(sale.createdAt, "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleDownloadReceipt}>
            <DownloadIcon className="h-4 w-4 mr-2" />
            Recibo
          </Button>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          {sale.status === "approved" && (
            <Button variant="outline" size="sm" onClick={handleRefund}>
              Reembolsar
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Sale Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Sale Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Detalhes da Venda
                <Badge status={statusInfo.status}>
                  {statusInfo.label}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">ID da Transação</label>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="font-mono text-sm">{sale.transactionId}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => copyToClipboard(sale.transactionId)}
                    >
                      <CopyIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Método de Pagamento</label>
                  <div className="flex items-center gap-2 mt-1">
                    <PaymentIcon className="h-4 w-4" />
                    <span className="text-sm">{paymentInfo.label}</span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Valor Bruto</label>
                  <p className="text-lg font-semibold">{formatCurrency(sale.amount, sale.currency)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Valor Líquido</label>
                  <p className="text-lg font-semibold text-green-600">
                    {formatCurrency(sale.netAmount, sale.currency)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PackageIcon className="h-5 w-5" />
                Produto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">{sale.product.name}</h3>
                  <p className="text-sm text-muted-foreground">ID: {sale.product.id}</p>
                </div>
                {sale.product.isArchived && (
                  <Badge status="error">Arquivado</Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customer Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                Cliente
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={sale.customer.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {sale.customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{sale.customer.name}</h3>
                  <p className="text-sm text-muted-foreground">{sale.customer.email}</p>
                </div>
              </div>
              
              {sale.metadata && (
                <div className="space-y-2 pt-4 border-t">
                  {sale.metadata.customerDocument && (
                    <div>
                      <label className="text-xs font-medium text-muted-foreground">CPF/CNPJ</label>
                      <p className="text-sm font-mono">{sale.metadata.customerDocument}</p>
                    </div>
                  )}
                  {sale.metadata.customerPhone && (
                    <div>
                      <label className="text-xs font-medium text-muted-foreground">Telefone</label>
                      <p className="text-sm">{sale.metadata.customerPhone}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Venda criada</p>
                  <p className="text-xs text-muted-foreground">
                    {format(sale.createdAt, "dd/MM/yyyy HH:mm", { locale: ptBR })}
                  </p>
                </div>
              </div>
              
              {sale.paidAt && (
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium">Pagamento confirmado</p>
                    <p className="text-xs text-muted-foreground">
                      {format(sale.paidAt, "dd/MM/yyyy HH:mm", { locale: ptBR })}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ClientPage;
