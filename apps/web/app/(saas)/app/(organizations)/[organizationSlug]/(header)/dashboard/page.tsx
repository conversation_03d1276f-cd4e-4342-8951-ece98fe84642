import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components";
import { Badge } from "@ui/components";
import { Progress } from "@ui/components";
import { Button } from "@ui/components";
import {
  DollarSign,
  CreditCard,
  Zap,
  Wallet,
  TrendingUp,
  Users,
  ShoppingCart,
  Calendar,
  Search,
  Eye,
  ExternalLink,
  Mountain,
  Flag,
  Receipt,
  BarChart3,
  Clock
} from "lucide-react";

export default async function DashboardPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Mock data - in real app this would come from API
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString('pt-BR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  const revenueData = [
    { date: "29 jul", value: 0 },
    { date: "30 jul", value: 0 },
    { date: "31 jul", value: 0 },
    { date: "1 ago", value: 0 },
    { date: "2 ago", value: 0 },
    { date: "3 ago", value: 0 },
    { date: "4 ago", value: 0 },
    { date: "5 ago", value: 0 },
    { date: "6 ago", value: 0 },
    { date: "7 ago", value: 0 },
    { date: "8 ago", value: 0 },
    { date: "9 ago", value: 0 },
    { date: "10 ago", value: 0 },
    { date: "11 ago", value: 0 },
    { date: "12 ago", value: 0 },
    { date: "13 ago", value: 0 },
    { date: "14 ago", value: 0 },
    { date: "15 ago", value: 0 },
    { date: "16 ago", value: 0 },
    { date: "17 ago", value: 0 },
    { date: "18 ago", value: 0 },
    { date: "19 ago", value: 0 },
    { date: "20 ago", value: 0 },
    { date: "21 ago", value: 0 },
    { date: "22 ago", value: 0 },
    { date: "23 ago", value: 0 },
    { date: "24 ago", value: 0 },
    { date: "25 ago", value: 0 },
    { date: "26 ago", value: 0 },
    { date: "27 ago", value: 0 },
    { date: "28 ago", value: 1500 }, // Spike at the end
  ];

  const paymentMethods = [
    { name: "Cartão de crédito", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "PIX", icon: Zap, percentage: 0, count: "0/0" },
    { name: "Boleto", icon: Receipt, percentage: 0, count: "0/0" },
    { name: "PicPay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Apple Pay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Google Pay", icon: CreditCard, percentage: 0, count: "0/0" },
    { name: "Samsung Pay", icon: CreditCard, percentage: 0, count: "0/0" },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Top Bar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>7/29/2025 - 8/28/2025</span>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="text"
              placeholder="Buscar produto..."
              className="w-64 pl-10 pr-4 py-2 rounded-lg border bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Welcome Section */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Hoje é {formattedDate}</CardTitle>
            <CardDescription>Olá, {organization.name} 👋</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Pequenas ações geram grandes resultados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Saldo disponível
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <p className="text-2xl font-bold">R$ 0,00</p>
              <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                <Receipt className="h-4 w-4 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Saldo pendente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <p className="text-2xl font-bold">R$ 0,00</p>
              <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                <Clock className="h-4 w-4 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Account Health Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Saúde da Conta</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Em breve, você poderá acompanhar a saúde da sua conta
            </p>
            <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
              <BarChart3 className="h-4 w-4 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Chart and Achievements */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">R$ 0,00 Receita líquida</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48 flex items-end justify-between gap-1">
              {revenueData.map((item, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-primary/20 rounded-t-sm transition-all duration-300"
                    style={{
                      height: `${Math.max(item.value / 20, 2)}px`,
                      minHeight: '2px'
                    }}
                  />
                  <span className="text-xs text-muted-foreground mt-1">
                    {item.date}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Jornada de conquistas</CardTitle>
              <Button variant="ghost" size="sm" className="text-primary">
                Saiba mais <ExternalLink className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Sua jornada começa com a primeira venda. Vamos nessa?
            </p>

            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <Mountain className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">1ª venda Explorador</p>
                  <p className="text-xs text-muted-foreground">Primeiro passo da jornada</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <Flag className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">10k Avançado</p>
                  <p className="text-xs text-muted-foreground">Meta intermediária</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                  <DollarSign className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">100k Expert</p>
                  <p className="text-xs text-muted-foreground">Nível máximo</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Grid */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Métodos de Pagamento</h3>
        <div className="grid gap-4 md:grid-cols-4">
          {paymentMethods.slice(0, 4).map((method, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                    <method.icon className="h-4 w-4 text-primary" />
                  </div>
                  <span className="font-medium text-sm">{method.name}</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{method.percentage}%</span>
                    <span className="font-medium">{method.count}</span>
                  </div>
                  <Progress value={method.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          {paymentMethods.slice(4).map((method, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-8 w-8 rounded-lg bg-primary/10 p-2">
                    <method.icon className="h-4 w-4 text-primary" />
                  </div>
                  <span className="font-medium text-sm">{method.name}</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{method.percentage}%</span>
                    <span className="font-medium">{method.count}</span>
                  </div>
                  <Progress value={method.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
