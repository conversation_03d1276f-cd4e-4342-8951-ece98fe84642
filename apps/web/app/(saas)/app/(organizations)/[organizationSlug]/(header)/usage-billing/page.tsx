import { getActiveOrganization } from "@saas/auth/lib/server";
import { PlanDetails, UsageOverview, BillingHistory } from "@saas/billing/components";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function UsageBillingPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Uso e Cobrança"
        subtitle="Acompanhe seu uso e histórico de cobrança"
      />

      <PlanDetails organizationId={organization.id} />
      <UsageOverview organizationId={organization.id} />
      <BillingHistory organizationId={organization.id} />
    </div>
  );
}
