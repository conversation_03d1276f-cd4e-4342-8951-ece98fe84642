"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import { PaletteIcon, ImageIcon, GlobeIcon, EyeOffIcon, SaveIcon, CheckIcon } from "lucide-react";
import { toast } from "sonner";
import { BrandingConfig, DomainConfig } from "@/modules/branding/lib/branding";

interface BrandingFormProps {
  organization: {
    id: string;
    name: string;
    customDomain?: string | null;
    enableCustomBranding: boolean;
    enableCustomDomain: boolean;
  };
  brandingConfig: BrandingConfig | null;
  domainConfig: DomainConfig | null;
}

export function BrandingForm({ organization, brandingConfig, domainConfig }: BrandingFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [config, setConfig] = useState<BrandingConfig>({
    primaryColor: brandingConfig?.primaryColor || "#3b82f6",
    secondaryColor: brandingConfig?.secondaryColor || "#64748b",
    accentColor: brandingConfig?.accentColor || "#10b981",
    backgroundColor: brandingConfig?.backgroundColor || "#ffffff",
    textColor: brandingConfig?.textColor || "#000000",
    logoUrl: brandingConfig?.logoUrl || "",
    faviconUrl: brandingConfig?.faviconUrl || "",
    companyName: brandingConfig?.companyName || organization.name,
    tagline: brandingConfig?.tagline || "",
    description: brandingConfig?.description || "",
    darkMode: brandingConfig?.darkMode || false,
    hideSystemBranding: brandingConfig?.hideSystemBranding || false,
  });

  const [domain, setDomain] = useState<{
    customDomain: string;
    sslEnabled: boolean;
    redirectWww: boolean;
  }>({
    customDomain: domainConfig?.customDomain || "",
    sslEnabled: domainConfig?.sslEnabled ?? true,
    redirectWww: domainConfig?.redirectWww ?? false,
  });

  const handleSaveBranding = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/organizations/${organization.id}/branding`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(config),
      });

      if (!response.ok) throw new Error("Erro ao salvar configurações");

      toast.success("Configurações de marca salvas com sucesso!");
    } catch (error) {
      toast.error("Erro ao salvar configurações de marca");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveDomain = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/organizations/${organization.id}/domain`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(domain),
      });

      if (!response.ok) throw new Error("Erro ao salvar domínio");

      toast.success("Configurações de domínio salvas com sucesso!");
    } catch (error) {
      toast.error("Erro ao salvar configurações de domínio");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PaletteIcon className="h-5 w-5" />
            Cores da Marca
          </CardTitle>
          <CardDescription>
            Defina as cores principais da sua marca
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primary-color">Cor Primária</Label>
              <div className="flex gap-2">
                <Input
                  id="primary-color"
                  type="color"
                  value={config.primaryColor}
                  onChange={(e) => setConfig({ ...config, primaryColor: e.target.value })}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.primaryColor}
                  onChange={(e) => setConfig({ ...config, primaryColor: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="secondary-color">Cor Secundária</Label>
              <div className="flex gap-2">
                <Input
                  id="secondary-color"
                  type="color"
                  value={config.secondaryColor}
                  onChange={(e) => setConfig({ ...config, secondaryColor: e.target.value })}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.secondaryColor}
                  onChange={(e) => setConfig({ ...config, secondaryColor: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="accent-color">Cor de Destaque</Label>
              <div className="flex gap-2">
                <Input
                  id="accent-color"
                  type="color"
                  value={config.accentColor}
                  onChange={(e) => setConfig({ ...config, accentColor: e.target.value })}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.accentColor}
                  onChange={(e) => setConfig({ ...config, accentColor: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="background-color">Cor de Fundo</Label>
              <div className="flex gap-2">
                <Input
                  id="background-color"
                  type="color"
                  value={config.backgroundColor}
                  onChange={(e) => setConfig({ ...config, backgroundColor: e.target.value })}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.backgroundColor}
                  onChange={(e) => setConfig({ ...config, backgroundColor: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
          <Button onClick={handleSaveBranding} disabled={isLoading}>
            <SaveIcon className="h-4 w-4 mr-2" />
            Salvar Cores
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Logo e Imagens
          </CardTitle>
          <CardDescription>
            Configure o logo e ícones da sua marca
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="logo-url">URL do Logo Principal</Label>
            <Input
              id="logo-url"
              placeholder="https://exemplo.com/logo.png"
              type="url"
              value={config.logoUrl}
              onChange={(e) => setConfig({ ...config, logoUrl: e.target.value })}
            />
            <p className="text-sm text-muted-foreground">
              Recomendado: PNG ou SVG, máximo 200x60px
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="favicon-url">URL do Favicon</Label>
            <Input
              id="favicon-url"
              placeholder="https://exemplo.com/favicon.ico"
              type="url"
              value={config.faviconUrl}
              onChange={(e) => setConfig({ ...config, faviconUrl: e.target.value })}
            />
            <p className="text-sm text-muted-foreground">
              Recomendado: ICO ou PNG, 32x32px
            </p>
          </div>
          <Button onClick={handleSaveBranding} disabled={isLoading}>
            <SaveIcon className="h-4 w-4 mr-2" />
            Salvar Imagens
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GlobeIcon className="h-5 w-5" />
            Domínio Personalizado
            {!organization.enableCustomDomain && (
              <Badge status="warning">Plano Pro</Badge>
            )}
          </CardTitle>
          <CardDescription>
            Configure um domínio personalizado para sua organização
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="custom-domain">Domínio Personalizado</Label>
            <Input
              id="custom-domain"
              placeholder="meudominio.com"
              type="text"
              value={domain.customDomain}
              onChange={(e) => setDomain({ ...domain, customDomain: e.target.value })}
              disabled={!organization.enableCustomDomain}
            />
            <p className="text-sm text-muted-foreground">
              Após configurar, você precisará apontar seu domínio para nossos servidores
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>SSL Automático</Label>
              <p className="text-sm text-muted-foreground">
                Certificado SSL gratuito via Let's Encrypt
              </p>
            </div>
            <Switch
              checked={domain.sslEnabled}
              onCheckedChange={(checked) => setDomain({ ...domain, sslEnabled: checked })}
              disabled={!organization.enableCustomDomain}
            />
          </div>
          {domain.customDomain && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900">Configuração DNS</h4>
              <p className="text-sm text-blue-700 mt-1">
                Adicione um registro CNAME apontando para:
                <code className="bg-blue-100 px-1 rounded ml-1">app.SupGateway.com</code>
              </p>
            </div>
          )}
          <Button
            onClick={handleSaveDomain}
            disabled={isLoading || !organization.enableCustomDomain}
          >
            <SaveIcon className="h-4 w-4 mr-2" />
            Salvar Domínio
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <EyeOffIcon className="h-5 w-5" />
            Personalização Avançada
            {!organization.enableCustomBranding && (
              <Badge status="warning">Plano Pro</Badge>
            )}
          </CardTitle>
          <CardDescription>
            Configurações avançadas de personalização
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="company-name">Nome da Empresa</Label>
            <Input
              id="company-name"
              placeholder="Minha Empresa"
              value={config.companyName}
              onChange={(e) => setConfig({ ...config, companyName: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="tagline">Slogan</Label>
            <Input
              id="tagline"
              placeholder="Transformando ideias em realidade"
              value={config.tagline}
              onChange={(e) => setConfig({ ...config, tagline: e.target.value })}
              disabled={!organization.enableCustomBranding}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Descrição da sua empresa..."
              rows={3}
              value={config.description}
              onChange={(e) => setConfig({ ...config, description: e.target.value })}
              disabled={!organization.enableCustomBranding}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Modo Escuro</Label>
              <p className="text-sm text-muted-foreground">
                Ativar tema escuro por padrão
              </p>
            </div>
            <Switch
              checked={config.darkMode}
              onCheckedChange={(checked) => setConfig({ ...config, darkMode: checked })}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Ocultar Marca do Sistema</Label>
              <p className="text-sm text-muted-foreground">
                Remove referências ao sistema original
              </p>
            </div>
            <Switch
              checked={config.hideSystemBranding}
              onCheckedChange={(checked) => setConfig({ ...config, hideSystemBranding: checked })}
              disabled={!organization.enableCustomBranding}
            />
          </div>
          <Button onClick={handleSaveBranding} disabled={isLoading}>
            <SaveIcon className="h-4 w-4 mr-2" />
            Salvar Configurações
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
