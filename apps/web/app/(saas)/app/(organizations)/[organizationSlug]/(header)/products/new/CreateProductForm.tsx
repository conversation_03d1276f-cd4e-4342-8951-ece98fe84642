"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { useProducts } from "@saas/products/hooks/useProducts";
import { CreateProductData } from "@saas/products/hooks/useProducts";
import { ProductTypeSchema } from "@repo/database";

const createProductSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  slug: z.string().min(1, "Slug é obrigatório").regex(/^[a-z0-9-]+$/, "Slug deve conter apenas letras minúsculas, números e hífens"),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  priceCents: z.number().min(0, "Preço deve ser maior ou igual a 0"),
  comparePriceCents: z.number().min(0).optional(),
  currency: z.string().default("BRL"),
  type: ProductTypeSchema,
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
  visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  duration: z.number().min(0).optional(),
  level: z.string().optional(),
  language: z.string().default("pt-BR"),
  certificate: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  gallery: z.array(z.string()).default([]),
  settings: z.record(z.any()).default({}),
});

type CreateProductFormData = CreateProductData;

interface CreateProductFormProps {
  organization: any;
}

export function CreateProductForm({ organization }: CreateProductFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [features, setFeatures] = useState<string[]>([]);
  const [requirements, setRequirements] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [newFeature, setNewFeature] = useState("");
  const [newRequirement, setNewRequirement] = useState("");

  const { createProduct } = useProducts(organization.id);

  const form = useForm<CreateProductFormData>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      currency: "BRL",
      status: "DRAFT",
      visibility: "PRIVATE",
      type: "COURSE",
      language: "pt-BR",
      certificate: false,
      downloadable: false,
      checkoutType: "DEFAULT",
    },
  });

  const onSubmit = async (data: CreateProductFormData) => {
    try {
      setLoading(true);

      const productData: CreateProductData = {
        ...data,
        tags,
        features,
        requirements,
        settings: {},
      };

      const product = await createProduct(productData);

      if (product) {
        toast.success("Produto criado com sucesso!");
        router.push(`/app/${organization.slug}/products/${product.id}`);
      }
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Erro ao criar produto");
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setFeatures(features.filter(feature => feature !== featureToRemove));
  };

  const addRequirement = () => {
    if (newRequirement.trim() && !requirements.includes(newRequirement.trim())) {
      setRequirements([...requirements, newRequirement.trim()]);
      setNewRequirement("");
    }
  };

  const removeRequirement = (requirementToRemove: string) => {
    setRequirements(requirements.filter(requirement => requirement !== requirementToRemove));
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle>Informações Básicas</CardTitle>
            <CardDescription>
              Informações principais do produto
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome do Produto *</Label>
              <Input
                id="name"
                placeholder="Ex: Curso de Marketing Digital"
                {...form.register("name")}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                placeholder="Ex: curso-marketing-digital"
                {...form.register("slug")}
              />
              {form.formState.errors.slug && (
                <p className="text-sm text-red-600">{form.formState.errors.slug.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo de Produto *</Label>
              <Select onValueChange={(value) => form.setValue("type", value as any)} defaultValue={form.getValues("type")}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COURSE">Curso</SelectItem>
                  <SelectItem value="EBOOK">E-book</SelectItem>
                  <SelectItem value="MENTORING">Mentoria</SelectItem>
                  <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
                  <SelectItem value="BUNDLE">Pacote</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="shortDescription">Descrição Curta</Label>
              <Textarea
                id="shortDescription"
                placeholder="Descrição breve do produto"
                {...form.register("shortDescription")}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição Completa</Label>
              <Textarea
                id="description"
                placeholder="Descrição detalhada do produto"
                {...form.register("description")}
                rows={5}
              />
            </div>
          </CardContent>
        </Card>

        {/* Preços e Configurações */}
        <Card>
          <CardHeader>
            <CardTitle>Preços e Configurações</CardTitle>
            <CardDescription>
              Configure preços e configurações de venda
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="priceCents">Preço (em centavos) *</Label>
              <Input
                id="priceCents"
                type="number"
                placeholder="Ex: 29700 (R$ 297,00)"
                {...form.register("priceCents", { valueAsNumber: true })}
              />
              {form.formState.errors.priceCents && (
                <p className="text-sm text-red-600">{form.formState.errors.priceCents.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="comparePriceCents">Preço de Comparação (em centavos)</Label>
              <Input
                id="comparePriceCents"
                type="number"
                placeholder="Ex: 39700 (R$ 397,00)"
                {...form.register("comparePriceCents", { valueAsNumber: true })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moeda</Label>
              <Select onValueChange={(value) => form.setValue("currency", value)} defaultValue={form.getValues("currency")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BRL">Real (BRL)</SelectItem>
                  <SelectItem value="USD">Dólar (USD)</SelectItem>
                  <SelectItem value="EUR">Euro (EUR)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value) => form.setValue("status", value as any)} defaultValue={form.getValues("status")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">Rascunho</SelectItem>
                  <SelectItem value="PUBLISHED">Publicado</SelectItem>
                  <SelectItem value="ARCHIVED">Arquivado</SelectItem>
                  <SelectItem value="SUSPENDED">Suspenso</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="visibility">Visibilidade</Label>
              <Select onValueChange={(value) => form.setValue("visibility", value as any)} defaultValue={form.getValues("visibility")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PUBLIC">Público</SelectItem>
                  <SelectItem value="PRIVATE">Privado</SelectItem>
                  <SelectItem value="UNLISTED">Não Listado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configurações Avançadas */}
      <Card>
        <CardHeader>
          <CardTitle>Configurações Avançadas</CardTitle>
          <CardDescription>
            Configurações específicas do tipo de produto
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="duration">Duração (em minutos)</Label>
              <Input
                id="duration"
                type="number"
                placeholder="Ex: 180 (3 horas)"
                {...form.register("duration", { valueAsNumber: true })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="level">Nível</Label>
              <Select onValueChange={(value) => form.setValue("level", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o nível" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BEGINNER">Iniciante</SelectItem>
                  <SelectItem value="INTERMEDIATE">Intermediário</SelectItem>
                  <SelectItem value="ADVANCED">Avançado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Idioma</Label>
              <Select onValueChange={(value) => form.setValue("language", value)} defaultValue={form.getValues("language")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                  <SelectItem value="en-US">English (US)</SelectItem>
                  <SelectItem value="es-ES">Español</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="checkoutType">Tipo de Checkout</Label>
              <Select onValueChange={(value) => form.setValue("checkoutType", value as any)} defaultValue={form.getValues("checkoutType")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DEFAULT">Padrão</SelectItem>
                  <SelectItem value="CUSTOM">Personalizado</SelectItem>
                  <SelectItem value="EXTERNAL">Externo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="certificate"
              checked={form.watch("certificate")}
              onCheckedChange={(checked) => form.setValue("certificate", checked)}
            />
            <Label htmlFor="certificate">Oferece certificado</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="downloadable"
              checked={form.watch("downloadable")}
              onCheckedChange={(checked) => form.setValue("downloadable", checked)}
            />
            <Label htmlFor="downloadable">Produto baixável</Label>
          </div>
        </CardContent>
      </Card>

      {/* Tags e Características */}
      <Card>
        <CardHeader>
          <CardTitle>Tags e Características</CardTitle>
          <CardDescription>
            Adicione tags, características e requisitos ao produto
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                Adicionar
              </Button>
            </div>
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Características */}
          <div className="space-y-2">
            <Label>Características</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar característica"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addFeature())}
              />
              <Button type="button" onClick={addFeature} variant="outline">
                Adicionar
              </Button>
            </div>
            {features.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {features.map((feature, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full flex items-center gap-1"
                  >
                    {feature}
                    <button
                      type="button"
                      onClick={() => removeFeature(feature)}
                      className="text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Requisitos */}
          <div className="space-y-2">
            <Label>Requisitos</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar requisito"
                value={newRequirement}
                onChange={(e) => setNewRequirement(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addRequirement())}
              />
              <Button type="button" onClick={addRequirement} variant="outline">
                Adicionar
              </Button>
            </div>
            {requirements.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {requirements.map((requirement, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full flex items-center gap-1"
                  >
                    {requirement}
                    <button
                      type="button"
                      onClick={() => removeRequirement(requirement)}
                      className="text-orange-600 hover:text-orange-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Botões de Ação */}
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Criando..." : "Criar Produto"}
        </Button>
      </div>
    </form>
  );
}
