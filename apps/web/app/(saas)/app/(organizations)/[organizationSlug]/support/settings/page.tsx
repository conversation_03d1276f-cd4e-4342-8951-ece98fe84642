import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { ConversationsSettings } from "@/modules/saas/conversations/components/ConversationsSettings";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, SaveIcon, TestTubeIcon } from "lucide-react";
import { notFound } from "next/navigation";
import Link from "next/link";

export default async function ConversationsSettingsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Configurações de Conversas"
        subtitle="Configure canais, automações, equipes e notificações"
        actions={
          <>
            <Link href={`/app/${organizationSlug}/support`}>
              <Button variant="outline">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Voltar às Conversas
              </Button>
            </Link>
            <Button variant="outline">
              <TestTubeIcon className="h-4 w-4 mr-2" />
              Testar Configurações
            </Button>
            <Button>
              <SaveIcon className="h-4 w-4 mr-2" />
              Salvar Alterações
            </Button>
          </>
        }
      />

      {/* Settings Content */}
      <ConversationsSettings organizationId={organization.id} />
    </div>
  );
}
