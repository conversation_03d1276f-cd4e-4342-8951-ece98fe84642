#!/bin/bash

# Script de build local otimizado para evitar problemas de memória
echo "🚀 Iniciando build local otimizado..."

# Configurar variáveis de ambiente para otimizar memória
export NODE_OPTIONS="--max-old-space-size=8192"
export NEXT_TELEMETRY_DISABLED=1
export NEXT_IGNORE_TYPECHECK_DURING_BUILD=1

# Limpar cache do Next.js
echo "🧹 Limpando cache..."
rm -rf .next
rm -rf .turbo

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    pnpm install
fi

# Build com configurações otimizadas
echo "🔨 Fazendo build otimizado..."
pnpm build

echo "✅ Build local concluído com sucesso!"
