import "server-only";

import { config } from "@repo/config";
import type { Locale } from "@repo/i18n";
import { safeBuildTimeAsync } from "@shared/lib/build-time";

export async function getUserLocale(): Promise<Locale> {
	return safeBuildTimeAsync(config.i18n.defaultLocale as Locale, async () => {
		const { cookies } = await import("next/headers");
		const cookie = (await cookies()).get(config.i18n.localeCookieName);
		return (cookie?.value ?? config.i18n.defaultLocale) as Locale;
	});
}

export async function setLocaleCookie(locale: Locale): Promise<void> {
	await safeBuildTimeAsync(undefined, async () => {
		const { cookies } = await import("next/headers");
		(await cookies()).set(config.i18n.localeCookieName, locale);
	});
}
