'use client';

import { useState, useEffect } from 'react';
import { ChartData } from '../types';

export function useMetrics(organizationId: string, period: 'day' | 'week' | 'month' | 'year' = 'month') {
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchMetrics() {
      try {
        setIsLoading(true);

        // TODO: Implementar chamada real para a API
        const mockData: ChartData[] = [
          { label: "Jan", value: 4000, color: "#3b82f6" },
          { label: "Fev", value: 3000, color: "#3b82f6" },
          { label: "Mar", value: 5000, color: "#3b82f6" },
          { label: "Abr", value: 4500, color: "#3b82f6" },
          { label: "Mai", value: 6000, color: "#3b82f6" },
          { label: "Jun", value: 5500, color: "#3b82f6" }
        ];

        setChartData(mockData);
      } catch (error) {
        console.error('Erro ao carregar métricas:', error);
      } finally {
        setIsLoading(false);
      }
    }

    if (organizationId) {
      fetchMetrics();
    }
  }, [organizationId, period]);

  return {
    chartData,
    isLoading
  };
}
