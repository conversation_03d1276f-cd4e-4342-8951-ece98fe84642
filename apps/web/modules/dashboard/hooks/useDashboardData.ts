'use client'

import { useState, useEffect } from 'react';
import { FinancialMetrics, DashboardMetric } from '../types';

export function useDashboardData(organizationId: string) {
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDashboardData() {
      try {
        setIsLoading(true);
        // TODO: Implementar chamada real para a API
        const mockData: FinancialMetrics = {
          revenue: {
            today: 2350,
            week: 15800,
            month: 67500,
            year: 789000
          },
          transactions: {
            count: 89,
            volume: 2350,
            average: 26.4
          },
          pix: {
            percentage: 67,
            volume: 1574.5
          },
          card: {
            percentage: 28,
            volume: 658
          },
          boleto: {
            percentage: 5,
            volume: 117.5
          },
          balance: {
            available: 12450.89,
            pending: 3450.12,
            total: 15901.01
          }
        };

        setMetrics(mockData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao carregar dados');
      } finally {
        setIsLoading(false);
      }
    }

    if (organizationId) {
      fetchDashboardData();
    }
  }, [organizationId]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getMainMetrics = (): DashboardMetric[] => {
    if (!metrics) return [];

    return [
      {
        id: 'revenue-today',
        title: 'Receita Hoje',
        value: formatCurrency(metrics.revenue.today),
        description: 'vs ontem',
        trend: { value: '+12%', isPositive: true }
      },
      {
        id: 'transactions',
        title: 'Transações',
        value: metrics.transactions.count.toString(),
        description: 'vs ontem',
        trend: { value: '+5%', isPositive: true }
      },
      {
        id: 'pix-percentage',
        title: 'PIX',
        value: `${metrics.pix.percentage}%`,
        description: 'Método preferido'
      },
      {
        id: 'balance-available',
        title: 'Saldo Disponível',
        value: formatCurrency(metrics.balance.available),
        description: 'Pronto para saque'
      }
    ];
  };

  return {
    metrics,
    isLoading,
    error,
    getMainMetrics,
    formatCurrency
  };
}
