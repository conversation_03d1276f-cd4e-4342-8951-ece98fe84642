import { cn } from "@ui/lib";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Package, TrendingUp, TrendingDown } from "lucide-react";

interface OrderStatus {
  status: string;
  count: number;
  color: string;
}

interface OrdersWidgetProps {
  title: string;
  totalOrders: string | number;
  trend: {
    value: string;
    isPositive: boolean;
  };
  statusData: OrderStatus[];
  className?: string;
}

export function OrdersWidget({
  title,
  totalOrders,
  trend,
  statusData,
  className
}: OrdersWidgetProps) {
  const totalCount = statusData.reduce((sum, item) => sum + item.count, 0);
  
  return (
    <Card className={cn("bg-card", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Package className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="text-2xl font-bold">{totalOrders}</p>
            <div className="flex items-center gap-1 mt-1">
              {trend.isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
              <span className={cn(
                "text-sm font-medium",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                {trend.value}
              </span>
              <span className="text-xs text-muted-foreground ml-1">from last month</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex h-2 rounded-full overflow-hidden bg-muted">
              {statusData.map((item, index) => (
                <div
                  key={index}
                  className={cn("transition-all", item.color)}
                  style={{
                    width: `${(item.count / totalCount) * 100}%`
                  }}
                />
              ))}
            </div>
            
            <div className="space-y-1">
              {statusData.map((item, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div className={cn("w-2 h-2 rounded-full", item.color)} />
                    <span className="text-muted-foreground">{item.status}</span>
                  </div>
                  <span className="font-medium">{item.count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}