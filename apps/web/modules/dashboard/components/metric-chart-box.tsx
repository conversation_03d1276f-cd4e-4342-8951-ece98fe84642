'use client'
import { cn } from "@ui/lib"
import { Card, CardContent } from "@ui/components/card"
import { TrendingUp, TrendingDown } from "lucide-react"

interface MetricChartBoxProps {
  title: string
  value: string
  change: string
  changeType?: 'positive' | 'negative' | 'neutral'
  chart: React.ReactNode
  className?: string
  description?: string
}

export function MetricChartBox({
  title,
  value,
  change,
  changeType = 'positive',
  chart,
  className,
  description
}: MetricChartBoxProps) {
  const isPositive = changeType === 'positive'
  const isNegative = changeType === 'negative'

  return (
    <Card className={cn(
      "flex h-full w-full flex-col gap-y-6 bg-gray-50 p-6 dark:bg-polar-800 border-0 shadow-sm",
      className
    )}>
      <div className="flex flex-col gap-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-gray-500 dark:text-polar-500">
            {title}
          </h2>
        </div>

        <div className="space-y-2">
          <h3 className="text-4xl font-light tracking-tight text-gray-900 dark:text-white">
            {value}
          </h3>

          <div className="flex items-center gap-2">
            {isPositive && <TrendingUp className="h-4 w-4 text-green-600" />}
            {isNegative && <TrendingDown className="h-4 w-4 text-red-600" />}

            <p className={cn(
              "text-sm font-medium",
              isPositive && "text-green-600",
              isNegative && "text-red-600",
              changeType === 'neutral' && "text-gray-600 dark:text-polar-400"
            )}>
              {change}
            </p>
          </div>

          {description && (
            <p className="text-xs text-gray-500 dark:text-polar-500">
              {description}
            </p>
          )}
        </div>
      </div>

      <div className="flex-1 min-h-[200px]">
        {chart}
      </div>
    </Card>
  )
}
