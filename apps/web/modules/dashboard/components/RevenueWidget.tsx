import { cn } from "@ui/lib";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { DollarSign, TrendingUp, TrendingDown } from "lucide-react";

interface RevenueData {
  month: string;
  value: number;
}

interface RevenueWidgetProps {
  title: string;
  totalRevenue: string | number;
  trend: {
    value: string;
    isPositive: boolean;
  };
  data: RevenueData[];
  className?: string;
}

export function RevenueWidget({
  title,
  totalRevenue,
  trend,
  data,
  className
}: RevenueWidgetProps) {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <Card className={cn("bg-card", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <DollarSign className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="text-2xl font-bold">{totalRevenue}</p>
            <div className="flex items-center gap-1 mt-1">
              {trend.isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
              <span className={cn(
                "text-sm font-medium",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                {trend.value}
              </span>
              <span className="text-xs text-muted-foreground ml-1">from last month</span>
            </div>
          </div>
          
          <div className="h-20">
            <div className="flex items-end justify-between h-full gap-1">
              {data.map((item, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-primary rounded-sm transition-all hover:opacity-80 w-full"
                    style={{
                      height: `${(item.value / maxValue) * 60}px`,
                      minHeight: '4px'
                    }}
                  />
                  <span className="text-xs text-muted-foreground mt-1">
                    {item.month.slice(0, 3)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}