'use client';

import { CheckoutLink, CheckoutProduct } from '../types';

/**
 * Client-side service for managing checkout links
 */
export class CheckoutLinkService {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || '/api';
  }

  /**
   * Generate a checkout link for a product
   */
  async generateLink(options: {
    productId: string;
    offerId?: string;
    utmParams?: Record<string, string>;
    expiresIn?: number;
    customParams?: Record<string, string>;
  }): Promise<{ success: boolean; data?: CheckoutLink; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/checkout/generate-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate checkout link');
      }

      return {
        success: true,
        data: {
          id: result.data.linkId,
          productId: result.data.productId,
          offerId: result.data.offerId,
          url: result.data.url,
          expiresAt: result.data.expiresAt ? new Date(result.data.expiresAt) : undefined,
          createdAt: new Date(result.data.createdAt),
          utmParams: options.utmParams,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Generate multiple checkout links
   */
  async generateBulkLinks(links: Array<{
    productId: string;
    offerId?: string;
    utmParams?: Record<string, string>;
    expiresIn?: number;
  }>): Promise<{
    success: boolean;
    data?: {
      generated: CheckoutLink[];
      failed: Array<{ productId: string; error: string }>;
    };
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/checkout/generate-bulk-links`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ links }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate bulk checkout links');
      }

      return {
        success: true,
        data: {
          generated: result.data.generated.map((item: any) => ({
            id: item.linkId,
            productId: item.productId,
            offerId: item.offerId,
            url: item.url,
            expiresAt: item.expiresAt ? new Date(item.expiresAt) : undefined,
            createdAt: new Date(item.createdAt),
          })),
          failed: result.data.failed,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Track a checkout link click
   */
  async trackClick(linkId: string, options?: {
    userAgent?: string;
    referrer?: string;
    utmParams?: Record<string, string>;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/checkout/track-click`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          linkId,
          userAgent: options?.userAgent || navigator.userAgent,
          referrer: options?.referrer || document.referrer,
          utmParams: options?.utmParams,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to track click');
      }

      return { success: true };
    } catch (error) {
      console.error('Error tracking click:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Track a checkout conversion
   */
  async trackConversion(linkId: string, options: {
    orderId: string;
    amount: number;
    paymentMethod: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/checkout/track-conversion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          linkId,
          ...options,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to track conversion');
      }

      return { success: true };
    } catch (error) {
      console.error('Error tracking conversion:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get analytics for a checkout link
   */
  async getLinkAnalytics(linkId: string, includeHistory: boolean = false): Promise<{
    success: boolean;
    data?: {
      linkId: string;
      productId: string;
      url: string;
      clicks: number;
      conversions: number;
      conversionRate: number;
      revenue: number;
      createdAt: Date;
      expiresAt?: Date;
      isExpired: boolean;
      clickHistory?: Array<{
        timestamp: Date;
        userAgent?: string;
        referrer?: string;
        converted: boolean;
      }>;
    };
    error?: string;
  }> {
    try {
      const url = new URL(`${this.baseUrl}/checkout/analytics/${linkId}`);
      if (includeHistory) {
        url.searchParams.set('includeHistory', 'true');
      }

      const response = await fetch(url.toString());
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to get link analytics');
      }

      return {
        success: true,
        data: {
          ...result.data,
          createdAt: new Date(result.data.createdAt),
          expiresAt: result.data.expiresAt ? new Date(result.data.expiresAt) : undefined,
          clickHistory: result.data.clickHistory?.map((click: any) => ({
            ...click,
            timestamp: new Date(click.timestamp),
          })),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Build a checkout URL with UTM parameters
   */
  buildCheckoutUrl(productId: string, options?: {
    offerId?: string;
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmContent?: string;
    utmTerm?: string;
    email?: string;
    ref?: string;
  }): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.supgateway.com';
    const url = new URL(`${baseUrl}/checkout/${productId}`);

    if (options?.offerId) {
      url.searchParams.set('offer', options.offerId);
    }

    if (options?.email) {
      url.searchParams.set('email', options.email);
    }

    if (options?.ref) {
      url.searchParams.set('ref', options.ref);
    }

    // Add UTM parameters
    const utmParams = {
      utm_source: options?.utmSource,
      utm_medium: options?.utmMedium,
      utm_campaign: options?.utmCampaign,
      utm_content: options?.utmContent,
      utm_term: options?.utmTerm,
    };

    Object.entries(utmParams).forEach(([key, value]) => {
      if (value) {
        url.searchParams.set(key, value);
      }
    });

    return url.toString();
  }

  /**
   * Parse UTM parameters from URL
   */
  parseUtmParams(url: string): Record<string, string> {
    const urlObj = new URL(url);
    const utmParams: Record<string, string> = {};

    urlObj.searchParams.forEach((value, key) => {
      if (key.startsWith('utm_')) {
        utmParams[key] = value;
      }
    });

    return utmParams;
  }

  /**
   * Validate checkout link expiry
   */
  isLinkExpired(link: CheckoutLink): boolean {
    if (!link.expiresAt) return false;
    return new Date() > link.expiresAt;
  }

  /**
   * Get time remaining for a checkout link
   */
  getTimeRemaining(link: CheckoutLink): {
    expired: boolean;
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } {
    if (!link.expiresAt) {
      return { expired: false, days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const now = new Date().getTime();
    const expiry = link.expiresAt.getTime();
    const difference = expiry - now;

    if (difference <= 0) {
      return { expired: true, days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { expired: false, days, hours, minutes, seconds };
  }
}

// Export singleton instance
export const checkoutLinkService = new CheckoutLinkService();
