import { CheckoutProduct, CheckoutLink, CheckoutSession } from '../types';

/**
 * Format currency value from cents to display format
 */
export function formatCurrency(value: number, currency: string = 'BRL'): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency,
  }).format(value);
}

/**
 * Convert currency display format to cents
 */
export function parseCurrencyToCents(value: string): number {
  const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
  return Math.round(parseFloat(cleanValue) * 100);
}

/**
 * Generate a secure checkout link for a product
 */
export function generateCheckoutLink(
  productId: string,
  options: {
    offerId?: string;
    utmParams?: Record<string, string>;
    expiresIn?: number; // minutes
    baseUrl?: string;
  } = {}
): CheckoutLink {
  const {
    offerId,
    utmParams = {},
    expiresIn = 60 * 24, // 24 hours default
    baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.supgateway.com'
  } = options;

  const linkId = `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Build URL with parameters
  const url = new URL(`${baseUrl}/checkout/${productId}`);
  
  // Add offer ID if provided
  if (offerId) {
    url.searchParams.set('offer', offerId);
  }
  
  // Add UTM parameters
  Object.entries(utmParams).forEach(([key, value]) => {
    if (key.startsWith('utm_') && value) {
      url.searchParams.set(key, value);
    }
  });
  
  // Add tracking parameter
  url.searchParams.set('ref', linkId);

  return {
    id: linkId,
    productId,
    offerId,
    url: url.toString(),
    expiresAt: expiresIn > 0 ? new Date(Date.now() + expiresIn * 60 * 1000) : undefined,
    utmParams,
    createdAt: new Date(),
  };
}

/**
 * Calculate total amount including offers and discounts
 */
export function calculateCheckoutTotal(
  basePrice: number,
  selectedOffers: { id: string; price: number }[] = [],
  coupon?: { discount: number; type: 'percentage' | 'fixed' } | null
): {
  subtotal: number;
  discountAmount: number;
  total: number;
} {
  const subtotal = basePrice + selectedOffers.reduce((sum, offer) => sum + offer.price, 0);
  
  let discountAmount = 0;
  if (coupon) {
    if (coupon.type === 'percentage') {
      discountAmount = Math.round(subtotal * coupon.discount);
    } else {
      discountAmount = Math.min(coupon.discount, subtotal);
    }
  }
  
  const total = Math.max(0, subtotal - discountAmount);
  
  return {
    subtotal,
    discountAmount,
    total,
  };
}

/**
 * Validate checkout session expiry
 */
export function isCheckoutSessionValid(session: CheckoutSession): boolean {
  return new Date() < session.expiresAt;
}

/**
 * Generate order ID
 */
export function generateOrderId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `order_${timestamp}_${random}`.toUpperCase();
}

/**
 * Generate transaction ID
 */
export function generateTransactionId(paymentMethod: string): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  const prefix = paymentMethod.toLowerCase().substr(0, 3);
  return `${prefix}_${timestamp}_${random}`.toUpperCase();
}

/**
 * Mask credit card number for display
 */
export function maskCreditCard(cardNumber: string): string {
  const cleaned = cardNumber.replace(/\s/g, '');
  if (cleaned.length < 4) return cardNumber;
  
  const lastFour = cleaned.slice(-4);
  const masked = '*'.repeat(cleaned.length - 4);
  return `${masked}${lastFour}`.replace(/(.{4})/g, '$1 ').trim();
}

/**
 * Format CPF for display
 */
export function formatCPF(cpf: string): string {
  const cleaned = cpf.replace(/\D/g, '');
  return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Format phone number for display
 */
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 11) {
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  return phone;
}

/**
 * Get payment method display name
 */
export function getPaymentMethodName(method: string): string {
  const methods: Record<string, string> = {
    'CREDIT_CARD': 'Cartão de Crédito',
    'PIX': 'PIX',
    'BOLETO': 'Boleto Bancário',
  };
  return methods[method] || method;
}

/**
 * Get payment method icon
 */
export function getPaymentMethodIcon(method: string): string {
  const icons: Record<string, string> = {
    'CREDIT_CARD': '💳',
    'PIX': '⚡',
    'BOLETO': '📄',
  };
  return icons[method] || '💰';
}

/**
 * Calculate installment amounts
 */
export function calculateInstallments(
  amount: number,
  maxInstallments: number,
  interestRate: number = 0.0299 // 2.99% per month
): Array<{
  number: number;
  amount: number;
  totalAmount: number;
  interestRate: number;
}> {
  const installments = [];
  
  for (let i = 1; i <= maxInstallments; i++) {
    let totalWithInterest = amount;
    let currentInterestRate = 0;
    
    if (i > 1) {
      currentInterestRate = interestRate;
      totalWithInterest = amount * (1 + (interestRate * (i - 1)));
    }
    
    const installmentAmount = Math.ceil(totalWithInterest / i);
    
    installments.push({
      number: i,
      amount: installmentAmount,
      totalAmount: installmentAmount * i,
      interestRate: currentInterestRate * 100,
    });
  }
  
  return installments;
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate CPF
 */
export function isValidCPF(cpf: string): boolean {
  const cleaned = cpf.replace(/\D/g, '');
  
  if (cleaned.length !== 11 || /^(\d)\1{10}$/.test(cleaned)) {
    return false;
  }
  
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleaned.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleaned.charAt(9))) return false;
  
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleaned.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleaned.charAt(10))) return false;
  
  return true;
}

/**
 * Track checkout event for analytics
 */
export function trackCheckoutEvent(
  event: string,
  data: Record<string, any>
): void {
  if (typeof window !== 'undefined') {
    // Google Analytics
    if ((window as any).gtag) {
      (window as any).gtag('event', event, data);
    }
    
    // Facebook Pixel
    if ((window as any).fbq) {
      (window as any).fbq('track', event, data);
    }
    
    // Custom analytics
    console.log(`Checkout Event: ${event}`, data);
  }
}
