'use client';

import { useState, useCallback } from 'react';
import { CheckoutFormSubmitData, PaymentResult, InstallmentOption } from '../types';

export interface PixPaymentData {
  pixCode: string;
  qrCode: string;
  expiresAt: Date;
  orderId: string;
}

export function usePaymentProcessing() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [pixData, setPixData] = useState<PixPaymentData | null>(null);

  // Calculate installment options
  const calculateInstallments = useCallback((amount: number, maxInstallments: number): InstallmentOption[] => {
    const options: InstallmentOption[] = [];
    
    for (let i = 1; i <= maxInstallments; i++) {
      let interestRate = 0;
      
      // Apply interest rates for installments > 1
      if (i > 1) {
        interestRate = 0.0299; // 2.99% per month (example rate)
      }
      
      const totalWithInterest = amount * (1 + (interestRate * (i - 1)));
      const installmentAmount = Math.ceil(totalWithInterest / i);
      
      options.push({
        number: i,
        amount: installmentAmount,
        totalAmount: installmentAmount * i,
        interestRate: interestRate * 100 // Convert to percentage
      });
    }
    
    return options;
  }, []);

  // Process credit card payment
  const processCreditCardPayment = useCallback(async (
    data: CheckoutFormSubmitData, 
    amount: number
  ): Promise<PaymentResult> => {
    setIsProcessing(true);
    
    try {
      // TODO: Integrate with actual payment processor (Stripe, PagSeguro, etc.)
      console.log('Processing credit card payment:', {
        amount,
        installments: data.creditCard?.installments,
        cardData: {
          ...data.creditCard,
          cardNumber: data.creditCard?.cardNumber?.slice(-4), // Only log last 4 digits
          cardCvv: '***' // Never log CVV
        }
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock successful response
      const orderId = `order-${Date.now()}`;
      
      return {
        success: true,
        orderId,
        transactionId: `tx-${Date.now()}`,
        paymentMethod: 'CREDIT_CARD',
        redirectUrl: `/checkout/success?orderId=${orderId}`
      };
    } catch (error) {
      console.error('Credit card payment error:', error);
      return {
        success: false,
        paymentMethod: 'CREDIT_CARD',
        error: 'Erro ao processar pagamento com cartão de crédito'
      };
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Process PIX payment
  const processPixPayment = useCallback(async (
    data: CheckoutFormSubmitData, 
    amount: number
  ): Promise<PaymentResult> => {
    setIsProcessing(true);
    
    try {
      // TODO: Integrate with actual PIX provider
      console.log('Processing PIX payment:', { amount, customerData: data.customerData });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const orderId = `order-${Date.now()}`;
      const pixCode = '00020126580014br.gov.bcb.pix013636c4b8e5-4d4e-4c4e-8b4e-4d4e4c4e8b4e5204000053039865802BR5925SupGateway LTDA6009SAO PAULO62070503***6304A1B2';
      
      // Generate PIX data
      const pixPaymentData: PixPaymentData = {
        pixCode,
        qrCode: pixCode, // In real implementation, generate actual QR code
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
        orderId
      };

      setPixData(pixPaymentData);

      return {
        success: true,
        orderId,
        transactionId: `pix-${Date.now()}`,
        paymentMethod: 'PIX',
        redirectUrl: `/checkout/pix?orderId=${orderId}`
      };
    } catch (error) {
      console.error('PIX payment error:', error);
      return {
        success: false,
        paymentMethod: 'PIX',
        error: 'Erro ao gerar pagamento PIX'
      };
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Process boleto payment
  const processBoletoPayment = useCallback(async (
    data: CheckoutFormSubmitData, 
    amount: number
  ): Promise<PaymentResult> => {
    setIsProcessing(true);
    
    try {
      // TODO: Integrate with actual boleto provider
      console.log('Processing boleto payment:', { amount, customerData: data.customerData });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1800));

      const orderId = `order-${Date.now()}`;
      
      return {
        success: true,
        orderId,
        transactionId: `boleto-${Date.now()}`,
        paymentMethod: 'BOLETO',
        redirectUrl: `/checkout/success?orderId=${orderId}`
      };
    } catch (error) {
      console.error('Boleto payment error:', error);
      return {
        success: false,
        paymentMethod: 'BOLETO',
        error: 'Erro ao gerar boleto'
      };
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Main payment processing function
  const processPayment = useCallback(async (
    data: CheckoutFormSubmitData, 
    amount: number
  ): Promise<PaymentResult> => {
    switch (data.paymentMethod) {
      case 'CREDIT_CARD':
        return processCreditCardPayment(data, amount);
      case 'PIX':
        return processPixPayment(data, amount);
      case 'BOLETO':
        return processBoletoPayment(data, amount);
      default:
        return {
          success: false,
          paymentMethod: data.paymentMethod,
          error: 'Método de pagamento não suportado'
        };
    }
  }, [processCreditCardPayment, processPixPayment, processBoletoPayment]);

  // Validate payment data
  const validatePaymentData = useCallback((data: CheckoutFormSubmitData): string[] => {
    const errors: string[] = [];

    if (data.paymentMethod === 'CREDIT_CARD') {
      if (!data.creditCard) {
        errors.push('Dados do cartão são obrigatórios');
      } else {
        if (!data.creditCard.cardNumber || data.creditCard.cardNumber.length < 16) {
          errors.push('Número do cartão inválido');
        }
        if (!data.creditCard.cardHolder || data.creditCard.cardHolder.length < 3) {
          errors.push('Nome do titular inválido');
        }
        if (!data.creditCard.cardExpiry || data.creditCard.cardExpiry.length < 5) {
          errors.push('Data de validade inválida');
        }
        if (!data.creditCard.cardCvv || data.creditCard.cardCvv.length < 3) {
          errors.push('CVV inválido');
        }
      }
    }

    // Validate customer data
    if (!data.customerData.name || data.customerData.name.length < 2) {
      errors.push('Nome inválido');
    }
    if (!data.customerData.email || !data.customerData.email.includes('@')) {
      errors.push('Email inválido');
    }
    if (!data.customerData.cpf || data.customerData.cpf.length !== 11) {
      errors.push('CPF inválido');
    }
    if (!data.customerData.phone || data.customerData.phone.length < 10) {
      errors.push('Telefone inválido');
    }

    return errors;
  }, []);

  return {
    isProcessing,
    pixData,
    processPayment,
    calculateInstallments,
    validatePaymentData,
    processCreditCardPayment,
    processPixPayment,
    processBoletoPayment,
  };
}
