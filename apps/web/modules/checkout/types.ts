import { z } from 'zod';

// Customer data schemas
export const customerDataSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  cpf: z.string().min(11, "CPF inválido").max(14, "CPF inválido"),
  phone: z.string().min(10, "Telefone inválido"),
});

export const customerDataSubmitSchema = customerDataSchema.extend({
  cpf: z.string().regex(/^\d{11}$/, "CPF deve conter apenas números"),
  phone: z.string().regex(/^\d{10,11}$/, "Telefone deve conter apenas números"),
});

// Payment schemas
export const creditCardSchema = z.object({
  cardNumber: z.string().min(16, "Número do cartão inválido"),
  cardHolder: z.string().min(3, "Nome do titular inválido"),
  cardExpiry: z.string().min(5, "Data de validade inválida"),
  cardCvv: z.string().min(3, "CVV inválido"),
  installments: z.number().min(1).max(12).default(1),
});

export const checkoutFormSchema = z.object({
  customerData: customerDataSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.union([
    creditCardSchema.optional(),
    z.undefined()
  ]).optional().superRefine((val, ctx) => {
    const paymentMethod = (ctx as any).path?.[1]?.input?.paymentMethod;
    if (paymentMethod === "CREDIT_CARD" && !val) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
      });
    }
  }),
  productId: z.string().min(1, "ID do produto é obrigatório"),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
});

export const checkoutFormSubmitSchema = z.object({
  customerData: customerDataSubmitSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.optional(creditCardSchema),
  productId: z.string().min(1, "ID do produto é obrigatório"),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
}).refine((data) => {
  if (data.paymentMethod === "CREDIT_CARD" && !data.creditCard) {
    return false;
  }
  return true;
}, {
  message: "Dados do cartão são obrigatórios para pagamento com cartão de crédito",
  path: ["creditCard"],
});

// Type definitions
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;
export type CheckoutFormSubmitData = z.infer<typeof checkoutFormSubmitSchema>;
export type CustomerData = z.infer<typeof customerDataSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;

// Product and offer interfaces
export interface CheckoutProduct {
  id: string;
  title: string;
  description?: string | null;
  type: 'COURSE' | 'MENTORSHIP' | 'EBOOK' | 'SUBSCRIPTION' | 'BUNDLE';
  price: number;
  installmentsLimit: number;
  thumbnail?: string | null;
  offers?: Offer[];
  regularPrice?: number | null;
  enableInstallments?: boolean;
  checkoutBanner?: string | null;
  checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
  acceptedPayments?: string[];
  checkoutSettings?: any;
  customCheckoutUrl?: string | null;
  successUrl?: string | null;
  cancelUrl?: string | null;
  termsUrl?: string | null;
  creator: {
    id: string;
    name: string;
  };
}

export interface Offer {
  id: string;
  title: string;
  description?: string | null;
  price: number;
  type: string;
}

// Checkout session and payment types
export interface CheckoutSession {
  id: string;
  productId: string;
  product: CheckoutProduct;
  customerData?: Partial<CustomerData>;
  selectedOffers: string[];
  appliedCoupon?: {
    code: string;
    discount: number;
    finalPrice: number;
  };
  totalAmount: number;
  expiresAt: Date;
  createdAt: Date;
}

export interface PaymentResult {
  success: boolean;
  orderId?: string;
  transactionId?: string;
  paymentMethod: string;
  redirectUrl?: string;
  error?: string;
}

export interface CheckoutLink {
  id: string;
  productId: string;
  offerId?: string;
  url: string;
  expiresAt?: Date;
  utmParams?: Record<string, string>;
  createdAt: Date;
}

// Payment processing types
export interface PaymentProcessor {
  processPayment(data: CheckoutFormSubmitData, amount: number): Promise<PaymentResult>;
  generatePixPayment?(data: CheckoutFormSubmitData, amount: number): Promise<{
    pixCode: string;
    qrCode: string;
    expiresAt: Date;
  }>;
}

export interface InstallmentOption {
  number: number;
  amount: number;
  totalAmount: number;
  interestRate: number;
}
