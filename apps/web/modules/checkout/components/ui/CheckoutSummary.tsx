'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Separator } from '@ui/components/separator';
import { ShoppingCart, Tag, Minus } from 'lucide-react';

import { CheckoutProduct, Offer } from '../../types';
import { formatCurrency } from '../../lib/checkout-utils';

interface CheckoutSummaryProps {
  product: CheckoutProduct;
  selectedOffers: Offer[];
  appliedCoupon?: {
    code: string;
    discount: number;
    finalPrice: number;
  } | null;
  totalAmount: number;
}

export function CheckoutSummary({
  product,
  selectedOffers,
  appliedCoupon,
  totalAmount,
}: CheckoutSummaryProps) {
  const subtotal = product.price + selectedOffers.reduce((sum, offer) => sum + offer.price, 0);
  const discountAmount = appliedCoupon ? subtotal - appliedCoupon.finalPrice : 0;

  return (
    <Card className="sticky top-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Resumo do Pedido
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            {product.thumbnail && (
              <img
                src={product.thumbnail}
                alt={product.title}
                className="h-16 w-16 rounded-lg object-cover"
              />
            )}
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 line-clamp-2">
                {product.title}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {product.type === 'COURSE' && 'Curso Online'}
                {product.type === 'EBOOK' && 'E-book'}
                				{product.type === 'MENTORSHIP' && 'Mentoria'}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <span className="font-semibold text-gray-900">
                  {formatCurrency(product.price / 100)}
                </span>
                {product.regularPrice && product.regularPrice > product.price && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatCurrency(product.regularPrice / 100)}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Selected Offers */}
        {selectedOffers.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Bônus Inclusos</h4>
              {selectedOffers.map((offer) => (
                <div key={offer.id} className="flex items-start gap-3">
                  <div className="flex-1 min-w-0">
                    <h5 className="text-sm font-medium text-gray-900">
                      {offer.title}
                    </h5>
                    {offer.description && (
                      <p className="text-xs text-gray-500 mt-1">
                        {offer.description}
                      </p>
                    )}
                    <span className="text-sm font-semibold text-green-600 mt-1 block">
                      + {formatCurrency(offer.price / 100)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        <Separator />

        {/* Pricing Breakdown */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">
              {formatCurrency(subtotal / 100)}
            </span>
          </div>

          {appliedCoupon && (
            <div className="flex justify-between text-sm">
              <div className="flex items-center gap-1">
                <Minus className="h-3 w-3 text-green-600" />
                <span className="text-green-600">
                  Desconto ({appliedCoupon.code})
                </span>
              </div>
              <span className="font-medium text-green-600">
                -{formatCurrency(discountAmount / 100)}
              </span>
            </div>
          )}

          <Separator />

          <div className="flex justify-between text-lg font-semibold">
            <span>Total</span>
            <span className="text-green-600">
              {formatCurrency(totalAmount / 100)}
            </span>
          </div>
        </div>

        {/* Security Badge */}
        <div className="rounded-lg bg-gray-50 p-3 text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center">
              <div className="h-2 w-2 rounded-full bg-white" />
            </div>
            <span>Compra 100% Segura</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Certificado SSL • Dados Criptografados
          </p>
        </div>

        {/* Guarantee Badge */}
        <div className="rounded-lg bg-blue-50 p-3 text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-blue-800">
            <Tag className="h-4 w-4" />
            <span className="font-medium">Garantia de 7 dias</span>
          </div>
          <p className="text-xs text-blue-600 mt-1">
            Não gostou? Devolvemos seu dinheiro
          </p>
        </div>

        {/* Creator Info */}
        <div className="rounded-lg border p-3">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600">
                {product.creator.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {product.creator.name}
              </p>
              <p className="text-xs text-gray-500">Criador do produto</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
