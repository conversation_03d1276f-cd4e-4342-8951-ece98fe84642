'use client';

import { useState } from 'react';
import { Card, CardContent } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Badge } from '@ui/components/badge';
import { Ticket, X, Loader2 } from 'lucide-react';

interface CouponFormProps {
  appliedCoupon?: {
    code: string;
    discount: number;
    finalPrice: number;
  } | null;
  onApplyCoupon: (code: string) => Promise<boolean>;
  onRemoveCoupon: () => void;
}

export function CouponForm({
  appliedCoupon,
  onApplyCoupon,
  onRemoveCoupon,
}: CouponFormProps) {
  const [couponCode, setCouponCode] = useState('');
  const [isApplying, setIsApplying] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) return;

    setIsApplying(true);
    try {
      const success = await onApplyCoupon(couponCode.trim().toUpperCase());
      if (success) {
        setCouponCode('');
        setShowForm(false);
      }
    } finally {
      setIsApplying(false);
    }
  };

  const handleRemoveCoupon = () => {
    onRemoveCoupon();
    setCouponCode('');
    setShowForm(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleApplyCoupon();
    }
  };

  if (appliedCoupon) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Ticket className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                Cupom aplicado:
              </span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {appliedCoupon.code}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemoveCoupon}
              className="h-8 w-8 p-0 text-green-600 hover:bg-green-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="mt-2 text-sm text-green-700">
            Desconto de {Math.round(appliedCoupon.discount * 100)}% aplicado com sucesso!
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!showForm) {
    return (
      <div className="text-center">
        <Button
          variant="outline"
          onClick={() => setShowForm(true)}
          className="text-blue-600 border-blue-200 hover:bg-blue-50"
        >
          <Ticket className="mr-2 h-4 w-4" />
          Tenho um cupom de desconto
        </Button>
      </div>
    );
  }

  return (
    <Card className="border-blue-200">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Ticket className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Cupom de Desconto
            </span>
          </div>
          
          <div className="flex gap-2">
            <Input
              placeholder="Digite seu cupom"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              onKeyPress={handleKeyPress}
              className="flex-1"
              disabled={isApplying}
            />
            <Button
              onClick={handleApplyCoupon}
              disabled={!couponCode.trim() || isApplying}
              className="shrink-0"
            >
              {isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Aplicar'
              )}
            </Button>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Digite o código do cupom e clique em aplicar
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowForm(false);
                setCouponCode('');
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              Cancelar
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
