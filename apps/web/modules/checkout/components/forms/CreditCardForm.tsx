'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';

import { CheckoutFormData } from '../../types';
import { useCheckoutValidation } from '../../hooks/useCheckoutValidation';
import { usePaymentProcessing } from '../../hooks/usePaymentProcessing';
import { formatCurrency } from '../../lib/checkout-utils';

interface CreditCardFormProps {
  totalAmount: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
}

export function CreditCardForm({
  totalAmount,
  installmentsLimit,
  enableInstallments = true,
}: CreditCardFormProps) {
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<CheckoutFormData>();

  const { validateCreditCard } = useCheckoutValidation();
  const { calculateInstallments } = usePaymentProcessing();

  const installments = watch('creditCard.installments') || 1;

  // Format card number input
  const formatCardNumber = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{4})(?=\d)/g, '$1 ').trim();
  };

  // Format expiry date input
  const formatExpiry = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length >= 2) {
      return numbers.replace(/(\d{2})(\d{0,2})/, '$1/$2');
    }
    return numbers;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCardNumber(e.target.value);
    if (formatted.replace(/\s/g, '').length <= 16) {
      setValue('creditCard.cardNumber', formatted);
    }
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatExpiry(e.target.value);
    if (formatted.length <= 5) {
      setValue('creditCard.cardExpiry', formatted);
    }
  };

  const handleCvvChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const numbers = e.target.value.replace(/\D/g, '');
    if (numbers.length <= 4) {
      setValue('creditCard.cardCvv', numbers);
    }
  };

  // Calculate installment options
  const installmentOptions = enableInstallments 
    ? calculateInstallments(totalAmount, Math.min(installmentsLimit, 12))
    : [{ number: 1, amount: totalAmount, totalAmount, interestRate: 0 }];

  // Get card brand from number
  const getCardBrand = (cardNumber: string) => {
    const number = cardNumber.replace(/\s/g, '');
    if (/^4/.test(number)) return 'visa';
    if (/^5[1-5]/.test(number)) return 'mastercard';
    if (/^3[47]/.test(number)) return 'amex';
    if (/^6/.test(number)) return 'discover';
    return 'unknown';
  };

  const cardNumber = watch('creditCard.cardNumber') || '';
  const cardBrand = getCardBrand(cardNumber);

  return (
    <div className="space-y-4 rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-gray-900">Dados do Cartão</h4>
      
      <div className="grid gap-4">
        <div className="space-y-2">
          <Label htmlFor="cardNumber">
            Número do Cartão <span className="text-red-500">*</span>
          </Label>
          <div className="relative">
            <Input
              id="cardNumber"
              placeholder="0000 0000 0000 0000"
              maxLength={19}
              {...register('creditCard.cardNumber', {
                onChange: handleCardNumberChange,
                validate: (value) => {
                  const cleanNumber = value?.replace(/\s/g, '') || '';
                  if (!validateCreditCard(cleanNumber)) {
                    return 'Número do cartão inválido';
                  }
                  return true;
                }
              })}
              error={errors.creditCard?.cardNumber?.message}
            />
            {cardBrand !== 'unknown' && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <div className={`h-6 w-10 rounded bg-gray-100 flex items-center justify-center text-xs font-bold ${
                  cardBrand === 'visa' ? 'text-blue-600' :
                  cardBrand === 'mastercard' ? 'text-red-600' :
                  cardBrand === 'amex' ? 'text-green-600' :
                  'text-gray-600'
                }`}>
                  {cardBrand.toUpperCase()}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="cardHolder">
            Nome do Titular <span className="text-red-500">*</span>
          </Label>
          <Input
            id="cardHolder"
            placeholder="Nome como está no cartão"
            {...register('creditCard.cardHolder')}
            error={errors.creditCard?.cardHolder?.message}
            style={{ textTransform: 'uppercase' }}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="cardExpiry">
              Validade <span className="text-red-500">*</span>
            </Label>
            <Input
              id="cardExpiry"
              placeholder="MM/AA"
              maxLength={5}
              {...register('creditCard.cardExpiry', {
                onChange: handleExpiryChange,
                validate: (value) => {
                  if (!value || value.length < 5) {
                    return 'Data de validade inválida';
                  }
                  const [month, year] = value.split('/');
                  const monthNum = parseInt(month);
                  const yearNum = parseInt(`20${year}`);
                  const now = new Date();
                  const expiry = new Date(yearNum, monthNum - 1);
                  
                  if (monthNum < 1 || monthNum > 12) {
                    return 'Mês inválido';
                  }
                  if (expiry < now) {
                    return 'Cartão expirado';
                  }
                  return true;
                }
              })}
              error={errors.creditCard?.cardExpiry?.message}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="cardCvv">
              CVV <span className="text-red-500">*</span>
            </Label>
            <Input
              id="cardCvv"
              placeholder="123"
              maxLength={4}
              {...register('creditCard.cardCvv', {
                onChange: handleCvvChange
              })}
              error={errors.creditCard?.cardCvv?.message}
            />
          </div>
        </div>

        {/* Installments */}
        {enableInstallments && installmentOptions.length > 1 && (
          <div className="space-y-2">
            <Label htmlFor="installments">Parcelas</Label>
            <Select
              value={installments.toString()}
              onValueChange={(value) => setValue('creditCard.installments', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione as parcelas" />
              </SelectTrigger>
              <SelectContent>
                {installmentOptions.map((option) => (
                  <SelectItem key={option.number} value={option.number.toString()}>
                    {option.number}x de {formatCurrency(option.amount / 100)}
                    {option.interestRate > 0 && (
                      <span className="text-gray-500 ml-1">
                        (Total: {formatCurrency(option.totalAmount / 100)})
                      </span>
                    )}
                    {option.number === 1 && (
                      <span className="text-green-600 ml-1 font-medium">sem juros</span>
                    )}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {installments > 1 && (
              <p className="text-xs text-gray-500">
                Total parcelado: {formatCurrency(installmentOptions[installments - 1]?.totalAmount / 100 || 0)}
              </p>
            )}
          </div>
        )}
      </div>

      <div className="rounded-lg bg-green-50 p-3 text-sm text-green-800">
        <p className="font-medium">🔒 Pagamento Seguro</p>
        <p className="mt-1">
          Seus dados são criptografados e processados com segurança.
          Não armazenamos informações do seu cartão.
        </p>
      </div>
    </div>
  );
}
