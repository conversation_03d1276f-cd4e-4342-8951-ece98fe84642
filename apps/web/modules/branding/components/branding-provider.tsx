"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { BrandingConfig } from "../lib/branding";

interface BrandingContextType {
  config: BrandingConfig | null;
  brandingConfig: BrandingConfig | null;
  isLoading: boolean;
  updateConfig: (newConfig: Partial<BrandingConfig>) => void;
}

const BrandingContext = createContext<BrandingContextType | undefined>(undefined);

interface BrandingProviderProps {
  children: React.ReactNode;
  organizationId: string;
  initialConfig?: BrandingConfig | null;
}

export function BrandingProvider({ children, organizationId, initialConfig }: BrandingProviderProps) {
  const [config, setConfig] = useState<BrandingConfig | null>(initialConfig || null);
  const [isLoading, setIsLoading] = useState(!initialConfig);

  useEffect(() => {
    if (!initialConfig) {
      fetchBrandingConfig();
    }
  }, [organizationId, initialConfig]);

  useEffect(() => {
    if (config) {
      applyBrandingStyles(config);
    }
  }, [config]);

  const fetchBrandingConfig = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/organizations/${organizationId}/branding`);
      if (response.ok) {
        const data = await response.json();
        setConfig(data.brandingConfig);
      }
    } catch (error) {
      console.error('Error fetching branding config:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfig = (newConfig: Partial<BrandingConfig>) => {
    setConfig(prev => prev ? { ...prev, ...newConfig } : newConfig as BrandingConfig);
  };

  const applyBrandingStyles = (brandingConfig: BrandingConfig) => {
    const root = document.documentElement;
    
    // Apply CSS variables
    if (brandingConfig.primaryColor) {
      root.style.setProperty('--primary', brandingConfig.primaryColor);
    }
    if (brandingConfig.secondaryColor) {
      root.style.setProperty('--secondary', brandingConfig.secondaryColor);
    }
    if (brandingConfig.accentColor) {
      root.style.setProperty('--accent', brandingConfig.accentColor);
    }
    if (brandingConfig.backgroundColor) {
      root.style.setProperty('--background', brandingConfig.backgroundColor);
    }
    if (brandingConfig.textColor) {
      root.style.setProperty('--foreground', brandingConfig.textColor);
    }
    if (brandingConfig.linkColor) {
      root.style.setProperty('--link', brandingConfig.linkColor);
    }
    if (brandingConfig.buttonColor) {
      root.style.setProperty('--button', brandingConfig.buttonColor);
    }
    if (brandingConfig.borderColor) {
      root.style.setProperty('--border', brandingConfig.borderColor);
    }
    if (brandingConfig.fontFamily) {
      root.style.setProperty('--font-family', brandingConfig.fontFamily);
      document.body.style.fontFamily = `${brandingConfig.fontFamily}, sans-serif`;
    }
    if (brandingConfig.borderRadius) {
      root.style.setProperty('--border-radius', brandingConfig.borderRadius);
    }

    // Apply favicon
    if (brandingConfig.faviconUrl) {
      updateFavicon(brandingConfig.faviconUrl);
    }

    // Apply page title
    if (brandingConfig.companyName) {
      updatePageTitle(brandingConfig.companyName);
    }

    // Apply dark mode
    if (brandingConfig.darkMode !== undefined) {
      root.classList.toggle('dark', brandingConfig.darkMode);
    }

    // Apply custom CSS
    if (brandingConfig.customCss) {
      injectCustomCSS(brandingConfig.customCss);
    }
  };

  const updateFavicon = (faviconUrl: string) => {
    const existingFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
    if (existingFavicon) {
      existingFavicon.href = faviconUrl;
    } else {
      const favicon = document.createElement('link');
      favicon.rel = 'icon';
      favicon.href = faviconUrl;
      document.head.appendChild(favicon);
    }
  };

  const updatePageTitle = (companyName: string) => {
    const currentTitle = document.title;
    const separator = ' | ';
    const parts = currentTitle.split(separator);
    
    if (parts.length > 1) {
      document.title = `${parts[0]}${separator}${companyName}`;
    } else {
      document.title = `${currentTitle}${separator}${companyName}`;
    }
  };

  const injectCustomCSS = (customCss: string) => {
    const existingStyle = document.getElementById('custom-branding-css');
    if (existingStyle) {
      existingStyle.textContent = customCss;
    } else {
      const style = document.createElement('style');
      style.id = 'custom-branding-css';
      style.textContent = customCss;
      document.head.appendChild(style);
    }
  };

  return (
    <BrandingContext.Provider value={{ config, brandingConfig: config, isLoading, updateConfig }}>
      {children}
    </BrandingContext.Provider>
  );
}

export function useBranding() {
  const context = useContext(BrandingContext);
  if (context === undefined) {
    throw new Error('useBranding must be used within a BrandingProvider');
  }
  return context;
}

export function BrandingLogo({ className }: { className?: string }) {
  const { config } = useBranding();
  
  if (!config?.logoUrl) {
    return null;
  }

  return (
    <img 
      src={config.logoUrl} 
      alt={config.companyName || 'Logo'}
      className={className}
    />
  );
}

export function BrandingTitle({ fallback }: { fallback: string }) {
  const { config } = useBranding();
  return config?.companyName || fallback;
}

export function BrandingTagline() {
  const { config } = useBranding();
  return config?.tagline || null;
}