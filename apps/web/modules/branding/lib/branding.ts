import { db } from "@repo/database/prisma/client";

export interface BrandingConfig {
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  backgroundColor?: string;
  textColor?: string;
  linkColor?: string;
  buttonColor?: string;
  borderColor?: string;
  logoUrl?: string;
  faviconUrl?: string;
  companyName?: string;
  tagline?: string;
  description?: string;
  darkMode?: boolean;
  hideSystemBranding?: boolean;
  customCss?: string;
  fontFamily?: string;
  borderRadius?: string;
}

export interface DomainConfig {
  customDomain?: string;
  sslEnabled?: boolean;
  redirectWww?: boolean;
}

export async function getBrandingConfig(organizationId: string): Promise<BrandingConfig | null> {
  const organization = await db.organization.findUnique({
    where: { id: organizationId },
    select: { branding: true }
  });

  if (!organization?.branding) {
    return null;
  }

  return organization.branding as BrandingConfig;
}

export async function updateBrandingConfig(
  organizationId: string,
  config: Partial<BrandingConfig>
): Promise<void> {
  const currentConfig = await getBrandingConfig(organizationId) || {};
  const updatedConfig = { ...currentConfig, ...config };

  await db.organization.update({
    where: { id: organizationId },
    data: { branding: updatedConfig }
  });
}

export async function getDomainConfig(organizationId: string): Promise<DomainConfig | null> {
  const organization = await db.organization.findUnique({
    where: { id: organizationId },
    select: { 
      customDomain: true,
      settings: true
    }
  });

  if (!organization) {
    return null;
  }

  const settings = organization.settings as any || {};
  
  return {
    customDomain: organization.customDomain || undefined,
    sslEnabled: settings.sslEnabled || false,
    redirectWww: settings.redirectWww || false
  };
}

export async function updateDomainConfig(
  organizationId: string,
  config: Partial<DomainConfig>
): Promise<void> {
  const currentSettings = await db.organization.findUnique({
    where: { id: organizationId },
    select: { settings: true }
  });

  const settings = currentSettings?.settings as any || {};
  const updatedSettings = {
    ...settings,
    sslEnabled: config.sslEnabled,
    redirectWww: config.redirectWww
  };

  await db.organization.update({
    where: { id: organizationId },
    data: {
      customDomain: config.customDomain,
      settings: updatedSettings
    }
  });
}

export async function enableCustomBranding(organizationId: string): Promise<void> {
  await db.organization.update({
    where: { id: organizationId },
    data: { enableCustomBranding: true }
  });
}

export async function enableCustomDomain(organizationId: string): Promise<void> {
  await db.organization.update({
    where: { id: organizationId },
    data: { enableCustomDomain: true }
  });
}

export function generateCssVariables(config: BrandingConfig): string {
  const variables: string[] = [];

  if (config.primaryColor) variables.push(`--primary: ${config.primaryColor};`);
  if (config.secondaryColor) variables.push(`--secondary: ${config.secondaryColor};`);
  if (config.accentColor) variables.push(`--accent: ${config.accentColor};`);
  if (config.backgroundColor) variables.push(`--background: ${config.backgroundColor};`);
  if (config.textColor) variables.push(`--foreground: ${config.textColor};`);
  if (config.linkColor) variables.push(`--link: ${config.linkColor};`);
  if (config.buttonColor) variables.push(`--button: ${config.buttonColor};`);
  if (config.borderColor) variables.push(`--border: ${config.borderColor};`);
  if (config.fontFamily) variables.push(`--font-family: ${config.fontFamily};`);
  if (config.borderRadius) variables.push(`--border-radius: ${config.borderRadius};`);

  return `:root { ${variables.join(' ')} }`;
}

export function validateDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain);
}

export function validateColor(color: string): boolean {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
  const hslRegex = /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/;
  
  return hexRegex.test(color) || rgbRegex.test(color) || hslRegex.test(color);
}