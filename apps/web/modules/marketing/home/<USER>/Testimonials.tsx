'use client';

import { motion } from 'framer-motion';
import { 
  StarIcon, 
  QuoteIcon,
  TrendingUpIcon,
  ShieldCheckIcon,
  ZapIcon,
  CheckCircleIcon
} from 'lucide-react';
import Image from 'next/image';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CTO",
    company: "TechCommerce",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "A integração foi surpreendentemente simples. Em menos de 2 horas já estávamos processando pagamentos PIX. A documentação é excelente e o suporte técnico respondeu todas nossas dúvidas rapidamente.",
    metrics: {
      label: "Aumento na conversão",
      value: "+35%",
      icon: TrendingUpIcon
    },
    highlight: "Integração em 2 horas"
  },
  {
    id: 2,
    name: "<PERSON> Rodrig<PERSON>",
    role: "Head de Produto",
    company: "MarketPlace Brasil",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "Desde que implementamos a solução, nossa taxa de abandono de carrinho diminuiu drasticamente. Os clientes adoram a facilidade do PIX e a segurança dos pagamentos com cartão.",
    metrics: {
      label: "Redução no abandono",
      value: "-42%",
      icon: CheckCircleIcon
    },
    highlight: "Taxa de abandono -42%"
  },
  {
    id: 3,
    name: "Roberto Santos",
    role: "Founder",
    company: "StartupPay",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "Como startup, precisávamos de uma solução confiável e escalável. O gateway nos permitiu focar no nosso produto principal enquanto eles cuidam de toda a complexidade dos pagamentos.",
    metrics: {
      label: "Uptime garantido",
      value: "99.9%",
      icon: ShieldCheckIcon
    },
    highlight: "Solução escalável"
  },
  {
    id: 4,
    name: "Mariana Costa",
    role: "CFO",
    company: "E-commerce Plus",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "A transparência nas taxas e a rapidez na liquidação dos valores transformaram nosso fluxo de caixa. Recomendo para qualquer empresa que leva pagamentos a sério.",
    metrics: {
      label: "Liquidação mais rápida",
      value: "D+1",
      icon: ZapIcon
    },
    highlight: "Fluxo de caixa otimizado"
  },
  {
    id: 5,
    name: "Felipe Oliveira",
    role: "Tech Lead",
    company: "FinTech Inovação",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "A API é muito bem documentada e os webhooks funcionam perfeitamente. Conseguimos implementar funcionalidades avançadas sem complicações. Excelente para desenvolvedores.",
    metrics: {
      label: "Tempo de integração",
      value: "< 1 dia",
      icon: ZapIcon
    },
    highlight: "API developer-friendly"
  },
  {
    id: 6,
    name: "Juliana Ferreira",
    role: "Head de Growth",
    company: "SaaS Brasileiro",
    avatar: "/api/placeholder/64/64",
    rating: 5,
    content: "O impacto nos nossos KPIs foi imediato. Mais conversões, menos chargebacks e clientes mais satisfeitos. A melhor decisão que tomamos este ano.",
    metrics: {
      label: "Redução em chargebacks",
      value: "-67%",
      icon: ShieldCheckIcon
    },
    highlight: "KPIs melhorados"
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

const TestimonialCard = ({ testimonial, index }: { testimonial: typeof testimonials[0], index: number }) => {
  const MetricIcon = testimonial.metrics.icon;
  
  return (
    <motion.div
      className="group relative p-8 bg-white dark:bg-gray-800 rounded-3xl border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-all duration-500 hover:scale-105"
      variants={itemVariants}
      whileHover={{ y: -10 }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50 dark:from-blue-900/10 dark:to-green-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl" />
      
      <div className="relative z-10">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center text-white font-bold text-xl">
                {testimonial.name.split(' ').map(n => n[0]).join('')}
              </div>
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="w-4 h-4 text-white" />
              </div>
            </div>
            <div>
              <h4 className="font-bold text-gray-900 dark:text-gray-100">
                {testimonial.name}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {testimonial.role} • {testimonial.company}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: testimonial.rating }).map((_, i) => (
              <StarIcon key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            ))}
          </div>
        </div>
        
        <div className="relative mb-6">
          <QuoteIcon className="absolute -top-2 -left-2 w-8 h-8 text-blue-500/20" />
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed pl-6">
            {testimonial.content}
          </p>
        </div>
        
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center">
              <MetricIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {testimonial.metrics.value}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {testimonial.metrics.label}
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium">
              <CheckCircleIcon className="w-3 h-3" />
              {testimonial.highlight}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export function Testimonials() {
  return (
    <section className="py-24 bg-gray-50 dark:bg-gray-800/50">
      <div className="container mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
              <StarIcon className="w-4 h-4 fill-current" />
              Depoimentos
            </div>
          </motion.div>
          
          <motion.h2 
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent"
            variants={itemVariants}
          >
            Empresas que confiam
            <span className="block bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent">
              na nossa solução
            </span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            Mais de 1.000 empresas brasileiras já transformaram seus pagamentos conosco. 
            Veja o que nossos clientes têm a dizer sobre os resultados obtidos.
          </motion.p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {testimonials.map((testimonial, index) => (
            <TestimonialCard 
              key={testimonial.id} 
              testimonial={testimonial} 
              index={index} 
            />
          ))}
        </motion.div>

        <motion.div 
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={itemVariants}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent mb-2">
                1000+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Empresas ativas
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent mb-2">
                R$ 2B+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Processados/mês
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent mb-2">
                99.9%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Uptime garantido
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent mb-2">
                4.9/5
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Satisfação média
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}