'use client'

import { cn } from "@ui/lib"
import { Loader2Icon } from "lucide-react"

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  text?: string
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

export function LoadingSpinner({ size = 'md', className, text }: LoadingSpinnerProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
      <Loader2Icon
        className={cn(
          "animate-spin text-blue-600 dark:text-blue-400",
          sizeClasses[size]
        )}
      />
      {text && (
        <p className="text-sm text-gray-500 dark:text-polar-500 text-center">
          {text}
        </p>
      )}
    </div>
  )
}

// Variante para loading em linha
export function LoadingSpinnerInline({ size = 'sm', className }: LoadingSpinnerProps) {
  return (
    <Loader2Icon
      className={cn(
        "animate-spin text-blue-600 dark:text-blue-400",
        sizeClasses[size],
        className
      )}
    />
  )
}

// Variante para loading de página completa
export function LoadingSpinnerPage({ text = "Carregando..." }: { text?: string }) {
  return (
    <div className="flex h-screen w-full items-center justify-center">
      <LoadingSpinner size="xl" text={text} />
    </div>
  )
}
