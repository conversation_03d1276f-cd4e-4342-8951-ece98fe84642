'use client'

import { cn } from "@ui/lib"
import { Button } from "@ui/components/button"
import { LucideIcon } from "lucide-react"

interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: "default" | "outline" | "secondary" | "ghost"
  }
  className?: string
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className
}: EmptyStateProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center py-12 px-6 text-center",
      className
    )}>
      {Icon && (
        <div className="mb-4 p-3 rounded-full bg-gray-100 dark:bg-polar-700">
          <Icon className="h-8 w-8 text-gray-400 dark:text-polar-500" />
        </div>
      )}

      <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
        {title}
      </h3>

      <p className="mb-6 max-w-sm text-sm text-gray-500 dark:text-polar-500">
        {description}
      </p>

      {/* {action && (
        <Button
          variant={action.variant || "default"}
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      )} */}
    </div>
  )
}

// Variantes específicas para casos comuns
export function EmptyStateNoData({
  title = "Nenhum dado encontrado",
  description = "Não há dados para exibir no momento.",
  action,
  className
}: Omit<EmptyStateProps, 'icon'>) {
  return (
    <EmptyState
      title={title}
      description={description}
      action={action}
      className={className}
    />
  )
}

export function EmptyStateNoResults({
  title = "Nenhum resultado encontrado",
  description = "Tente ajustar os filtros ou termos de busca.",
  action,
  className
}: Omit<EmptyStateProps, 'icon'>) {
  return (
    <EmptyState
      title={title}
      description={description}
      action={action}
      className={className}
    />
  )
}

export function EmptyStateError({
  title = "Erro ao carregar dados",
  description = "Ocorreu um erro ao carregar os dados. Tente novamente.",
  action,
  className
}: Omit<EmptyStateProps, 'icon'>) {
  return (
    <EmptyState
      title={title}
      description={description}
      action={action}
      className={className}
    />
  )
}
