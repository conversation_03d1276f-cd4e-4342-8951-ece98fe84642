'use client'

import { cn } from "@ui/lib"
import { <PERSON><PERSON> } from "@ui/components/button"
import { Badge } from "@ui/components/badge"
import {
  BellIcon,
  CheckIcon,
  ClockIcon,
  AlertTriangleIcon,
  InfoIcon
} from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@ui/components/popover"
import { useState } from "react"

interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  isRead: boolean
}

interface NotificationBellProps {
  className?: string
}

const getNotificationIcon = (type: Notification['type']) => {
  const icons = {
    info: InfoIcon,
    success: CheckIcon,
    warning: ClockIcon,
    error: AlertTriangleIcon
  }
  const Icon = icons[type]
  return <Icon className="h-4 w-4" />
}

const getNotificationColor = (type: Notification['type']) => {
  const colors = {
    info: "text-blue-600 bg-blue-50 dark:bg-blue-900/20",
    success: "text-green-600 bg-green-50 dark:bg-green-900/20",
    warning: "text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20",
    error: "text-red-600 bg-red-50 dark:bg-red-900/20"
  }
  return colors[type]
}

export function NotificationBell({ className }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      title: "Nova venda realizada",
      message: "Venda de R$ 99,90 foi processada com sucesso",
      type: "success",
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutos atrás
      isRead: false
    },
    {
      id: "2",
      title: "Pagamento pendente",
      message: "Aguardando confirmação do pagamento PIX",
      type: "warning",
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atrás
      isRead: false
    },
    {
      id: "3",
      title: "Sistema atualizado",
      message: "Nova funcionalidade disponível no dashboard",
      type: "info",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
      isRead: true
    }
  ])

  const unreadCount = notifications.filter(n => !n.isRead).length

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, isRead: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })))
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn("h-9 w-9 p-0 relative", className)}
        >
          <BellIcon className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4 border-b border-gray-200 dark:border-polar-800">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Notificações
            </h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400"
              >
                Marcar todas como lidas
              </Button>
            )}
          </div>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-8 text-center">
              <BellIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500 dark:text-polar-500">
                Nenhuma notificação
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-polar-800">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "p-4 hover:bg-gray-50 dark:hover:bg-polar-800/50 transition-colors cursor-pointer",
                    !notification.isRead && "bg-blue-50/50 dark:bg-blue-900/10"
                  )}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className={cn(
                      "p-2 rounded-full",
                      getNotificationColor(notification.type)
                    )}>
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h4 className={cn(
                          "text-sm font-medium truncate",
                          notification.isRead
                            ? "text-gray-600 dark:text-polar-400"
                            : "text-gray-900 dark:text-white"
                        )}>
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-400 dark:text-polar-600 ml-2">
                          {notification.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>

                      <p className="text-sm text-gray-500 dark:text-polar-500 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {notifications.length > 0 && (
          <div className="p-4 border-t border-gray-200 dark:border-polar-800">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-gray-600 dark:text-polar-400 hover:text-gray-900 dark:hover:text-white"
            >
              Ver todas as notificações
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  )
}
