"use client";

import Script from "next/script";

const googleTagId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID as string;

export function AnalyticsScript() {
	if (!googleTagId) return null;

	return (
		<>
			<Script
				src={`https://www.googletagmanager.com/gtag/js?id=${googleTagId}`}
				strategy="afterInteractive"
			/>
			<Script id="google-analytics" strategy="afterInteractive">
				{`
					window.dataLayer = window.dataLayer || [];
					function gtag(){dataLayer.push(arguments);}
					gtag('js', new Date());
					gtag('config', '${googleTagId}');
				`}
			</Script>
		</>
	);
}

export function useAnalytics() {
	const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
		if (typeof window !== 'undefined' && (window as any).gtag) {
			(window as any).gtag('event', eventName, parameters);
		}
	};

	return {
		trackEvent,
	};
}
