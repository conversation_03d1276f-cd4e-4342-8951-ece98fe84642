"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
  ReceiptIcon,
  DownloadIcon,
  EyeIcon,
  CreditCardIcon,
  CalendarIcon,
  DollarSignIcon
} from "lucide-react";

interface BillingHistoryProps {
  organizationId: string;
}

export function BillingHistory({ organizationId }: BillingHistoryProps) {
  // Mock data - replace with real API calls
  const billingHistory = [
    {
      id: "INV-001",
      date: "15 de Dezembro, 2024",
      amount: 99.90,
      status: "paid",
      description: "Plano Profissional - Dezembro 2024",
      invoiceUrl: "#"
    },
    {
      id: "INV-002",
      date: "15 de Novembro, 2024",
      amount: 99.90,
      status: "paid",
      description: "Plano Profissional - Novembro 2024",
      invoiceUrl: "#"
    },
    {
      id: "INV-003",
      date: "15 de Outubro, 2024",
      amount: 99.90,
      status: "paid",
      description: "Plano Profissional - Outubro 2024",
      invoiceUrl: "#"
    },
    {
      id: "INV-004",
      date: "15 de Setembro, 2024",
      amount: 79.90,
      status: "paid",
      description: "Plano Básico - Setembro 2024",
      invoiceUrl: "#"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Pago</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Pendente</Badge>;
      case "failed":
        return <Badge className="bg-red-100 text-red-800 border border-red-200">Falhou</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <CreditCardIcon className="h-4 w-4 text-green-500" />;
      case "pending":
        return <CalendarIcon className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <DollarSignIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ReceiptIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ReceiptIcon className="h-5 w-5" />
          Histórico de Cobrança
        </CardTitle>
        <CardDescription>
          Histórico de faturas e pagamentos
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {billingHistory.map((invoice) => (
            <div
              key={invoice.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-muted rounded-full">
                  {getStatusIcon(invoice.status)}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{invoice.description}</p>
                    {getStatusBadge(invoice.status)}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                    <span>#{invoice.id}</span>
                    <span>{invoice.date}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <p className="text-lg font-semibold">
                  R$ {invoice.amount.toFixed(2).replace('.', ',')}
                </p>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <EyeIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <DownloadIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {billingHistory.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <ReceiptIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhuma fatura encontrada</p>
          </div>
        )}

        {billingHistory.length > 0 && (
          <div className="pt-4 border-t">
            <Button variant="outline" className="w-full">
              Ver Histórico Completo
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
