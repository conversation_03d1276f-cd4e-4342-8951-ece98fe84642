"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  WebhookIcon,
  BotIcon,
  ZapIcon,
  GlobeIcon,
  MailIcon,
  DatabaseIcon,
  SettingsIcon,
  ActivityIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
  Trash2Icon,
  EditIcon,
  PlayIcon,
  PauseIcon
} from "lucide-react";

interface IntegrationsListProps {
  organizationId: string;
}

interface ActiveIntegration {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  status: "active" | "inactive" | "error" | "maintenance";
  lastExecution: string;
  successRate: number;
  totalExecutions: number;
  lastError?: string;
  category: string;
  createdAt: string;
  updatedAt: string;
}

// Dados mockados para demonstração - em produção viriam do banco de dados
const mockActiveIntegrations: ActiveIntegration[] = [
  {
    id: "webhook-1",
    name: "Webhook - Novo Usuário",
    description: "Envia notificação quando um novo usuário é criado",
    icon: WebhookIcon,
    status: "active",
    lastExecution: "2 min atrás",
    successRate: 98.5,
    totalExecutions: 1247,
    category: "Automação",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20"
  },
  {
    id: "n8n-1",
    name: "n8n - Sync Dados",
    description: "Sincroniza dados com sistema externo a cada hora",
    icon: BotIcon,
    status: "active",
    lastExecution: "5 min atrás",
    successRate: 95.2,
    totalExecutions: 892,
    category: "Automação",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-19"
  },
  {
    id: "zapier-1",
    name: "Zapier - Slack Notifications",
    description: "Envia notificações para o Slack sobre eventos importantes",
    icon: ZapIcon,
    status: "inactive",
    lastExecution: "2 dias atrás",
    successRate: 87.3,
    totalExecutions: 156,
    category: "Comunicação",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-18"
  },
  {
    id: "api-1",
    name: "API - Consulta Externa",
    description: "Consulta dados de terceiros via API",
    icon: GlobeIcon,
    status: "error",
    lastExecution: "8 min atrás",
    successRate: 76.8,
    totalExecutions: 445,
    lastError: "Timeout na requisição externa",
    category: "Desenvolvimento",
    createdAt: "2024-01-12",
    updatedAt: "2024-01-20"
  },
  {
    id: "email-1",
    name: "Email - Notificações",
    description: "Sistema de notificações por email",
    icon: MailIcon,
    status: "active",
    lastExecution: "10 min atrás",
    successRate: 99.1,
    totalExecutions: 2103,
    category: "Comunicação",
    createdAt: "2024-01-08",
    updatedAt: "2024-01-20"
  }
];

export function IntegrationsList({ organizationId }: IntegrationsListProps) {
  const [integrations, setIntegrations] = useState<ActiveIntegration[]>(mockActiveIntegrations);
  const [filterStatus, setFilterStatus] = useState<string>("all");

  const getStatusIcon = (status: ActiveIntegration["status"]) => {
    switch (status) {
      case "active":
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case "inactive":
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
      case "error":
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case "maintenance":
        return <AlertCircleIcon className="h-4 w-4 text-yellow-500" />;
      default:
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: ActiveIntegration["status"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Ativo</Badge>;
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Inativo</Badge>;
      case "error":
        return <Badge className="bg-red-100 text-red-800 border border-red-200">Erro</Badge>;
      case "maintenance":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Manutenção</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Desconhecido</Badge>;
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 95) return "text-green-600";
    if (rate >= 80) return "text-yellow-600";
    return "text-red-600";
  };

  const filteredIntegrations = integrations.filter(integration => {
    if (filterStatus === "all") return true;
    return integration.status === filterStatus;
  });

  const handleToggleStatus = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => {
      if (integration.id === integrationId) {
        const newStatus = integration.status === "active" ? "inactive" : "active";
        return { ...integration, status: newStatus };
      }
      return integration;
    }));
  };

  const handleDeleteIntegration = (integrationId: string) => {
    if (confirm("Tem certeza que deseja excluir esta integração?")) {
      setIntegrations(prev => prev.filter(integration => integration.id !== integrationId));
    }
  };

  const stats = {
    total: integrations.length,
    active: integrations.filter(i => i.status === "active").length,
    inactive: integrations.filter(i => i.status === "inactive").length,
    error: integrations.filter(i => i.status === "error").length,
    averageSuccessRate: integrations.reduce((acc, i) => acc + i.successRate, 0) / integrations.length
  };

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <WebhookIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Integrações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ativas</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Funcionando</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inativas</CardTitle>
            <XCircleIcon className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.inactive}</div>
            <p className="text-xs text-muted-foreground">Pausadas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Com Erro</CardTitle>
            <XCircleIcon className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.error}</div>
            <p className="text-xs text-muted-foreground">Precisam atenção</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
            <ActivityIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageSuccessRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Média geral</p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Integrações Ativas</CardTitle>
          <CardDescription>
            Gerencie suas integrações configuradas e monitore seu desempenho
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Button
              variant={filterStatus === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterStatus("all")}
            >
              Todas ({stats.total})
            </Button>
            <Button
              variant={filterStatus === "active" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterStatus("active")}
            >
              Ativas ({stats.active})
            </Button>
            <Button
              variant={filterStatus === "inactive" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterStatus("inactive")}
            >
              Inativas ({stats.inactive})
            </Button>
            <Button
              variant={filterStatus === "error" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterStatus("error")}
            >
              Com Erro ({stats.error})
            </Button>
          </div>

          <div className="space-y-4">
            {filteredIntegrations.map((integration) => {
              const IconComponent = integration.icon;
              return (
                <Card key={integration.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <IconComponent className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <CardDescription>{integration.description}</CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(integration.status)}
                        {getStatusBadge(integration.status)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Categoria</p>
                          <p className="font-medium">{integration.category}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Execuções</p>
                          <p className="font-medium">{integration.totalExecutions.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Taxa de Sucesso</p>
                          <p className={`font-medium ${getSuccessRateColor(integration.successRate)}`}>
                            {integration.successRate}%
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Última Execução</p>
                          <p className="font-medium">{integration.lastExecution}</p>
                        </div>
                      </div>

                      {integration.lastError && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-800">
                            <strong>Último erro:</strong> {integration.lastError}
                          </p>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleStatus(integration.id)}
                        >
                          {integration.status === "active" ? (
                            <>
                              <PauseIcon className="h-4 w-4 mr-2" />
                              Pausar
                            </>
                          ) : (
                            <>
                              <PlayIcon className="h-4 w-4 mr-2" />
                              Ativar
                            </>
                          )}
                        </Button>

                        <Button variant="outline" size="sm">
                          <SettingsIcon className="h-4 w-4 mr-2" />
                          Configurar
                        </Button>

                        <Button variant="outline" size="sm">
                          <ActivityIcon className="h-4 w-4 mr-2" />
                          Logs
                        </Button>

                        <Button variant="outline" size="sm">
                          <EditIcon className="h-4 w-4 mr-2" />
                          Editar
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteIntegration(integration.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2Icon className="h-4 w-4 mr-2" />
                          Excluir
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {filteredIntegrations.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Nenhuma integração encontrada com os filtros selecionados.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
