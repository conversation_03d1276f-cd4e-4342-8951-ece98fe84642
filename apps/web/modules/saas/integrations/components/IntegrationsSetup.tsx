"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  ZapIcon,
  WebhookIcon,
  BotIcon,
  GlobeIcon,
  MailIcon,
  DatabaseIcon,
  PlusIcon,
  ExternalLinkIcon
} from "lucide-react";

interface IntegrationsSetupProps {
  organizationId: string;
}

interface IntegrationTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: string;
  status: "available" | "coming-soon" | "beta";
  setupTime: string;
  difficulty: "easy" | "medium" | "hard";
}

const availableIntegrations: IntegrationTemplate[] = [
  {
    id: "webhooks",
    name: "Webhooks",
    description: "Configure webhooks para receber notificações em tempo real",
    icon: WebhookIcon,
    category: "Automação",
    status: "available",
    setupTime: "5 min",
    difficulty: "easy"
  },
  {
    id: "n8n",
    name: "n8n Workflows",
    description: "Crie automações visuais e workflows complexos",
    icon: BotIcon,
    category: "Automação",
    status: "available",
    setupTime: "15 min",
    difficulty: "medium"
  },
  {
    id: "zapier",
    name: "Zapier",
    description: "Conecte com milhares de aplicações populares",
    icon: ZapIcon,
    category: "Integração",
    status: "available",
    setupTime: "10 min",
    difficulty: "easy"
  },
  {
    id: "api",
    name: "API Management",
    description: "Gerencie chaves de API e acessos externos",
    icon: GlobeIcon,
    category: "Desenvolvimento",
    status: "available",
    setupTime: "8 min",
    difficulty: "medium"
  },
  {
    id: "email",
    name: "Email Providers",
    description: "Configure provedores de email personalizados",
    icon: MailIcon,
    category: "Comunicação",
    status: "available",
    setupTime: "12 min",
    difficulty: "easy"
  },
  {
    id: "database",
    name: "Database Sync",
    description: "Sincronize dados com bancos externos",
    icon: DatabaseIcon,
    category: "Dados",
    status: "beta",
    setupTime: "20 min",
    difficulty: "hard"
  }
];

export function IntegrationsSetup({ organizationId }: IntegrationsSetupProps) {
  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);

  const getStatusBadge = (status: IntegrationTemplate["status"]) => {
    switch (status) {
      case "available":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Disponível</Badge>;
      case "coming-soon":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Em Breve</Badge>;
      case "beta":
        return <Badge className="bg-blue-100 text-blue-800 border border-blue-200">Beta</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Desconhecido</Badge>;
    }
  };

  const getDifficultyBadge = (difficulty: IntegrationTemplate["difficulty"]) => {
    switch (difficulty) {
      case "easy":
        return <Badge variant="secondary" className="bg-green-50 text-green-700">Fácil</Badge>;
      case "medium":
        return <Badge variant="secondary" className="bg-yellow-50 text-yellow-700">Médio</Badge>;
      case "hard":
        return <Badge variant="secondary" className="bg-red-50 text-red-700">Difícil</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  const handleSetupIntegration = (integrationId: string) => {
    setSelectedIntegration(integrationId);
    // Aqui você pode implementar a lógica para abrir um modal ou navegar para a página de configuração
    console.log(`Configurando integração: ${integrationId} para organização: ${organizationId}`);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <PlusIcon className="h-5 w-5" />
              Configurar Novas Integrações
            </CardTitle>
            <CardDescription>
              Escolha e configure integrações para automatizar seu fluxo de trabalho
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {availableIntegrations.map((integration) => {
            const IconComponent = integration.icon;
            return (
              <Card
                key={integration.id}
                className={`hover:shadow-lg transition-all duration-200 cursor-pointer ${
                  selectedIntegration === integration.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedIntegration(integration.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <IconComponent className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-base">{integration.name}</CardTitle>
                        <CardDescription className="text-sm">{integration.description}</CardDescription>
                      </div>
                    </div>
                    {getStatusBadge(integration.status)}
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Categoria</span>
                      <span className="font-medium">{integration.category}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Tempo de Configuração</span>
                      <span className="font-medium">{integration.setupTime}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Complexidade</span>
                      {getDifficultyBadge(integration.difficulty)}
                    </div>

                    <Button
                      className="w-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSetupIntegration(integration.id);
                      }}
                      disabled={integration.status !== "available"}
                    >
                      {integration.status === "available" ? (
                        <>
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Configurar
                        </>
                      ) : (
                        <>
                          <ExternalLinkIcon className="h-4 w-4 mr-2" />
                          {integration.status === "coming-soon" ? "Em Breve" : "Beta"}
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {selectedIntegration && (
          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm text-muted-foreground">
              Integração selecionada: <span className="font-medium">{selectedIntegration}</span>
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Clique em "Configurar" para iniciar o processo de configuração
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
