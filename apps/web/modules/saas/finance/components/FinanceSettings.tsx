"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  CreditCardIcon,
  BellIcon,
  ShieldIcon,
  SettingsIcon,
  PlusIcon,
  EditIcon,
  Trash2Icon,
  CheckCircleIcon,
  AlertCircleIcon,

  SmartphoneIcon,
  BuildingIcon,
} from "lucide-react";

interface FinanceSettingsProps {
  organizationId: string;
}

// Mock settings data - replace with real API calls
const mockPaymentProviders = [
  {
    id: "stripe_001",
    name: "Stripe",
    type: "STRIPE",
    status: "ACTIVE",
    isDefault: true,
    config: {
      publicKey: "pk_test_***",
      secretKey: "sk_test_***",
      webhookUrl: "https://api.example.com/webhooks/stripe",
    },
    createdAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "mercadopago_001",
    name: "Mercado Pago",
    type: "MERCADO_PAGO",
    status: "ACTIVE",
    isDefault: false,
    config: {
      accessToken: "APP_USR-***",
      publicKey: "APP_USR-***",
      webhookUrl: "https://api.example.com/webhooks/mercadopago",
    },
    createdAt: "2024-01-10T14:30:00Z",
  },
  {
    id: "pagseguro_001",
    name: "PagSeguro",
    type: "PAGSEGURO",
    status: "INACTIVE",
    isDefault: false,
    config: {
      email: "<EMAIL>",
      token: "***",
      appId: "app123",
    },
    createdAt: "2024-01-05T09:15:00Z",
  },
];

const mockFeeSettings = {
  platformFeePercentage: 3.0,
  minimumFeeAmount: 1.00,
  maximumFeeAmount: 50.00,
  internationalFeePercentage: 1.5,
  chargebackFee: 15.00,
  withdrawalFee: 2.50,
  currency: "BRL",
};

const mockNotificationSettings = {
  emailNotifications: {
    transactionCompleted: true,
    transactionFailed: true,
    withdrawalProcessed: true,
    lowBalance: true,
    monthlyReport: true,
  },
  webhookNotifications: {
    transactionEvents: true,
    balanceUpdates: false,
    reportGeneration: true,
  },
  thresholds: {
    lowBalanceAmount: 1000.00,
    highVolumeAmount: 10000.00,
  },
};

export function FinanceSettings({ organizationId }: FinanceSettingsProps) {
  const [activeTab, setActiveTab] = useState("providers");
  const [feeSettings, setFeeSettings] = useState(mockFeeSettings);
  const [notificationSettings, setNotificationSettings] = useState(mockNotificationSettings);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const getProviderIcon = (type: string) => {
    const icons = {
      STRIPE: CreditCardIcon,
      MERCADO_PAGO: SmartphoneIcon,
      PAGSEGURO: SmartphoneIcon,
      PAYPAL: CreditCardIcon,
    };
    const Icon = icons[type as keyof typeof icons] || BuildingIcon;
    return <Icon className="h-5 w-5" />;
  };

  const getStatusBadge = (status: string) => {
    return status === "ACTIVE" ? (
      <Badge variant="default" className="flex items-center gap-1">
        <CheckCircleIcon className="h-3 w-3" />
        Ativo
      </Badge>
    ) : (
      <Badge variant="secondary" className="flex items-center gap-1">
        <AlertCircleIcon className="h-3 w-3" />
        Inativo
      </Badge>
    );
  };

  const handleSaveSettings = () => {
    console.log("Salvando configurações...");
    // Implement save logic
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="providers">Provedores</TabsTrigger>
          <TabsTrigger value="fees">Taxas</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
          <TabsTrigger value="security">Segurança</TabsTrigger>
        </TabsList>

        {/* Payment Providers */}
        <TabsContent value="providers" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">Provedores de Pagamento</h3>
              <p className="text-sm text-muted-foreground">
                Configure os gateways de pagamento da sua organização
              </p>
            </div>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Adicionar Provedor
            </Button>
          </div>

          <div className="grid gap-4">
            {mockPaymentProviders.map((provider) => (
              <Card key={provider.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getProviderIcon(provider.type)}
                      <div>
                        <CardTitle className="text-base">{provider.name}</CardTitle>
                        <CardDescription>
                          Configurado em {new Date(provider.createdAt).toLocaleDateString('pt-BR')}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {provider.isDefault && (
                        <Badge variant="outline">Padrão</Badge>
                      )}
                      {getStatusBadge(provider.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Tipo: {provider.type} • ID: {provider.id}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <EditIcon className="h-4 w-4 mr-2" />
                        Editar
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2Icon className="h-4 w-4 mr-2" />
                        Remover
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Fee Settings */}
        <TabsContent value="fees" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configuração de Taxas</CardTitle>
              <CardDescription>
                Defina as taxas aplicadas nas transações
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="platformFee">Taxa da Plataforma (%)</Label>
                  <Input
                    id="platformFee"
                    type="number"
                    step="0.1"
                    value={feeSettings.platformFeePercentage}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      platformFeePercentage: parseFloat(e.target.value)
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="internationalFee">Taxa Internacional (%)</Label>
                  <Input
                    id="internationalFee"
                    type="number"
                    step="0.1"
                    value={feeSettings.internationalFeePercentage}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      internationalFeePercentage: parseFloat(e.target.value)
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minFee">Taxa Mínima (R$)</Label>
                  <Input
                    id="minFee"
                    type="number"
                    step="0.01"
                    value={feeSettings.minimumFeeAmount}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      minimumFeeAmount: parseFloat(e.target.value)
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxFee">Taxa Máxima (R$)</Label>
                  <Input
                    id="maxFee"
                    type="number"
                    step="0.01"
                    value={feeSettings.maximumFeeAmount}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      maximumFeeAmount: parseFloat(e.target.value)
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="chargebackFee">Taxa de Chargeback (R$)</Label>
                  <Input
                    id="chargebackFee"
                    type="number"
                    step="0.01"
                    value={feeSettings.chargebackFee}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      chargebackFee: parseFloat(e.target.value)
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="withdrawalFee">Taxa de Saque (R$)</Label>
                  <Input
                    id="withdrawalFee"
                    type="number"
                    step="0.01"
                    value={feeSettings.withdrawalFee}
                    onChange={(e) => setFeeSettings({
                      ...feeSettings,
                      withdrawalFee: parseFloat(e.target.value)
                    })}
                  />
                </div>
              </div>
              <Separator />
              <div className="flex justify-end">
                <Button onClick={handleSaveSettings}>
                  Salvar Configurações
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications */}
        <TabsContent value="notifications" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BellIcon className="h-5 w-5" />
                  Notificações por Email
                </CardTitle>
                <CardDescription>
                  Configure quando receber notificações por email
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(notificationSettings.emailNotifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <Label htmlFor={key} className="text-sm">
                      {key === 'transactionCompleted' && 'Transação Concluída'}
                      {key === 'transactionFailed' && 'Transação Falhou'}
                      {key === 'withdrawalProcessed' && 'Saque Processado'}
                      {key === 'lowBalance' && 'Saldo Baixo'}
                      {key === 'monthlyReport' && 'Relatório Mensal'}
                    </Label>
                    <Switch
                      id={key}
                      checked={value}
                      onCheckedChange={(checked) => setNotificationSettings({
                        ...notificationSettings,
                        emailNotifications: {
                          ...notificationSettings.emailNotifications,
                          [key]: checked
                        }
                      })}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  Configurações de Limite
                </CardTitle>
                <CardDescription>
                  Defina os limites para alertas automáticos
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="lowBalance">Alerta de Saldo Baixo (R$)</Label>
                  <Input
                    id="lowBalance"
                    type="number"
                    step="0.01"
                    value={notificationSettings.thresholds.lowBalanceAmount}
                    onChange={(e) => setNotificationSettings({
                      ...notificationSettings,
                      thresholds: {
                        ...notificationSettings.thresholds,
                        lowBalanceAmount: parseFloat(e.target.value)
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="highVolume">Alerta de Alto Volume (R$)</Label>
                  <Input
                    id="highVolume"
                    type="number"
                    step="0.01"
                    value={notificationSettings.thresholds.highVolumeAmount}
                    onChange={(e) => setNotificationSettings({
                      ...notificationSettings,
                      thresholds: {
                        ...notificationSettings.thresholds,
                        highVolumeAmount: parseFloat(e.target.value)
                      }
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldIcon className="h-5 w-5" />
                Configurações de Segurança
              </CardTitle>
              <CardDescription>
                Gerencie as configurações de segurança financeira
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <ShieldIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Configurações de Segurança</h3>
                <p className="text-muted-foreground mb-6">
                  Configurações avançadas de segurança serão implementadas em breve
                </p>
                <Button variant="outline">
                  <ShieldIcon className="h-4 w-4 mr-2" />
                  Configurar Segurança
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
