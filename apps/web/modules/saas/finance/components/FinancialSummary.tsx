"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  DollarSignIcon,
  TrendingUpIcon,
  ClockIcon,
  DownloadIcon,
  EyeIcon,
  FilterIcon,
  UploadIcon
} from "lucide-react";

interface FinancialSummaryProps {
  organizationId: string;
}

export function FinancialSummary({ organizationId }: FinancialSummaryProps) {
  const financialData = {
    netRevenue: 0,
    availableBalance: 0,
    pendingBalance: 0,
    totalTransactions: 0,
    monthlyGrowth: 0,
    pendingWithdrawals: 0
  };

  return (
    <div className="space-y-6">
      {/* Cartões de Resumo Financeiro */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium"><PERSON><PERSON><PERSON> líquida</CardTitle>
            <DollarSignIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {financialData.netRevenue.toLocaleString('pt-BR')}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo disponível</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {financialData.availableBalance.toLocaleString('pt-BR')}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo pendente</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {financialData.pendingBalance.toLocaleString('pt-BR')}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>
      </div>

      {/* Seção de Extrato */}
      <Card>
        <CardHeader>
          <CardTitle>Extrato</CardTitle>
          <CardDescription>
            Registros de vendas, saques, taxas e comissões
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Abas de Filtro */}
            <div className="flex items-center justify-between">
              <div className="flex space-x-1 bg-muted p-1 rounded-lg">
                <Button variant="default" size="sm" className="h-8 px-3">
                  Todas
                </Button>
                <Button variant="ghost" size="sm" className="h-8 px-3">
                  Entrada
                </Button>
                <Button variant="ghost" size="sm" className="h-8 px-3">
                  Saída
                </Button>
              </div>

              {/* Barra de Ações */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Buscar por código"
                    className="pl-8 pr-3 py-2 border rounded-lg bg-background text-sm w-48"
                  />
                  <DownloadIcon className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                </div>

                <Button variant="outline" size="sm">
                  <FilterIcon className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="sm">
                  <EyeIcon className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="sm">
                  <UploadIcon className="h-4 w-4" />
                </Button>

                <Button disabled variant="outline" size="sm">
                  <DollarSignIcon className="h-4 w-4 mr-2" />
                  Solicitar saque
                </Button>
              </div>
            </div>

            {/* Tabela de Extrato */}
            <div className="border rounded-lg">
              <div className="bg-muted/50 px-4 py-3 border-b">
                <div className="grid grid-cols-5 gap-4 text-sm font-medium">
                  <div className="flex items-center gap-1">
                    Código
                    <span className="text-muted-foreground">?</span>
                  </div>
                  <div>Descrição</div>
                  <div className="flex items-center gap-1">
                    Data
                    <span className="text-muted-foreground">↕</span>
                  </div>
                  <div className="flex items-center gap-1">
                    Valor
                    <span className="text-muted-foreground">↕</span>
                  </div>
                  <div>Categoria</div>
                </div>
              </div>

              {/* Estado Vazio */}
              <div className="py-16 text-center">
                <div className="mx-auto w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📄</span>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2">Ainda não há movimentações</h3>
                <p className="text-muted-foreground">
                  Registros de vendas, saques, taxas e comissões serão listados aqui.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
