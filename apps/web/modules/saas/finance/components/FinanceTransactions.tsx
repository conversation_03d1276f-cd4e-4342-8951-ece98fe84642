"use client";

import { useState } from "react";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { ActionBar } from "@saas/shared/components/ActionBar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON>et, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from "@ui/components/sheet";
import {
  FilterIcon,
  DownloadIcon,
  EyeIcon,
  RefreshCcw,
  ArrowUpIcon,
  ArrowDownIcon,
  CreditCardIcon,
  BanknoteIcon,
  SmartphoneIcon,
  BuildingIcon,
} from "lucide-react";

interface FinanceTransactionsProps {
  organizationId: string;
}

// Mock transaction data - replace with real API calls
const mockTransactions = [
  {
    id: "txn_001",
    type: "CREDIT",
    status: "COMPLETED",
    amountCents: 150000, // R$ 1,500.00
    currency: "BRL",
    description: "Venda - Curso Premium de Marketing Digital",
    paymentMethod: "PIX",
    fromUser: "João Silva",
    toUser: null,
    externalId: "pix_123456789",
    processedAt: "2024-01-20T10:30:00Z",
    settledAt: "2024-01-20T10:31:00Z",
    createdAt: "2024-01-20T10:30:00Z",
    fees: [
      { type: "PLATFORM_FEE", amountCents: 4500, percentage: 3.0 }
    ]
  },
  {
    id: "txn_002",
    type: "DEBIT",
    status: "COMPLETED",
    amountCents: 4500, // R$ 45.00
    currency: "BRL",
    description: "Taxa de Plataforma",
    paymentMethod: "PLATFORM_FEE",
    fromUser: null,
    toUser: null,
    externalId: null,
    processedAt: "2024-01-20T10:31:00Z",
    settledAt: "2024-01-20T10:31:00Z",
    createdAt: "2024-01-20T10:31:00Z",
    fees: []
  },
  {
    id: "txn_003",
    type: "CREDIT",
    status: "PENDING",
    amountCents: 250000, // R$ 2,500.00
    currency: "BRL",
    description: "Venda - Consultoria Empresarial",
    paymentMethod: "CREDIT_CARD",
    fromUser: "Maria Santos",
    toUser: null,
    externalId: "cc_987654321",
    processedAt: null,
    settledAt: null,
    createdAt: "2024-01-20T08:45:00Z",
    fees: [
      { type: "PAYMENT_PROCESSOR_FEE", amountCents: 7250, percentage: 2.9 },
      { type: "PLATFORM_FEE", amountCents: 7500, percentage: 3.0 }
    ]
  },
  {
    id: "txn_004",
    type: "TRANSFER",
    status: "COMPLETED",
    amountCents: 50000, // R$ 500.00
    currency: "BRL",
    description: "Comissão de Afiliado",
    paymentMethod: "INTERNAL",
    fromUser: "Sistema",
    toUser: "Carlos Oliveira",
    externalId: null,
    processedAt: "2024-01-19T15:20:00Z",
    settledAt: "2024-01-19T15:20:00Z",
    createdAt: "2024-01-19T15:20:00Z",
    fees: []
  },
  {
    id: "txn_005",
    type: "REFUND",
    status: "FAILED",
    amountCents: 120000, // R$ 1,200.00
    currency: "BRL",
    description: "Estorno - Curso Básico",
    paymentMethod: "PIX",
    fromUser: null,
    toUser: "Ana Costa",
    externalId: "refund_456789",
    processedAt: "2024-01-19T12:10:00Z",
    settledAt: null,
    createdAt: "2024-01-19T12:00:00Z",
    fees: []
  }
];

export function FinanceTransactions({ organizationId }: FinanceTransactionsProps) {
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  const formatCurrency = (amountCents: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amountCents / 100);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "—";
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Concluída", variant: "default" as const },
      PENDING: { label: "Pendente", variant: "secondary" as const },
      PROCESSING: { label: "Processando", variant: "outline" as const },
      FAILED: { label: "Falhou", variant: "destructive" as const },
      CANCELLED: { label: "Cancelada", variant: "destructive" as const },
      REVERSED: { label: "Revertida", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      CREDIT: { label: "Crédito", variant: "default" as const, icon: ArrowUpIcon },
      DEBIT: { label: "Débito", variant: "secondary" as const, icon: ArrowDownIcon },
      TRANSFER: { label: "Transferência", variant: "outline" as const, icon: ArrowUpIcon },
      REFUND: { label: "Estorno", variant: "destructive" as const, icon: ArrowDownIcon },
      FEE: { label: "Taxa", variant: "secondary" as const, icon: ArrowDownIcon },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.CREDIT;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getPaymentMethodIcon = (method: string) => {
    const methodIcons = {
      PIX: SmartphoneIcon,
      CREDIT_CARD: CreditCardIcon,
      DEBIT_CARD: CreditCardIcon,
      BANK_TRANSFER: BuildingIcon,
      CASH: BanknoteIcon,
      PLATFORM_FEE: BuildingIcon,
      INTERNAL: BuildingIcon,
    };

    const Icon = methodIcons[method as keyof typeof methodIcons] || CreditCardIcon;
    return <Icon className="h-4 w-4" />;
  };

  const columns: DataTableColumn<any>[] = [
    {
      key: "createdAt",
      label: "Data",
      sortable: true,
      render: (_, transaction) => (
        <div className="text-sm">
          {formatDate(transaction.createdAt)}
        </div>
      ),
    },
    {
      key: "type",
      label: "Tipo",
      render: (_, transaction) => getTypeBadge(transaction.type),
    },
    {
      key: "description",
      label: "Descrição",
      render: (_, transaction) => (
        <div>
          <div className="font-medium text-sm">{transaction.description}</div>
          <div className="text-xs text-muted-foreground">
            ID: {transaction.id}
          </div>
        </div>
      ),
    },
    {
      key: "paymentMethod",
      label: "Método",
      render: (_, transaction) => (
        <div className="flex items-center gap-2">
          {getPaymentMethodIcon(transaction.paymentMethod)}
          <span className="text-sm">{transaction.paymentMethod}</span>
        </div>
      ),
    },
    {
      key: "amountCents",
      label: "Valor",
      sortable: true,
      render: (_, transaction) => (
        <div className={`text-sm font-medium ${
          transaction.type === "CREDIT" ? "text-green-600" : "text-red-600"
        }`}>
          {transaction.type === "CREDIT" ? "+" : "-"}
          {formatCurrency(transaction.amountCents)}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (_, transaction) => getStatusBadge(transaction.status),
    },
    {
      key: "settledAt",
      label: "Liquidação",
      render: (_, transaction) => (
        <div className="text-sm text-muted-foreground">
          {formatDate(transaction.settledAt)}
        </div>
      ),
    },
  ];

  const actions: DataTableAction<any>[] = [
    {
      label: "Ver Detalhes",
      icon: EyeIcon,
      onClick: (transaction) => {
        console.log("Ver detalhes:", transaction);
      },
    },
    {
      label: "Reprocessar",
      icon: RefreshCcw,
      onClick: (transaction) => {
        console.log("Reprocessar:", transaction);
      },
      show: (transaction) => transaction.status === "FAILED",
    },
  ];

  const bulkActions = [
    {
      label: "Exportar Selecionadas",
      icon: DownloadIcon,
      onClick: (selectedIds: string[]) => {
        console.log("Exportar:", selectedIds);
      },
    },
    {
      label: "Reprocessar Selecionadas",
      icon: RefreshCcw,
      onClick: (selectedIds: string[]) => {
        console.log("Reprocessar:", selectedIds);
      },
    },
  ];

  const filteredTransactions = mockTransactions.filter(transaction =>
    transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (transaction.fromUser && transaction.fromUser.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <ActionBar
        searchPlaceholder="Buscar transações..."
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        selectedCount={selectedTransactions.length}
        onClearSelection={() => setSelectedTransactions([])}
        actions={
          <div className="flex items-center gap-2">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  Filtros
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filtros de Transação</SheetTitle>
                </SheetHeader>
                <div className="py-4">
                  <p className="text-sm text-muted-foreground">
                    Filtros avançados serão implementados aqui
                  </p>
                </div>
              </SheetContent>
            </Sheet>
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        }
        bulkActions={bulkActions}
      />

      <DataTable
        data={filteredTransactions}
        columns={columns}
        actions={actions}
        selectedItems={selectedTransactions}
        onSelectionChange={setSelectedTransactions}
        emptyMessage="Nenhuma transação encontrada"
        loadingMessage="Carregando transações..."
      />
    </div>
  );
}
