"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  TrendingUpIcon,
  TrendingDownIcon,
  DollarSignIcon,
  CreditCardIcon,
  ClockIcon,
  AlertTriangleIcon,
  BarChart3Icon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PiggyBankIcon,
  WalletIcon,
  RefreshCcw,
} from "lucide-react";

interface FinanceOverviewProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockMetrics = {
  totalBalance: 125430.50,
  availableBalance: 98750.25,
  pendingBalance: 26680.25,
  totalRevenue: 542890.75,
  monthlyRevenue: 45230.80,
  totalFees: 12450.30,
  totalWithdraws: 89650.40,
  transactionCount: 2847,
  pendingTransactions: 23,
  failedTransactions: 5,
};

const mockRecentTransactions = [
  {
    id: "1",
    type: "CREDIT",
    description: "Venda - Curso Premium",
    amount: 1500.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T10:30:00Z",
    paymentMethod: "PIX",
  },
  {
    id: "2",
    type: "DEBIT",
    description: "Taxa de Processamento",
    amount: -45.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T09:15:00Z",
    paymentMethod: "PLATFORM_FEE",
  },
  {
    id: "3",
    type: "CREDIT",
    description: "Venda - Consultoria",
    amount: 2500.00,
    status: "PENDING",
    createdAt: "2024-01-20T08:45:00Z",
    paymentMethod: "CREDIT_CARD",
  },
];

export function FinanceOverview({ organizationId }: FinanceOverviewProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Concluída", variant: "default" as const },
      PENDING: { label: "Pendente", variant: "secondary" as const },
      FAILED: { label: "Falhou", variant: "destructive" as const },
      PROCESSING: { label: "Processando", variant: "outline" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTransactionIcon = (type: string) => {
    return type === "CREDIT" ? (
      <ArrowUpIcon className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownIcon className="h-4 w-4 text-red-600" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Financial Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Total</CardTitle>
            <WalletIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(mockMetrics.totalBalance)}
            </div>
            <p className="text-xs text-muted-foreground">
              Disponível: {formatCurrency(mockMetrics.availableBalance)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita Mensal</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(mockMetrics.monthlyRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.5% em relação ao mês anterior
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendente</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {formatCurrency(mockMetrics.pendingBalance)}
            </div>
            <p className="text-xs text-muted-foreground">
              {mockMetrics.pendingTransactions} transações pendentes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxas Totais</CardTitle>
            <CreditCardIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(mockMetrics.totalFees)}
            </div>
            <p className="text-xs text-muted-foreground">
              2.3% da receita total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions and Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Últimas movimentações financeiras
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                Ver Todas
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <p className="text-sm font-medium">{transaction.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(transaction.createdAt)} • {transaction.paymentMethod}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-medium ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(Math.abs(transaction.amount))}
                    </p>
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Ações Rápidas</CardTitle>
            <CardDescription>
              Operações financeiras mais utilizadas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <Button className="w-full justify-start" variant="outline">
                <PiggyBankIcon className="h-4 w-4 mr-2" />
                Solicitar Saque
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <BarChart3Icon className="h-4 w-4 mr-2" />
                Gerar Relatório
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <RefreshCcw  className="h-4 w-4 mr-2" />
                Reconciliar Contas
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <CalendarIcon className="h-4 w-4 mr-2" />
                Agendar Pagamento
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Financial Alerts */}
      {(mockMetrics.failedTransactions > 0 || mockMetrics.pendingTransactions > 20) && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-yellow-800">
              <AlertTriangleIcon className="h-5 w-5" />
              <span>Alertas Financeiros</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {mockMetrics.failedTransactions > 0 && (
                <p className="text-sm text-yellow-700">
                  • {mockMetrics.failedTransactions} transações falharam e precisam de atenção
                </p>
              )}
              {mockMetrics.pendingTransactions > 20 && (
                <p className="text-sm text-yellow-700">
                  • {mockMetrics.pendingTransactions} transações pendentes aguardando processamento
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
