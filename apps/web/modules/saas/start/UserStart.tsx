"use client";

import { config } from "@repo/config";
import { OrganizationsGrid } from "@saas/organizations/components/OrganizationsGrid";
import { Card } from "@ui/components/card";
import { Suspense } from "react";

export default function UserStart() {
	return (
		<div>
			{config.organizations.enable && (
				<Suspense fallback={<div>Carregando organizações...</div>}>
					<OrganizationsGrid />
				</Suspense>
			)}

			<Card className="mt-6">
				<div className="flex h-64 items-center justify-center p-8 text-foreground/60">
					Place your content here...
				</div>
			</Card>
		</div>
	);
}
