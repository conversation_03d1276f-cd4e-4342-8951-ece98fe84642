"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Progress } from "@ui/components/progress";

export function PaymentMethodsGrid() {
	const paymentMethods = [
		{
			name: "Cartão de crédito",
			percentage: 0,
			amount: "R$ 0",
			icon: "💳",
		},
		{
			name: "PIX",
			percentage: 0,
			amount: "R$ 0",
			icon: "🏦",
		},
		{
			name: "Boleto",
			percentage: 0,
			amount: "R$ 0",
			icon: "📄",
		},
		{
			name: "PayPal",
			percentage: 0,
			amount: "R$ 0",
			icon: "💰",
		},
		{
			name: "Apple Pay",
			percentage: 0,
			amount: "R$ 0",
			icon: "🍎",
		},
		{
			name: "Google Pay",
			percentage: 0,
			amount: "R$ 0",
			icon: "🔍",
		},
		{
			name: "Samsung Pay",
			percentage: 0,
			amount: "R$ 0",
			icon: "📱",
		},
	];

	return (
		<Card>
			<CardHeader>
				<CardTitle>Métodos de Pagamento</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					{paymentMethods.map((method) => (
						<div key={method.name} className="space-y-2">
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-2">
									<span className="text-lg">{method.icon}</span>
									<span className="text-sm font-medium">{method.name}</span>
								</div>
								<span className="text-xs text-muted-foreground">
									{method.percentage}%
								</span>
							</div>
							<Progress value={method.percentage} className="h-2" />
							<p className="text-xs text-muted-foreground">{method.amount}</p>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}