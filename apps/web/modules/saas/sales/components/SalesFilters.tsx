"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Sheet, <PERSON>et<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { Calendar } from "@ui/components/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Checkbox } from "@ui/components/checkbox";
import { Label } from "@ui/components/label";
import {
  FilterIcon,
  SearchIcon,
  CalendarIcon,
  XIcon,
  DownloadIcon,
  EyeIcon,
  UploadIcon
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@ui/lib";

interface SalesFiltersProps {
  organizationId: string;
  onFiltersChange?: (filters: SalesFilterState) => void;
  onExport?: () => void;
}

export interface SalesFilterState {
  searchTerm: string;
  productIds: string[];
  status: string[];
  paymentMethods: string[];
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  amountRange: {
    min: number | null;
    max: number | null;
  };
  sellerIds: string[];
  buyerSearch: string;
}

const statusOptions = [
  { id: "pending", name: "Pendente", color: "bg-yellow-100 text-yellow-800" },
  { id: "approved", name: "Aprovado", color: "bg-green-100 text-green-800" },
  { id: "failed", name: "Falhou", color: "bg-red-100 text-red-800" },
  { id: "refunded", name: "Reembolsado", color: "bg-gray-100 text-gray-800" },
  { id: "cancelled", name: "Cancelado", color: "bg-gray-100 text-gray-800" },
];

const paymentMethodOptions = [
  { id: "pix", name: "PIX" },
  { id: "card", name: "Cartão" },
  { id: "boleto", name: "Boleto" },
  { id: "transfer", name: "Transferência" },
];

// Mock products - in real implementation, fetch from API
const mockProducts = [
  { id: "1", name: "Produto Premium" },
  { id: "2", name: "Curso Online" },
  { id: "3", name: "Consultoria" },
  { id: "4", name: "E-book" },
  { id: "5", name: "Workshop" },
];

// Mock sellers - in real implementation, fetch from API
const mockSellers = [
  { id: "1", name: "Equipe de Vendas" },
  { id: "2", name: "Marketing" },
  { id: "3", name: "Parceiros" },
];

export function SalesFilters({ organizationId, onFiltersChange, onExport }: SalesFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<SalesFilterState>({
    searchTerm: "",
    productIds: [],
    status: [],
    paymentMethods: [],
    dateRange: { from: undefined, to: undefined },
    amountRange: { min: null, max: null },
    sellerIds: [],
    buyerSearch: "",
  });

  const updateFilters = (newFilters: Partial<SalesFilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters: SalesFilterState = {
      searchTerm: "",
      productIds: [],
      status: [],
      paymentMethods: [],
      dateRange: { from: undefined, to: undefined },
      amountRange: { min: null, max: null },
      sellerIds: [],
      buyerSearch: "",
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.productIds.length > 0) count++;
    if (filters.status.length > 0) count++;
    if (filters.paymentMethods.length > 0) count++;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) count++;
    if (filters.sellerIds.length > 0) count++;
    if (filters.buyerSearch) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Search and Quick Actions */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2 flex-1">
          {/* Search Input */}
          <div className="relative flex-1 max-w-md">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar por CPF, ID da transação, e-mail ou nome"
              value={filters.searchTerm}
              onChange={(e) => updateFilters({ searchTerm: e.target.value })}
              className="pl-10"
            />
          </div>

          {/* Product Filter */}
          <Select
            value={filters.productIds.length === 1 ? filters.productIds[0] : "all"}
            onValueChange={(value) =>
              updateFilters({ productIds: value === "all" ? [] : [value] })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Todos os produtos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os produtos</SelectItem>
              {mockProducts.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Advanced Filters Button */}
          <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="relative">
                <FilterIcon className="h-4 w-4 mr-2" />
                Filtros
                {activeFiltersCount > 0 && (
                  <Badge status="info" className="ml-2 h-5 w-5 p-0 text-xs">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="w-[500px] sm:w-[600px]">
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <FilterIcon className="h-5 w-5" />
                  Filtros Avançados
                </SheetTitle>
              </SheetHeader>

              <div className="space-y-6 mt-6 max-h-[70vh] overflow-y-auto">
                {/* Search by Buyer */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Buscar por Comprador</Label>
                  <Input
                    placeholder="Nome, e-mail ou CPF do comprador"
                    value={filters.buyerSearch}
                    onChange={(e) => updateFilters({ buyerSearch: e.target.value })}
                  />
                </div>

                {/* Date Range Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Período da Venda</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !filters.dateRange.from && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.from ? (
                            format(filters.dateRange.from, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            "Data inicial"
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={filters.dateRange.from || undefined}
                          onSelect={(date) => updateFilters({ dateRange: { ...filters.dateRange, from: date } })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !filters.dateRange.to && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.to ? (
                            format(filters.dateRange.to, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            "Data final"
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={filters.dateRange.to || undefined}
                          onSelect={(date) => updateFilters({ dateRange: { ...filters.dateRange, to: date } })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Amount Range Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Faixa de Valor</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="Valor mínimo"
                      value={filters.amountRange.min || ""}
                      onChange={(e) => updateFilters({
                        amountRange: {
                          ...filters.amountRange,
                          min: e.target.value ? parseFloat(e.target.value) : null
                        }
                      })}
                    />
                    <Input
                      type="number"
                      placeholder="Valor máximo"
                      value={filters.amountRange.max || ""}
                      onChange={(e) => updateFilters({
                        amountRange: {
                          ...filters.amountRange,
                          max: e.target.value ? parseFloat(e.target.value) : null
                        }
                      })}
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Status da Transação</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {statusOptions.map((status) => (
                      <div key={status.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-${status.id}`}
                          checked={filters.status.includes(status.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilters({ status: [...filters.status, status.id] });
                            } else {
                              updateFilters({
                                status: filters.status.filter(s => s !== status.id)
                              });
                            }
                          }}
                        />
                        <Label htmlFor={`status-${status.id}`} className="text-sm">
                          {status.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Payment Methods Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Método de Pagamento</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {paymentMethodOptions.map((method) => (
                      <div key={method.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`method-${method.id}`}
                          checked={filters.paymentMethods.includes(method.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilters({
                                paymentMethods: [...filters.paymentMethods, method.id]
                              });
                            } else {
                              updateFilters({
                                paymentMethods: filters.paymentMethods.filter(m => m !== method.id)
                              });
                            }
                          }}
                        />
                        <Label htmlFor={`method-${method.id}`} className="text-sm">
                          {method.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Sellers Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Vendedor</Label>
                  <div className="space-y-2">
                    {mockSellers.map((seller) => (
                      <div key={seller.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`seller-${seller.id}`}
                          checked={filters.sellerIds.includes(seller.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFilters({ sellerIds: [...filters.sellerIds, seller.id] });
                            } else {
                              updateFilters({
                                sellerIds: filters.sellerIds.filter(s => s !== seller.id)
                              });
                            }
                          }}
                        />
                        <Label htmlFor={`seller-${seller.id}`} className="text-sm">
                          {seller.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Clear Filters */}
                {activeFiltersCount > 0 && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="w-full"
                  >
                    <XIcon className="h-4 w-4 mr-2" />
                    Limpar Filtros
                  </Button>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {/* View Options Button */}
          <Button variant="outline" size="sm">
            <EyeIcon className="h-4 w-4" />
          </Button>

          {/* Export Button */}
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <UploadIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Filtros ativos:</span>

          {filters.searchTerm && (
            <Badge status="info" className="gap-1">
              Busca: {filters.searchTerm}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ searchTerm: "" })}
              />
            </Badge>
          )}

          {filters.buyerSearch && (
            <Badge status="info" className="gap-1">
              Comprador: {filters.buyerSearch}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ buyerSearch: "" })}
              />
            </Badge>
          )}

          {filters.productIds.length > 0 && (
            <Badge status="info" className="gap-1">
              Produtos: {filters.productIds.length}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ productIds: [] })}
              />
            </Badge>
          )}

          {filters.status.length > 0 && (
            <Badge status="info" className="gap-1">
              Status: {filters.status.length}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ status: [] })}
              />
            </Badge>
          )}

          {filters.paymentMethods.length > 0 && (
            <Badge status="info" className="gap-1">
              Métodos: {filters.paymentMethods.length}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ paymentMethods: [] })}
              />
            </Badge>
          )}

          {filters.sellerIds.length > 0 && (
            <Badge status="info" className="gap-1">
              Vendedores: {filters.sellerIds.length}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ sellerIds: [] })}
              />
            </Badge>
          )}

          {(filters.dateRange.from || filters.dateRange.to) && (
            <Badge status="info" className="gap-1">
              Período: {filters.dateRange.from && format(filters.dateRange.from, "dd/MM", { locale: ptBR })}
              {filters.dateRange.to && ` - ${format(filters.dateRange.to, "dd/MM", { locale: ptBR })}`}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ dateRange: { from: undefined, to: undefined } })}
              />
            </Badge>
          )}

          {(filters.amountRange.min !== null || filters.amountRange.max !== null) && (
            <Badge status="info" className="gap-1">
              Valor: {filters.amountRange.min !== null ? `R$ ${filters.amountRange.min}` : "R$ 0"}
              {filters.amountRange.max !== null && ` - R$ ${filters.amountRange.max}`}
              <XIcon
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ amountRange: { min: null, max: null } })}
              />
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-6 px-2 text-xs"
          >
            Limpar todos
          </Button>
        </div>
      )}
    </div>
  );
}
