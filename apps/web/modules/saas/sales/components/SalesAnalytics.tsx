"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  DollarSignIcon,
  TrendingUpIcon,
  ShoppingCartIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CalendarIcon,
  UsersIcon
} from "lucide-react";

interface SalesAnalyticsProps {
  organizationId: string;
  productIds?: string[];
}

interface MetricData {
  value: number;
  previousValue: number;
  change: number;
  changePercent: number;
  trend: "up" | "down" | "neutral";
}

interface SalesMetrics {
  totalOrders: MetricData;
  totalRevenue: MetricData;
  todayRevenue: MetricData;
  averageOrderValue: MetricData;
  conversionRate: MetricData;
  refundRate: MetricData;
}

// Mock data - in real implementation, fetch from API
const mockMetrics: SalesMetrics = {
  totalOrders: {
    value: 1247,
    previousValue: 1156,
    change: 91,
    changePercent: 7.9,
    trend: "up"
  },
  totalRevenue: {
    value: 89750,
    previousValue: 82340,
    change: 7410,
    changePercent: 9.0,
    trend: "up"
  },
  todayRevenue: {
    value: 2890,
    previousValue: 3120,
    change: -230,
    changePercent: -7.4,
    trend: "down"
  },
  averageOrderValue: {
    value: 7200,
    previousValue: 7125,
    change: 75,
    changePercent: 1.1,
    trend: "up"
  },
  conversionRate: {
    value: 3.2,
    previousValue: 2.8,
    change: 0.4,
    changePercent: 14.3,
    trend: "up"
  },
  refundRate: {
    value: 1.8,
    previousValue: 2.3,
    change: -0.5,
    changePercent: -21.7,
    trend: "up" // Lower refund rate is good
  }
};

function MetricCard({
  title,
  icon: Icon,
  metric,
  format = "currency",
  suffix = ""
}: {
  title: string;
  icon: any;
  metric: MetricData;
  format?: "currency" | "number" | "percentage";
  suffix?: string;
}) {
  const formatValue = (value: number) => {
    switch (format) {
      case "currency":
        return new Intl.NumberFormat("pt-BR", {
          style: "currency",
          currency: "BRL",
        }).format(value / 100);
      case "percentage":
        return `${value.toFixed(1)}%`;
      case "number":
        return value.toLocaleString("pt-BR");
      default:
        return value.toString();
    }
  };

  const isPositive = metric.trend === "up";
  const TrendIcon = isPositive ? ArrowUpIcon : ArrowDownIcon;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {formatValue(metric.value)}{suffix}
        </div>
        <div className="flex items-center text-xs text-muted-foreground mt-1">
          <TrendIcon className={`h-3 w-3 mr-1 ${
            isPositive ? "text-green-600" : "text-red-600"
          }`} />
          <span className={isPositive ? "text-green-600" : "text-red-600"}>
            {Math.abs(metric.changePercent).toFixed(1)}%
          </span>
          <span className="ml-1">vs período anterior</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function SalesAnalytics({ organizationId, productIds }: SalesAnalyticsProps) {
  const [metrics, setMetrics] = useState<SalesMetrics>(mockMetrics);
  const [isLoading, setIsLoading] = useState(false);

  // In real implementation, fetch metrics based on organizationId and productIds
  useEffect(() => {
    // Simulate API call
    setIsLoading(true);
    const timer = setTimeout(() => {
      setMetrics(mockMetrics);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [organizationId, productIds]);

  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-3">
        <MetricCard
          title="Total de Pedidos"
          icon={ShoppingCartIcon}
          metric={metrics.totalOrders}
          format="number"
        />

        <MetricCard
          title="Receita de Hoje"
          icon={CalendarIcon}
          metric={metrics.todayRevenue}
          format="currency"
        />

        <MetricCard
          title="Receita Total"
          icon={DollarSignIcon}
          metric={metrics.totalRevenue}
          format="currency"
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <MetricCard
          title="Ticket Médio"
          icon={TrendingUpIcon}
          metric={metrics.averageOrderValue}
          format="currency"
        />

        <MetricCard
          title="Taxa de Conversão"
          icon={UsersIcon}
          metric={metrics.conversionRate}
          format="percentage"
        />

        <MetricCard
          title="Taxa de Reembolso"
          icon={ArrowDownIcon}
          metric={metrics.refundRate}
          format="percentage"
        />
      </div>
    </div>
  );
}

   