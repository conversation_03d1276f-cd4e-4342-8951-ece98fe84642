"use client";

import { useState, useEffect } from "react";

export interface MetricData {
  value: number;
  previousValue: number;
  change: number;
  changePercent: number;
  trend: "up" | "down" | "neutral";
}

export interface SalesMetrics {
  totalOrders: MetricData;
  totalRevenue: MetricData;
  todayRevenue: MetricData;
  averageOrderValue: MetricData;
  conversionRate: MetricData;
  refundRate: MetricData;
}

export interface UseSalesMetricsOptions {
  organizationId: string;
  productIds?: string[];
  startDate?: Date;
  endDate?: Date;
  interval?: "day" | "week" | "month" | "year";
}

// Mock data generator for metrics
const generateMockMetrics = (organizationId: string, productIds?: string[]): SalesMetrics => {
  // Base values that would come from your database
  const baseRevenue = 89750;
  const baseOrders = 1247;
  const baseTodayRevenue = 2890;
  
  // Apply product filter effect (if specific products selected, show subset of data)
  const productMultiplier = productIds && productIds.length > 0 ? 0.6 : 1;
  
  return {
    totalOrders: {
      value: Math.floor(baseOrders * productMultiplier),
      previousValue: Math.floor(1156 * productMultiplier),
      change: Math.floor(91 * productMultiplier),
      changePercent: 7.9,
      trend: "up"
    },
    totalRevenue: {
      value: Math.floor(baseRevenue * productMultiplier),
      previousValue: Math.floor(82340 * productMultiplier),
      change: Math.floor(7410 * productMultiplier),
      changePercent: 9.0,
      trend: "up"
    },
    todayRevenue: {
      value: Math.floor(baseTodayRevenue * productMultiplier),
      previousValue: Math.floor(3120 * productMultiplier),
      change: Math.floor(-230 * productMultiplier),
      changePercent: -7.4,
      trend: "down"
    },
    averageOrderValue: {
      value: 7200,
      previousValue: 7125,
      change: 75,
      changePercent: 1.1,
      trend: "up"
    },
    conversionRate: {
      value: 3.2,
      previousValue: 2.8,
      change: 0.4,
      changePercent: 14.3,
      trend: "up"
    },
    refundRate: {
      value: 1.8,
      previousValue: 2.3,
      change: -0.5,
      changePercent: -21.7,
      trend: "up" // Lower refund rate is good, so trend is "up"
    }
  };
};

export function useSalesMetrics({
  organizationId,
  productIds,
  startDate,
  endDate,
  interval = "month"
}: UseSalesMetricsOptions) {
  const [data, setData] = useState<SalesMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 300));

        // In real implementation, this would be an API call to your metrics endpoint
        // const response = await fetch(`/api/organizations/${organizationId}/sales/metrics`, {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     productIds,
        //     startDate,
        //     endDate,
        //     interval
        //   })
        // });
        // const metrics = await response.json();

        const metrics = generateMockMetrics(organizationId, productIds);
        setData(metrics);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch metrics");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [organizationId, productIds, startDate, endDate, interval]);

  return {
    data,
    isLoading,
    error,
    refetch: () => {
      setIsLoading(true);
      // This will trigger the useEffect
    },
  };
}

// Hook for real-time metrics (could be used for dashboard updates)
export function useRealtimeSalesMetrics(organizationId: string) {
  const [metrics, setMetrics] = useState<Partial<SalesMetrics>>({});
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // In a real implementation, you might use WebSocket or Server-Sent Events
    // for real-time updates
    
    // Simulate real-time updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        todayRevenue: {
          value: (prev.todayRevenue?.value || 2890) + Math.floor(Math.random() * 100),
          previousValue: prev.todayRevenue?.previousValue || 3120,
          change: prev.todayRevenue?.change || -230,
          changePercent: prev.todayRevenue?.changePercent || -7.4,
          trend: "up" as const
        }
      }));
    }, 30000); // Update every 30 seconds

    setIsConnected(true);

    return () => {
      clearInterval(interval);
      setIsConnected(false);
    };
  }, [organizationId]);

  return {
    metrics,
    isConnected,
  };
}

// Utility function to format metric values
export function formatMetricValue(
  value: number,
  format: "currency" | "number" | "percentage" = "number",
  currency = "BRL"
): string {
  switch (format) {
    case "currency":
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency,
      }).format(value / 100); // Assuming values are in cents
    case "percentage":
      return `${value.toFixed(1)}%`;
    case "number":
      return value.toLocaleString("pt-BR");
    default:
      return value.toString();
  }
}

// Utility function to get metric trend color
export function getMetricTrendColor(trend: "up" | "down" | "neutral"): string {
  switch (trend) {
    case "up":
      return "text-green-600";
    case "down":
      return "text-red-600";
    case "neutral":
      return "text-gray-600";
    default:
      return "text-gray-600";
  }
}

// Utility function to calculate date ranges for metrics
export function getMetricDateRange(period: "today" | "week" | "month" | "quarter" | "year" | "all_time") {
  const now = new Date();
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  switch (period) {
    case "today":
      return {
        start: startOfDay,
        end: now,
        interval: "hour" as const
      };
    case "week":
      const startOfWeek = new Date(startOfDay);
      startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
      return {
        start: startOfWeek,
        end: now,
        interval: "day" as const
      };
    case "month":
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      return {
        start: startOfMonth,
        end: now,
        interval: "day" as const
      };
    case "quarter":
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
      return {
        start: quarterStart,
        end: now,
        interval: "week" as const
      };
    case "year":
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      return {
        start: startOfYear,
        end: now,
        interval: "month" as const
      };
    case "all_time":
      // For all time, you might want to start from organization creation date
      const allTimeStart = new Date(2024, 0, 1); // Fallback date
      return {
        start: allTimeStart,
        end: now,
        interval: "month" as const
      };
    default:
      return {
        start: startOfMonth,
        end: now,
        interval: "day" as const
      };
  }
}
