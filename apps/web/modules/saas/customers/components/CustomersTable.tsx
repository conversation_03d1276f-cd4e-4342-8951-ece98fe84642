"use client";

import { useState } from "react";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { ActionBar } from "@saas/shared/components/ActionBar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>etHeader, <PERSON>etTitle, SheetTrigger } from "@ui/components/sheet";
import { CustomerDetails } from "./CustomerDetails";
import {
  FilterIcon,
  DownloadIcon,
  MailIcon,
  EyeIcon,
  EditIcon,
  Trash2Icon,
  UserPlusIcon,
} from "lucide-react";

interface CustomersTableProps {
  organizationId: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "active" | "inactive" | "pending" | "verified";
  totalSpent: number;
  lastPurchase: string;
  createdAt: string;
  city: string;
  state: string;
  avatar?: string;
}

// Dados mockados para demonstração
const mockCustomers: Customer[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(11) 99999-9999",
    status: "verified",
    totalSpent: 2500.00,
    lastPurchase: "2024-01-20",
    createdAt: "2023-03-15",
    city: "São Paulo",
    state: "SP",
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "(21) 88888-8888",
    status: "active",
    totalSpent: 1800.00,
    lastPurchase: "2024-01-18",
    createdAt: "2023-05-22",
    city: "Rio de Janeiro",
    state: "RJ",
  },
  {
    id: "3",
    name: "Pedro Costa",
    email: "<EMAIL>",
    phone: "(31) 77777-7777",
    status: "pending",
    totalSpent: 950.00,
    lastPurchase: "2024-01-15",
    createdAt: "2023-08-10",
    city: "Belo Horizonte",
    state: "MG",
  },
  {
    id: "4",
    name: "Ana Oliveira",
    email: "<EMAIL>",
    phone: "(41) 66666-6666",
    status: "inactive",
    totalSpent: 0.00,
    lastPurchase: "",
    createdAt: "2023-12-01",
    city: "Curitiba",
    state: "PR",
  },
];

const statusColors = {
  verified: "bg-green-100 text-green-800 border-green-200",
  active: "bg-blue-100 text-blue-800 border-blue-200",
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  inactive: "bg-gray-100 text-gray-800 border-gray-200",
};

const statusLabels = {
  verified: "Verificado",
  active: "Ativo",
  pending: "Pendente",
  inactive: "Inativo",
};

export function CustomersTable({ organizationId }: CustomersTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomers, setSelectedCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Nunca";
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const filteredCustomers = mockCustomers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  );

  const columns: DataTableColumn<Customer>[] = [
    {
      key: "name",
      label: "Cliente",
      render: (_, customer) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={customer.avatar} />
            <AvatarFallback className="bg-primary/10 text-primary text-xs">
              {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm">{customer.name}</p>
            <p className="text-xs text-muted-foreground">{customer.email}</p>
          </div>
        </div>
      ),
      width: "300px",
    },
    {
      key: "phone",
      label: "Telefone",
      render: (phone) => (
        <span className="text-sm">{phone}</span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (status) => (
        <Badge 
          variant="outline" 
          className={`${statusColors[status as keyof typeof statusColors]} text-xs`}
        >
          {statusLabels[status as keyof typeof statusLabels]}
        </Badge>
      ),
    },
    {
      key: "totalSpent",
      label: "Total Gasto",
      render: (totalSpent) => (
        <span className="font-medium text-sm">
          {formatCurrency(totalSpent)}
        </span>
      ),
    },
    {
      key: "lastPurchase",
      label: "Última Compra",
      render: (lastPurchase) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(lastPurchase)}
        </span>
      ),
    },
    {
      key: "city",
      label: "Localização",
      render: (_, customer) => (
        <span className="text-sm">
          {customer.city}, {customer.state}
        </span>
      ),
    },
  ];

  const actions: DataTableAction<Customer>[] = [
    {
      label: "Ver Detalhes",
      onClick: (customer) => setSelectedCustomer(customer),
    },
    {
      label: "Editar",
      onClick: (customer) => console.log("Editar", customer),
    },
    {
      label: "Excluir",
      onClick: (customer) => console.log("Excluir", customer),
      variant: "destructive",
    },
  ];

  return (
    <div className="space-y-6">
      <ActionBar
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Buscar clientes por nome, email ou telefone..."
        selectedCount={selectedCustomers.length}
        onClearSelection={() => setSelectedCustomers([])}
        actions={
          <>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline">
                  <FilterIcon className="h-4 w-4 mr-2" />
                  Filtros
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filtros Avançados</SheetTitle>
                </SheetHeader>
                <div className="py-4">
                  <p className="text-sm text-muted-foreground">
                    Filtros avançados serão implementados aqui
                  </p>
                </div>
              </SheetContent>
            </Sheet>
            <Button>
              <UserPlusIcon className="h-4 w-4 mr-2" />
              Novo Cliente
            </Button>
          </>
        }
        bulkActions={
          <>
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar Selecionados
            </Button>
            <Button variant="outline" size="sm">
              <MailIcon className="h-4 w-4 mr-2" />
              Enviar Email
            </Button>
          </>
        }
      />

      <DataTable
        data={filteredCustomers}
        columns={columns}
        actions={actions}
        selectable
        onSelectionChange={setSelectedCustomers}
        emptyMessage="Nenhum cliente encontrado"
      />

      {/* Modal de detalhes do cliente */}
      {selectedCustomer && (
        <Sheet open={!!selectedCustomer} onOpenChange={() => setSelectedCustomer(null)}>
          <SheetContent className="w-[600px] sm:max-w-[600px]">
            <CustomerDetails
              customer={selectedCustomer}
              onClose={() => setSelectedCustomer(null)}
              onEdit={(customer) => console.log("Editar", customer)}
              onDelete={(customer) => console.log("Excluir", customer)}
            />
          </SheetContent>
        </Sheet>
      )}
    </div>
  );
}
