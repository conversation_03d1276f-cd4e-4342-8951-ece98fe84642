"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import {
  SettingsIcon,
  MailIcon,
  BellIcon,
  ShieldIcon,
  DatabaseIcon,
  PaletteIcon,
  SaveIcon,
} from "lucide-react";

interface CustomersSettingsProps {
  organizationId: string;
}

export function CustomersSettings({ organizationId }: CustomersSettingsProps) {
  const [settings, setSettings] = useState({
    // Configurações de Email
    emailNotifications: true,
    welcomeEmail: true,
    purchaseConfirmation: true,
    marketingEmails: false,
    emailTemplate: "modern",
    
    // Configurações de Notificações
    newCustomerAlert: true,
    purchaseAlert: true,
    inactiveCustomerAlert: false,
    
    // Configurações de Privacidade
    dataRetention: "2-years",
    allowDataExport: true,
    requireConsent: true,
    
    // Configurações de Interface
    defaultView: "table",
    itemsPerPage: "25",
    showAvatars: true,
    
    // Configurações Personalizadas
    customFields: "",
    integrations: {
      crm: false,
      analytics: true,
      marketing: false,
    }
  });

  const handleSave = () => {
    console.log("Salvando configurações:", settings);
    // Aqui você implementaria a lógica para salvar as configurações
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Configurações de Email */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MailIcon className="h-5 w-5" />
              Configurações de Email
            </CardTitle>
            <CardDescription>
              Configure como e quando enviar emails para clientes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Notificações por Email</Label>
                <p className="text-xs text-muted-foreground">
                  Receber emails sobre atividades de clientes
                </p>
              </div>
              <Switch
                checked={settings.emailNotifications}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, emailNotifications: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email de Boas-vindas</Label>
                <p className="text-xs text-muted-foreground">
                  Enviar automaticamente para novos clientes
                </p>
              </div>
              <Switch
                checked={settings.welcomeEmail}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, welcomeEmail: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Confirmação de Compra</Label>
                <p className="text-xs text-muted-foreground">
                  Enviar recibo após cada compra
                </p>
              </div>
              <Switch
                checked={settings.purchaseConfirmation}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, purchaseConfirmation: checked }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Template de Email</Label>
              <Select
                value={settings.emailTemplate}
                onValueChange={(value) =>
                  setSettings(prev => ({ ...prev, emailTemplate: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="modern">Moderno</SelectItem>
                  <SelectItem value="classic">Clássico</SelectItem>
                  <SelectItem value="minimal">Minimalista</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Notificações */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BellIcon className="h-5 w-5" />
              Notificações do Sistema
            </CardTitle>
            <CardDescription>
              Configure alertas e notificações internas
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Novo Cliente</Label>
                <p className="text-xs text-muted-foreground">
                  Alerta quando um novo cliente se cadastra
                </p>
              </div>
              <Switch
                checked={settings.newCustomerAlert}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, newCustomerAlert: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Nova Compra</Label>
                <p className="text-xs text-muted-foreground">
                  Alerta quando um cliente faz uma compra
                </p>
              </div>
              <Switch
                checked={settings.purchaseAlert}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, purchaseAlert: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cliente Inativo</Label>
                <p className="text-xs text-muted-foreground">
                  Alerta para clientes sem atividade há 30 dias
                </p>
              </div>
              <Switch
                checked={settings.inactiveCustomerAlert}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, inactiveCustomerAlert: checked }))
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Privacidade */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldIcon className="h-5 w-5" />
              Privacidade e Dados
            </CardTitle>
            <CardDescription>
              Configure políticas de privacidade e retenção de dados
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Retenção de Dados</Label>
              <Select
                value={settings.dataRetention}
                onValueChange={(value) =>
                  setSettings(prev => ({ ...prev, dataRetention: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-year">1 ano</SelectItem>
                  <SelectItem value="2-years">2 anos</SelectItem>
                  <SelectItem value="5-years">5 anos</SelectItem>
                  <SelectItem value="indefinite">Indefinido</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Permitir Exportação</Label>
                <p className="text-xs text-muted-foreground">
                  Clientes podem exportar seus dados
                </p>
              </div>
              <Switch
                checked={settings.allowDataExport}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, allowDataExport: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Consentimento Obrigatório</Label>
                <p className="text-xs text-muted-foreground">
                  Exigir consentimento para coleta de dados
                </p>
              </div>
              <Switch
                checked={settings.requireConsent}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, requireConsent: checked }))
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Interface */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PaletteIcon className="h-5 w-5" />
              Interface e Visualização
            </CardTitle>
            <CardDescription>
              Personalize como os dados são exibidos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Visualização Padrão</Label>
              <Select
                value={settings.defaultView}
                onValueChange={(value) =>
                  setSettings(prev => ({ ...prev, defaultView: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="table">Tabela</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                  <SelectItem value="list">Lista</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Itens por Página</Label>
              <Select
                value={settings.itemsPerPage}
                onValueChange={(value) =>
                  setSettings(prev => ({ ...prev, itemsPerPage: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Mostrar Avatares</Label>
                <p className="text-xs text-muted-foreground">
                  Exibir fotos de perfil dos clientes
                </p>
              </div>
              <Switch
                checked={settings.showAvatars}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, showAvatars: checked }))
                }
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="min-w-32">
          <SaveIcon className="h-4 w-4 mr-2" />
          Salvar Configurações
        </Button>
      </div>
    </div>
  );
}
