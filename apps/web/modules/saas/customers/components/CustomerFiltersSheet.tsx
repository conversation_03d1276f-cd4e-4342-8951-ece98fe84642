"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import { Separator } from "@ui/components/separator";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@ui/components/sheet";
import {
  FilterIcon,
  SearchIcon,
  XIcon,
  UsersIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  CrownIcon,
  TrendingUpIcon,
  StarIcon,
} from "lucide-react";

interface CustomerFiltersSheetProps {
  onFiltersChange: (filters: CustomerFilters) => void;
  activeFilters: CustomerFilters;
}

export interface CustomerFilters {
  searchTerm: string;
  status: string[];
  dateRange: {
    from: string;
    to: string;
  };
  minRevenue: number;
  maxRevenue: number;
}

const statusOptions = [
  { id: "verified", name: "Verificados", icon: CheckCircleIcon, color: "text-green-600" },
  { id: "active", name: "Ativos", icon: CheckCircleIcon, color: "text-blue-600" },
  { id: "pending", name: "Pendentes", icon: ClockIcon, color: "text-yellow-600" },
  { id: "inactive", name: "Inativos", icon: XCircleIcon, color: "text-gray-600" },
];

export function CustomerFiltersSheet({ onFiltersChange, activeFilters }: CustomerFiltersSheetProps) {
  const [filters, setFilters] = useState<CustomerFilters>(activeFilters);
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: keyof CustomerFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleStatusToggle = (statusId: string) => {
    const newStatus = filters.status.includes(statusId)
      ? filters.status.filter(id => id !== statusId)
      : [...filters.status, statusId];
    handleFilterChange('status', newStatus);
  };

  const clearAllFilters = () => {
    const clearedFilters: CustomerFilters = {
      searchTerm: "",
      status: [],
      dateRange: { from: "", to: "" },
      minRevenue: 0,
      maxRevenue: 0,
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const activeFiltersCount = [
    filters.searchTerm ? 1 : 0,
    filters.status.length,
    filters.dateRange.from || filters.dateRange.to ? 1 : 0,
    filters.minRevenue > 0 || filters.maxRevenue > 0 ? 1 : 0,
  ].reduce((acc, count) => acc + count, 0);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <FilterIcon className="h-4 w-4 mr-2" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <FilterIcon className="h-5 w-5" />
            Filtros Avançados
          </SheetTitle>
          <SheetDescription>
            Aplique filtros para encontrar clientes específicos
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Barra de Busca */}
          <div className="space-y-2">
            <Label htmlFor="search">Buscar</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Nome, email, CPF ou telefone..."
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <Separator />

          {/* Filtros de Status */}
          <div className="space-y-3">
            <Label>Status</Label>
            <div className="grid grid-cols-2 gap-2">
              {statusOptions.map((status) => {
                const IconComponent = status.icon;
                return (
                  <div key={status.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status.id}`}
                      checked={filters.status.includes(status.id)}
                      onCheckedChange={() => handleStatusToggle(status.id)}
                    />
                    <Label
                      htmlFor={`status-${status.id}`}
                      className="flex items-center gap-2 text-sm cursor-pointer"
                    >
                      <IconComponent className={`h-4 w-4 ${status.color}`} />
                      {status.name}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Filtros de Data */}
          <div className="space-y-3">
            <Label>Período de Cadastro</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="date-from" className="text-xs">De</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={filters.dateRange.from}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, from: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-to" className="text-xs">Até</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={filters.dateRange.to}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, to: e.target.value })}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Filtros de Receita */}
          <div className="space-y-3">
            <Label>Faixa de Receita (R$)</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="min-revenue" className="text-xs">Mínimo</Label>
                <Input
                  id="min-revenue"
                  type="number"
                  placeholder="0"
                  value={filters.minRevenue || ""}
                  onChange={(e) => handleFilterChange('minRevenue', Number(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-revenue" className="text-xs">Máximo</Label>
                <Input
                  id="max-revenue"
                  type="number"
                  placeholder="10000"
                  value={filters.maxRevenue || ""}
                  onChange={(e) => handleFilterChange('maxRevenue', Number(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Ações */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={clearAllFilters}
              className="flex-1"
            >
              <XIcon className="h-4 w-4 mr-2" />
              Limpar Filtros
            </Button>
            <Button
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Aplicar Filtros
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
