"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import {
  SearchIcon,
  PlusIcon,
  EditIcon,
  Trash2Icon,
  EyeIcon,
  MailIcon,
  MapPinIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DownloadIcon,
  CalendarIcon,
  ChevronDownIcon,
  FilterIcon,
} from "lucide-react";
import { CustomerFiltersSheet, CustomerFilters } from "./CustomerFiltersSheet";
import { AddCustomerModal, NewCustomer } from "./AddCustomerModal";
import { CustomerDetails } from "./CustomerDetails";

interface CustomersListProps {
  organizationId: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "active" | "inactive" | "pending" | "verified";
  totalSpent: number;
  lastPurchase: string;
  createdAt: string;
  city: string;
  state: string;
  address?: string;
  company?: string;
  notes?: string;
  purchases: Purchase[];
  activities: Activity[];
}

interface Purchase {
  id: string;
  date: string;
  amount: number;
  status: "completed" | "pending" | "cancelled";
  description: string;
}

interface Activity {
  id: string;
  date: string;
  type: "login" | "purchase" | "support" | "email";
  description: string;
}

// Dados mockados para demonstração
const mockCustomers: Customer[] = [
  {
    id: "1",
    name: "João Silva",
    email: "<EMAIL>",
    phone: "(11) 99999-9999",
    status: "verified",
    totalSpent: 2500.00,
    lastPurchase: "2024-01-20",
    createdAt: "2023-03-15",
    city: "São Paulo",
    state: "SP",
    address: "Rua das Flores, 123 - Centro",
    company: "Tech Solutions Ltda",
    notes: "Cliente VIP com preferência por pagamentos PIX",
    purchases: [
      {
        id: "1",
        date: "2024-01-20",
        amount: 1500.00,
        status: "completed",
        description: "Plano Premium Mensal"
      },
      {
        id: "2",
        date: "2024-01-15",
        amount: 1000.00,
        status: "completed",
        description: "Serviço de Consultoria"
      }
    ],
    activities: [
      {
        id: "1",
        date: "2024-01-20",
        type: "purchase",
        description: "Compra realizada - R$ 1.500,00"
      },
      {
        id: "2",
        date: "2024-01-19",
        type: "login",
        description: "Login no sistema"
      },
      {
        id: "3",
        date: "2024-01-18",
        type: "support",
        description: "Ticket de suporte aberto"
      }
    ]
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "(21) 88888-8888",
    status: "active",
    totalSpent: 1200.00,
    lastPurchase: "2024-01-18",
    createdAt: "2023-06-20",
    city: "Rio de Janeiro",
    state: "RJ",
    company: "Design Studio",
    purchases: [
      {
        id: "3",
        date: "2024-01-18",
        amount: 1200.00,
        status: "completed",
        description: "Pacote de Templates"
      }
    ],
    activities: [
      {
        id: "4",
        date: "2024-01-18",
        type: "purchase",
        description: "Compra realizada - R$ 1.200,00"
      }
    ]
  },
  {
    id: "3",
    name: "Pedro Costa",
    email: "<EMAIL>",
    phone: "(31) 77777-7777",
    status: "pending",
    totalSpent: 0.00,
    lastPurchase: "Nunca",
    createdAt: "2024-01-15",
    city: "Belo Horizonte",
    state: "MG",
    purchases: [],
    activities: [
      {
        id: "5",
        date: "2024-01-15",
        type: "login",
        description: "Primeiro acesso ao sistema"
      }
    ]
  },
  {
    id: "4",
    name: "Ana Oliveira",
    email: "<EMAIL>",
    phone: "(41) 66666-6666",
    status: "inactive",
    totalSpent: 800.00,
    lastPurchase: "2023-10-15",
    createdAt: "2023-01-10",
    city: "Curitiba",
    state: "PR",
    purchases: [
      {
        id: "6",
        date: "2023-10-15",
        amount: 800.00,
        status: "completed",
        description: "Plano Básico"
      }
    ],
    activities: [
      {
        id: "7",
        date: "2023-10-15",
        type: "purchase",
        description: "Compra realizada - R$ 800,00"
      }
    ]
  },
  {
    id: "5",
    name: "Carlos Ferreira",
    email: "<EMAIL>",
    phone: "(51) 55555-5555",
    status: "verified",
    totalSpent: 3200.00,
    lastPurchase: "2024-01-19",
    createdAt: "2022-08-12",
    city: "Porto Alegre",
    state: "RS",
    company: "Consultoria Empresarial",
    notes: "Cliente recorrente, sempre paga pontualmente",
    purchases: [
      {
        id: "8",
        date: "2024-01-19",
        amount: 2000.00,
        status: "completed",
        description: "Serviço de Auditoria"
      },
      {
        id: "9",
        date: "2023-12-15",
        amount: 1200.00,
        status: "completed",
        description: "Consultoria Mensal"
      }
    ],
    activities: [
      {
        id: "10",
        date: "2024-01-19",
        type: "purchase",
        description: "Compra realizada - R$ 2.000,00"
      },
      {
        id: "11",
        date: "2024-01-18",
        type: "support",
        description: "Solicitação de relatório"
      }
    ]
  }
];

export function CustomersList({ organizationId }: CustomersListProps) {
  const [customers, setCustomers] = useState<Customer[]>(mockCustomers);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [filters, setFilters] = useState<CustomerFilters>({
    searchTerm: "",
    status: [],
    dateRange: { from: "", to: "" },
    minRevenue: 0,
    maxRevenue: 0,
  });

  const getStatusIcon = (status: Customer["status"]) => {
    switch (status) {
      case "verified":
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case "active":
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case "pending":
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case "inactive":
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: Customer["status"]) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800 border border-green-200 text-xs">Verificado</Badge>;
      case "active":
        return <Badge className="bg-blue-100 text-blue-800 border border-blue-200 text-xs">Ativo</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200 text-xs">Pendente</Badge>;
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200 text-xs">Inativo</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200 text-xs">Desconhecido</Badge>;
    }
  };

  const filteredCustomers = customers.filter(customer => {
    // Filtro de busca
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = customer.name.toLowerCase().includes(searchLower) ||
                           customer.email.toLowerCase().includes(searchLower) ||
                           customer.phone.includes(searchTerm);
      if (!matchesSearch) return false;
    }

    // Filtros avançados
    if (filters.status.length > 0 && !filters.status.includes(customer.status)) {
      return false;
    }

    if (filters.dateRange.from || filters.dateRange.to) {
      const customerDate = new Date(customer.createdAt);
      if (filters.dateRange.from && customerDate < new Date(filters.dateRange.from)) {
        return false;
      }
      if (filters.dateRange.to && customerDate > new Date(filters.dateRange.to)) {
        return false;
      }
    }

    if (filters.minRevenue > 0 && customer.totalSpent < filters.minRevenue) {
      return false;
    }
    if (filters.maxRevenue > 0 && customer.totalSpent > filters.maxRevenue) {
      return false;
    }

    return true;
  });

  const handleDeleteCustomer = (customerId: string) => {
    if (confirm("Tem certeza que deseja excluir este cliente?")) {
      setCustomers(prev => prev.filter(customer => customer.id !== customerId));
      if (selectedCustomer?.id === customerId) {
        setSelectedCustomer(null);
      }
    }
  };

  const handleAddCustomer = (newCustomer: NewCustomer) => {
    const customer: Customer = {
      id: Date.now().toString(),
      name: newCustomer.name,
      email: newCustomer.email,
      phone: newCustomer.phone,
      status: "pending",
      totalSpent: 0,
      lastPurchase: "Nunca",
      createdAt: new Date().toISOString().split('T')[0],
      city: newCustomer.city,
      state: newCustomer.state,
      address: newCustomer.address,
      company: newCustomer.company,
      notes: newCustomer.notes,
      purchases: [],
      activities: [
        {
          id: "1",
          date: new Date().toISOString(),
          type: "login",
          description: "Cliente cadastrado no sistema"
        }
      ]
    };
    setCustomers(prev => [customer, ...prev]);
    setSelectedCustomer(customer);
  };

  const handleFiltersChange = (newFilters: CustomerFilters) => {
    setFilters(newFilters);
  };

  const activeFiltersCount = [
    filters.status.length,
    filters.dateRange.from || filters.dateRange.to ? 1 : 0,
    filters.minRevenue > 0 || filters.maxRevenue > 0 ? 1 : 0,
  ].reduce((acc, count) => acc + count, 0);

  return (
    <div className="flex h-[calc(100vh-200px)]">
      {/* Lista Lateral de Clientes */}
      <div className="w-80 border-r bg-muted/30 flex flex-col">
        {/* Header da Lista */}
        <div className="p-4 border-b bg-background">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Customers</h2>
            <Button size="sm">
              <PlusIcon className="h-4 w-4 mr-2" />
              Novo
            </Button>
          </div>

          {/* Barra de Busca */}
          <div className="relative mb-4">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar clientes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtros */}
          <div className="flex gap-2">
            <CustomerFiltersSheet
              onFiltersChange={handleFiltersChange}
              activeFilters={filters}
            />
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Lista de Clientes */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-2 space-y-1">
            {filteredCustomers.map((customer) => (
              <div
                key={customer.id}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedCustomer?.id === customer.id
                    ? "bg-primary/10 border border-primary/20"
                    : "hover:bg-muted/50"
                }`}
                onClick={() => setSelectedCustomer(customer)}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-primary">
                      {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">{customer.name}</p>
                    <p className="text-xs text-muted-foreground truncate">{customer.email}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      {getStatusIcon(customer.status)}
                      {getStatusBadge(customer.status)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredCustomers.length === 0 && (
            <div className="text-center py-8 px-4">
              <div className="w-12 h-12 mx-auto mb-3 bg-muted/50 rounded-full flex items-center justify-center">
                <SearchIcon className="h-6 w-6 text-muted-foreground" />
              </div>
              <p className="text-sm font-medium text-foreground mb-1">
                {customers.length === 0 ? "Nenhum cliente cadastrado" : "Nenhum cliente encontrado"}
              </p>
              <p className="text-xs text-muted-foreground">
                {customers.length === 0 ? "Comece adicionando seu primeiro cliente" : "Tente ajustar os filtros"}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Área de Detalhes do Cliente */}
      <div className="flex-1 overflow-y-auto">
        {selectedCustomer ? (
          <CustomerDetails
            customer={selectedCustomer}
            onClose={() => setSelectedCustomer(null)}
            onEdit={(customer) => {
              // Implementar edição
              console.log("Editar cliente:", customer);
            }}
            onDelete={handleDeleteCustomer}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-muted/50 rounded-full flex items-center justify-center">
                <EyeIcon className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium text-foreground mb-2">Selecione um cliente</h3>
              <p className="text-muted-foreground">
                Escolha um cliente da lista para ver os detalhes
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Modal de Adicionar Cliente */}
      <AddCustomerModal onCustomerAdd={handleAddCustomer} />
    </div>
  );
}
