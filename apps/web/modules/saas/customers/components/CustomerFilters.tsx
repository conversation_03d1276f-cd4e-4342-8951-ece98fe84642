"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import {
  SearchIcon,
  FilterIcon,
  UsersIcon,
  UserPlusIcon,
  DownloadIcon,
  MailIcon
} from "lucide-react";

interface CustomerFiltersProps {
  organizationId: string;
}

export function CustomerFilters({ organizationId }: CustomerFiltersProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSegment, setSelectedSegment] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  const segments = [
    { id: "all", name: "Todos", count: 1247 },
    { id: "vip", name: "VIP", count: 89 },
    { id: "active", name: "Ativos", count: 892 },
    { id: "inactive", name: "Inativos", count: 156 },
    { id: "new", name: "Novos", count: 234 }
  ];

  const statuses = [
    { id: "all", name: "Todos", count: 1247 },
    { id: "verified", name: "Verificados", count: 1103 },
    { id: "unverified", name: "Não Verificados", count: 144 }
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Filtros e Busca
            </CardTitle>
            <CardDescription>
              Encontre e organize seus clientes com filtros avançados
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <MailIcon className="h-4 w-4 mr-2" />
              Enviar Email
            </Button>
            <Button>
              <UserPlusIcon className="h-4 w-4 mr-2" />
              Novo Cliente
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Barra de Busca */}
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar por nome, email, CPF ou telefone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtros de Segmento */}
          <div>
            <h4 className="text-sm font-medium mb-2">Segmentos</h4>
            <div className="flex flex-wrap gap-2">
              {segments.map((segment) => (
                <Button
                  key={segment.id}
                  variant={selectedSegment === segment.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSegment(segment.id)}
                  className="h-8"
                >
                  {segment.name}
                  <Badge variant="secondary" className="ml-2">
                    {segment.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros de Status */}
          <div>
            <h4 className="text-sm font-medium mb-2">Status</h4>
            <div className="flex flex-wrap gap-2">
              {statuses.map((status) => (
                <Button
                  key={status.id}
                  variant={selectedStatus === status.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedStatus(status.id)}
                  className="h-8"
                >
                  {status.name}
                  <Badge variant="secondary" className="ml-2">
                    {status.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros Avançados */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <Button variant="outline" size="sm">
              <FilterIcon className="h-4 w-4 mr-2" />
              Filtros Avançados
            </Button>
            <span className="text-sm text-muted-foreground">
              {searchTerm && `"${searchTerm}" • `}
              {selectedSegment !== "all" && `${segments.find(s => s.id === selectedSegment)?.name} • `}
              {selectedStatus !== "all" && `${statuses.find(s => s.id === selectedStatus)?.name}`}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
