"use client";

import { CustomerMetrics } from "./CustomerMetrics";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  DollarSignIcon,
  CalendarIcon,
  ArrowRightIcon,
} from "lucide-react";

interface CustomersOverviewProps {
  organizationId: string;
}

// Dados mockados para demonstração
const recentActivities = [
  {
    id: "1",
    customer: "<PERSON>",
    action: "Realizou uma compra",
    amount: 1500.00,
    time: "2 minutos atrás",
    type: "purchase" as const,
  },
  {
    id: "2",
    customer: "<PERSON>",
    action: "Cadastrou-se na plataforma",
    time: "15 minutos atrás",
    type: "signup" as const,
  },
  {
    id: "3",
    customer: "<PERSON>",
    action: "Atualizou perfil",
    time: "1 hora atrás",
    type: "update" as const,
  },
  {
    id: "4",
    customer: "<PERSON>",
    action: "Realizou uma compra",
    amount: 850.00,
    time: "2 horas atrás",
    type: "purchase" as const,
  },
];

const topCustomers = [
  {
    id: "1",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    totalSpent: 15750.00,
    purchases: 12,
    growth: 23.5,
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    totalSpent: 12300.00,
    purchases: 8,
    growth: 18.2,
  },
  {
    id: "3",
    name: "Pedro Costa",
    email: "<EMAIL>",
    totalSpent: 9850.00,
    purchases: 15,
    growth: -5.1,
  },
  {
    id: "4",
    name: "Ana Oliveira",
    email: "<EMAIL>",
    totalSpent: 8200.00,
    purchases: 6,
    growth: 31.8,
  },
];

export function CustomersOverview({ organizationId }: CustomersOverviewProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Métricas principais */}
      <CustomerMetrics organizationId={organizationId} />

      <div className="grid gap-6 md:grid-cols-2">
        {/* Atividades Recentes */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Atividades Recentes
                </CardTitle>
                <CardDescription>
                  Últimas ações dos seus clientes
                </CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                Ver todas
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      {activity.type === 'purchase' && <DollarSignIcon className="h-4 w-4 text-green-600" />}
                      {activity.type === 'signup' && <UsersIcon className="h-4 w-4 text-blue-600" />}
                      {activity.type === 'update' && <CalendarIcon className="h-4 w-4 text-orange-600" />}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{activity.customer}</p>
                      <p className="text-xs text-muted-foreground">{activity.action}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    {activity.amount && (
                      <p className="font-medium text-sm text-green-600">
                        {formatCurrency(activity.amount)}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Clientes */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUpIcon className="h-5 w-5" />
                  Top Clientes
                </CardTitle>
                <CardDescription>
                  Clientes com maior valor total
                </CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                Ver ranking
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCustomers.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-primary">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-sm">{customer.name}</p>
                      <p className="text-xs text-muted-foreground">{customer.purchases} compras</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatCurrency(customer.totalSpent)}</p>
                    <div className="flex items-center gap-1">
                      {customer.growth > 0 ? (
                        <TrendingUpIcon className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDownIcon className="h-3 w-3 text-red-600" />
                      )}
                      <span className={`text-xs ${customer.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {Math.abs(customer.growth)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
