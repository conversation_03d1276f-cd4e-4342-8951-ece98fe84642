"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { useTranslations } from "next-intl";

export function LoginModeSwitch({
	activeMode,
	onChange,
	className,
}: {
	activeMode: "password" | "magic-link";
	onChange: (mode: string) => void;
	className?: string;
}) {
	const t = useTranslations();
	return (
		<Tabs value={activeMode} onValueChange={onChange} className={className}>
			<TabsList className="w-full h-11 bg-muted/50">
				<TabsTrigger
					value="password"
					className="flex-1 data-[state=active]:bg-background data-[state=active]:shadow-sm"
				>
					{t("auth.login.modes.password")}
				</TabsTrigger>
				<TabsTrigger
					value="magic-link"
					className="flex-1 data-[state=active]:bg-background data-[state=active]:shadow-sm"
				>
					{t("auth.login.modes.magicLink")}
				</TabsTrigger>
			</TabsList>
		</Tabs>
	);
}
