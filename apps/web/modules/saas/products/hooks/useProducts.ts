import { useState, useCallback } from "react";
import { toast } from "sonner";
import {
  useProductsListQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useArchiveProductMutation,
  usePublishProductMutation,
  useProductCheckoutLinksQuery,
  type Product as ApiProduct
} from '../lib/api';

// Re-export the API Product type
export type Product = ApiProduct;

export interface CreateProductData {
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  priceCents: number;
  comparePriceCents?: number;
  currency?: string;
  type: Product["type"];
  status?: Product["status"];
  visibility?: Product["visibility"];
  categoryId?: string;
  thumbnail?: string;
  gallery?: string[];
  tags?: string[];
  features?: string[];
  requirements?: string[];
  duration?: number;
  level?: string;
  language?: string;
  certificate?: boolean;
  downloadable?: boolean;
  checkoutType?: Product["checkoutType"];
  settings?: Record<string, any>;
}

export interface UpdateProductData extends Partial<CreateProductData> {}

export interface ProductFilters {
  searchTerm: string;
  status: Product["status"][];
  type: Product["type"][];
  category: string[];
  priceRange: { min: number | null; max: number | null };
  hasCommission: boolean | null;
}

export interface ProductsResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function useProducts(organizationId: string) {
  const [filters, setFilters] = useState<Partial<ProductFilters>>({});
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);

  // Use React Query for products list
  const {
    data: productsData,
    isLoading: loading,
    error: queryError,
    refetch: refetchProducts
  } = useProductsListQuery(organizationId, {
    ...filters,
    page,
    limit
  });

  const error = queryError?.message || null;
  const products = productsData?.products || [];
  const pagination = productsData?.pagination || null;

  // Mutations
  const createProductMutation = useCreateProductMutation();
  const updateProductMutation = useUpdateProductMutation();
  const deleteProductMutation = useDeleteProductMutation();
  const archiveProductMutation = useArchiveProductMutation();
  const publishProductMutation = usePublishProductMutation();

  // Fetch products function for compatibility
  const fetchProducts = useCallback(async (newFilters?: Partial<ProductFilters>, newPage = 1, newLimit = 20) => {
    setFilters(newFilters || {});
    setPage(newPage);
    setLimit(newLimit);
    await refetchProducts();
  }, [refetchProducts]);

  // Fetch single product - this is now handled by useProductQuery directly
  const fetchProduct = useCallback(async (productId: string): Promise<Product | null> => {
    // This function is kept for compatibility but should use useProductQuery directly
    toast.info("Use useProductQuery hook directly for better performance");
    return null;
  }, []);

  // Create product using mutation
  const createProduct = useCallback(async (productData: CreateProductData): Promise<Product | null> => {
    if (!organizationId) return null;

    try {
      const newProduct = await createProductMutation.mutateAsync({
        ...productData,
        organizationId,
      } as any);

      toast.success("Produto criado com sucesso!");
      return newProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create product";
      toast.error(`Erro ao criar produto: ${errorMessage}`);
      return null;
    }
  }, [organizationId, createProductMutation]);

  // Update product using mutation
  const updateProduct = useCallback(async (productId: string, productData: UpdateProductData): Promise<Product | null> => {
    if (!organizationId || !productId) return null;

    try {
      const updatedProduct = await updateProductMutation.mutateAsync({
        id: productId,
        organizationId,
        ...productData,
      } as any);

      toast.success("Produto atualizado com sucesso!");
      return updatedProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update product";
      toast.error(`Erro ao atualizar produto: ${errorMessage}`);
      return null;
    }
  }, [organizationId, updateProductMutation]);

  // Delete product using mutation
  const deleteProduct = useCallback(async (productId: string): Promise<boolean> => {
    if (!organizationId || !productId) return false;

    try {
      await deleteProductMutation.mutateAsync({
        id: productId,
        organizationId,
      });

      toast.success("Produto deletado com sucesso!");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete product";
      toast.error(`Erro ao deletar produto: ${errorMessage}`);
      return false;
    }
  }, [organizationId, deleteProductMutation]);

  // Archive product using mutation
  const archiveProduct = useCallback(async (productId: string): Promise<Product | null> => {
    if (!organizationId || !productId) return null;

    try {
      const archivedProduct = await archiveProductMutation.mutateAsync({
        id: productId,
        organizationId,
      });

      toast.success("Produto arquivado com sucesso!");
      return archivedProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to archive product";
      toast.error(`Erro ao arquivar produto: ${errorMessage}`);
      return null;
    }
  }, [organizationId, archiveProductMutation]);

  // Publish product using mutation
  const publishProduct = useCallback(async (productId: string): Promise<Product | null> => {
    if (!organizationId || !productId) return null;

    try {
      const publishedProduct = await publishProductMutation.mutateAsync({
        id: productId,
        organizationId,
      });

      toast.success("Produto publicado com sucesso!");
      return publishedProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to publish product";
      toast.error(`Erro ao publicar produto: ${errorMessage}`);
      return null;
    }
  }, [organizationId, publishProductMutation]);

  // Fetch product checkout links - this is now handled by useProductCheckoutLinksQuery directly
  const fetchProductCheckoutLinks = useCallback(async (productId: string) => {
    // This function is kept for compatibility but should use useProductCheckoutLinksQuery directly
    toast.info("Use useProductCheckoutLinksQuery hook directly for better performance");
    return [];
  }, []);

  return {
    // State
    products,
    loading,
    error,
    pagination,

    // Actions
    fetchProducts,
    fetchProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    archiveProduct,
    publishProduct,
    fetchProductCheckoutLinks,

    // Mutation states
    isCreating: createProductMutation.isPending,
    isUpdating: updateProductMutation.isPending,
    isDeleting: deleteProductMutation.isPending,
    isArchiving: archiveProductMutation.isPending,
    isPublishing: publishProductMutation.isPending,

    // Computed
    hasProducts: products.length > 0,
    totalProducts: pagination?.total || 0,
  };
}
