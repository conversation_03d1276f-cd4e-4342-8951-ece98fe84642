import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { InferRequestType, InferResponseType } from "hono";

// Types
export type Product = InferResponseType<typeof apiClient.products[":id"]["$get"]>["product"];
export type ProductsResponse = InferResponseType<typeof apiClient.products["$get"]>;
export type CreateProductRequest = InferRequestType<typeof apiClient.products["$post"]>["json"];
export type UpdateProductRequest = InferRequestType<typeof apiClient.products[":id"]["$put"]>["json"];

// Query Keys
export const productsListQueryKey = (organizationId: string, filters?: Record<string, any>) =>
  ["products", "list", organizationId, filters] as const;

export const productQueryKey = (id: string) => ["products", "detail", id] as const;

export const productCheckoutLinksQueryKey = (productId: string) =>
  ["products", "checkout-links", productId] as const;

// Queries
export const useProductsListQuery = (
  organizationId: string,
  filters?: {
    status?: string[];
    type?: string[];
    categoryId?: string[];
    page?: number;
    limit?: number;
  }
) => {
  return useQuery({
    queryKey: productsListQueryKey(organizationId, filters),
    queryFn: async () => {
      const queryParams: Record<string, string> = {
        organizationId,
      };

      if (filters?.page) queryParams.page = filters.page.toString();
      if (filters?.limit) queryParams.limit = filters.limit.toString();
      
      // Handle array parameters
      const searchParams = new URLSearchParams(queryParams);
      if (filters?.status?.length) {
        filters.status.forEach(status => searchParams.append("status", status));
      }
      if (filters?.type?.length) {
        filters.type.forEach(type => searchParams.append("type", type));
      }
      if (filters?.categoryId?.length) {
        filters.categoryId.forEach(categoryId => searchParams.append("categoryId", categoryId));
      }

      const response = await apiClient.products.$get({
        query: Object.fromEntries(searchParams.entries()) as any,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }

      return response.json();
    },
    enabled: !!organizationId,
  });
};

export const useProductQuery = (id: string) => {
  return useQuery({
    queryKey: productQueryKey(id),
    queryFn: async () => {
      if (!id) return null;

      const response = await apiClient.products[":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch product");
      }

      return response.json().then((data) => data.product);
    },
    enabled: !!id,
  });
};

export const useProductCheckoutLinksQuery = (productId: string) => {
  return useQuery({
    queryKey: productCheckoutLinksQueryKey(productId),
    queryFn: async () => {
      if (!productId) return [];

      const response = await apiClient.products[":id"]["checkout-links"].$get({
        param: { id: productId },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch checkout links");
      }

      return response.json().then((data) => data.checkoutLinks);
    },
    enabled: !!productId,
  });
};

// Mutations
export const useCreateProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      organizationId,
      ...productData
    }: CreateProductRequest & { organizationId: string }) => {
      const response = await apiClient.products.$post({
        json: productData,
        query: { organizationId },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create product");
      }

      return response.json().then((data) => data.product);
    },
    onSuccess: (data, variables) => {
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });
      
      // Set the new product in cache
      queryClient.setQueryData(productQueryKey(data.id), data);
    },
  });
};

export const useUpdateProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      organizationId,
      ...productData
    }: UpdateProductRequest & { id: string; organizationId: string }) => {
      const response = await apiClient.products[":id"].$put({
        param: { id },
        json: productData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update product");
      }

      return response.json().then((data) => data.product);
    },
    onSuccess: (data, variables) => {
      // Update product in cache
      queryClient.setQueryData(productQueryKey(variables.id), data);
      
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });
    },
  });
};

export const useDeleteProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, organizationId }: { id: string; organizationId: string }) => {
      const response = await apiClient.products[":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete product");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // Remove product from cache
      queryClient.removeQueries({
        queryKey: productQueryKey(variables.id),
      });
      
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });
    },
  });
};

export const useArchiveProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, organizationId }: { id: string; organizationId: string }) => {
      const response = await apiClient.products[":id"].archive.$post({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to archive product");
      }

      return response.json().then((data) => data.product);
    },
    onSuccess: (data, variables) => {
      // Update product in cache
      queryClient.setQueryData(productQueryKey(variables.id), data);
      
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });
    },
  });
};

export const usePublishProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, organizationId }: { id: string; organizationId: string }) => {
      const response = await apiClient.products[":id"].publish.$post({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to publish product");
      }

      return response.json().then((data) => data.product);
    },
    onSuccess: (data, variables) => {
      // Update product in cache
      queryClient.setQueryData(productQueryKey(variables.id), data);
      
      // Invalidate products list
      queryClient.invalidateQueries({
        queryKey: ["products", "list", variables.organizationId],
      });
    },
  });
};
