"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import {
  SearchIcon,
  PackageIcon,
  PlusIcon
} from "lucide-react";
import { ProductFiltersSheet, ProductFilters } from "./ProductFiltersSheet";

interface ProductFiltersProps {
  organizationId: string;
  onFiltersChange?: (filters: ProductFilters) => void;
}

export function ProductFilters({ organizationId, onFiltersChange }: ProductFiltersProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [filters, setFilters] = useState<ProductFilters>({
    searchTerm: "",
    status: [],
    category: [],
    type: [],
    priceRange: { min: null, max: null },
    hasCommission: null,
  });

  const categories = [
    { id: "all", name: "Todos", count: 12 },
    { id: "authorial", name: "Autorais", count: 8 },
    { id: "affiliation", name: "Afiliação", count: 3 },
    { id: "coproduction", name: "Coprodução", count: 1 }
  ];

  const handleFiltersChange = (newFilters: ProductFilters) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    const newFilters = { ...filters, searchTerm: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    const newFilters = {
      ...filters,
      category: categoryId === "all" ? [] : [categoryId]
    };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <PackageIcon className="h-5 w-5" />
              Filtros e Busca
            </CardTitle>
            <CardDescription>
              Encontre e organize seus produtos
            </CardDescription>
          </div>
          <Button>
            <PlusIcon className="h-4 w-4 mr-2" />
            Adicionar produto
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Barra de Busca */}
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar"
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtros de Categoria */}
          <div>
            <h4 className="text-sm font-medium mb-2">Categorias</h4>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleCategoryChange(category.id)}
                  className="h-8"
                >
                  {category.name}
                  <Badge variant="secondary" className="ml-2">
                    {category.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros Avançados */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <ProductFiltersSheet
              onFiltersChange={handleFiltersChange}
              activeFilters={filters}
            />
            <span className="text-sm text-muted-foreground">
              {searchTerm && `"${searchTerm}" • `}
              {selectedCategory !== "all" && `${categories.find(c => c.id === selectedCategory)?.name}`}
              {filters.status.length > 0 && ` • ${filters.status.length} status`}
              {filters.category.length > 0 && ` • ${filters.category.length} categorias`}
              {filters.type.length > 0 && ` • ${filters.type.length} tipos`}
              {filters.priceRange.min !== null || filters.priceRange.max !== null ? " • Preço filtrado" : ""}
              {filters.hasCommission !== null && " • Com comissão"}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
