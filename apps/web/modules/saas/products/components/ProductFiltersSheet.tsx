"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import { Separator } from "@ui/components/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@ui/components/sheet";
import {
  FilterIcon,
  SearchIcon,
  XIcon,
  PackageIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  DollarSignIcon,
  TagIcon,
  ArchiveIcon,
  EyeIcon,
  EyeOffIcon,
  CalendarIcon,
  StarIcon,
  UsersIcon,
  TrendingUpIcon,
  RefreshCwIcon,
  ZapIcon,
  Globe,
} from "lucide-react";

interface ProductFiltersSheetProps {
  onFiltersChange: (filters: ProductFilters) => void;
  activeFilters: ProductFilters;
  totalProducts?: number;
  loading?: boolean;
}

export interface ProductFilters {
  searchTerm: string;
  status: string[];
  category: string[];
  type: string[];
  priceRange: { min: number | null; max: number | null };
  hasCommission: boolean | null;
  visibility: string[];
  duration: { min: number | null; max: number | null };
  hasCertificate: boolean | null;
  isDownloadable: boolean | null;
  language: string[];
  level: string[];
  tags: string[];
  dateRange: { start: Date | null; end: Date | null };
  sortBy: string;
  sortOrder: "asc" | "desc";
}

const statusOptions = [
  { id: "PUBLISHED", name: "Publicado", icon: CheckCircleIcon, color: "text-emerald-600 dark:text-emerald-400", bgColor: "bg-emerald-500/10 dark:bg-emerald-400/10" },
  { id: "DRAFT", name: "Rascunho", icon: ClockIcon, color: "text-amber-600 dark:text-amber-400", bgColor: "bg-amber-500/10 dark:bg-amber-400/10" },
  { id: "ARCHIVED", name: "Arquivado", icon: ArchiveIcon, color: "text-slate-600 dark:text-slate-400", bgColor: "bg-slate-500/10 dark:bg-slate-400/10" },
  { id: "SUSPENDED", name: "Suspenso", icon: XCircleIcon, color: "text-red-600 dark:text-red-400", bgColor: "bg-red-500/10 dark:bg-red-400/10" },
];

const typeOptions = [
  { id: "COURSE", name: "Curso", icon: PackageIcon, count: 12 },
  { id: "EBOOK", name: "E-Book", icon: PackageIcon, count: 8 },
  		{ id: "MENTORSHIP", name: "Mentoria", icon: UsersIcon, count: 5 },
  { id: "SUBSCRIPTION", name: "Assinatura", icon: TrendingUpIcon, count: 3 },
  { id: "BUNDLE", name: "Pacote", icon: PackageIcon, count: 2 },
];

const visibilityOptions = [
  { id: "PUBLIC", name: "Público", icon: EyeIcon, color: "text-emerald-600 dark:text-emerald-400" },
  { id: "PRIVATE", name: "Privado", icon: EyeOffIcon, color: "text-blue-600 dark:text-blue-400" },
  { id: "UNLISTED", name: "Não Listado", icon: EyeOffIcon, color: "text-amber-600 dark:text-amber-400" },
];

const languageOptions = [
  { id: "pt-BR", name: "Português (BR)", count: 15 },
  { id: "en-US", name: "Inglês (US)", count: 8 },
  { id: "es-ES", name: "Espanhol", count: 3 },
];

const levelOptions = [
  { id: "BEGINNER", name: "Iniciante", count: 12 },
  { id: "INTERMEDIATE", name: "Intermediário", count: 18 },
  { id: "ADVANCED", name: "Avançado", count: 8 },
  { id: "EXPERT", name: "Expert", count: 5 },
];

const sortOptions = [
  { id: "createdAt", name: "Data de Criação" },
  { id: "updatedAt", name: "Última Atualização" },
  { id: "name", name: "Nome" },
  { id: "priceCents", name: "Preço" },
  { id: "sales", name: "Vendas" },
  { id: "rating", name: "Avaliação" },
];

export function ProductFiltersSheet({
  onFiltersChange,
  activeFilters,
  totalProducts = 0,
  loading = false
}: ProductFiltersSheetProps) {
  const [filters, setFilters] = useState<ProductFilters>(activeFilters);
  const [isOpen, setIsOpen] = useState(false);
  const [localSearchTerm, setLocalSearchTerm] = useState(activeFilters.searchTerm);

  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleStatusToggle = (statusId: string) => {
    const newStatus = filters.status.includes(statusId)
      ? filters.status.filter(id => id !== statusId)
      : [...filters.status, statusId];
    handleFilterChange('status', newStatus);
  };

  const handleCategoryToggle = (categoryId: string) => {
    const newCategory = filters.category.includes(categoryId)
      ? filters.category.filter(id => id !== categoryId)
      : [...filters.category, categoryId];
    handleFilterChange('category', newCategory);
  };

  const handleTypeToggle = (typeId: string) => {
    const newType = filters.type.includes(typeId)
      ? filters.type.filter(id => id !== typeId)
      : [...filters.type, typeId];
    handleFilterChange('type', newType);
  };

  const handleVisibilityToggle = (visibilityId: string) => {
    const newVisibility = filters.visibility.includes(visibilityId)
      ? filters.visibility.filter(id => id !== visibilityId)
      : [...filters.visibility, visibilityId];
    handleFilterChange('visibility', newVisibility);
  };

  const handleLanguageToggle = (languageId: string) => {
    const newLanguage = filters.language.includes(languageId)
      ? filters.language.filter(id => id !== languageId)
      : [...filters.language, languageId];
    handleFilterChange('language', newLanguage);
  };

  const handleLevelToggle = (levelId: string) => {
    const newLevel = filters.level.includes(levelId)
      ? filters.level.filter(id => id !== levelId)
      : [...filters.level, levelId];
    handleFilterChange('level', newLevel);
  };

  const handleSearchSubmit = () => {
    handleFilterChange('searchTerm', localSearchTerm);
  };

  const clearAllFilters = () => {
    const clearedFilters: ProductFilters = {
      searchTerm: "",
      status: [],
      category: [],
      type: [],
      priceRange: { min: null, max: null },
      hasCommission: null,
      visibility: [],
      duration: { min: null, max: null },
      hasCertificate: null,
      isDownloadable: null,
      language: [],
      level: [],
      tags: [],
      dateRange: { start: null, end: null },
      sortBy: "createdAt",
      sortOrder: "desc",
    };
    setFilters(clearedFilters);
    setLocalSearchTerm("");
    onFiltersChange(clearedFilters);
  };

  const activeFiltersCount = useMemo(() => [
    filters.searchTerm ? 1 : 0,
    filters.status.length,
    filters.category.length,
    filters.type.length,
    filters.priceRange.min !== null || filters.priceRange.max !== null ? 1 : 0,
    filters.hasCommission !== null ? 1 : 0,
    filters.visibility.length,
    filters.duration.min !== null || filters.duration.max !== null ? 1 : 0,
    filters.hasCertificate !== null ? 1 : 0,
    filters.isDownloadable !== null ? 1 : 0,
    filters.language.length,
    filters.level.length,
    filters.tags.length,
    filters.dateRange.start || filters.dateRange.end ? 1 : 0,
  ].reduce((acc, count) => acc + count, 0), [filters]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="relative group shadow-sm border-border/50 hover:bg-muted/50 dark:hover:bg-muted/30 transition-all duration-200"
        >
          <FilterIcon className="h-4 w-4 mr-2 group-hover:rotate-180 transition-transform duration-200" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge className="ml-2 h-5 w-5 p-0 text-xs font-medium bg-primary/10 text-primary border-primary/20">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[450px] sm:w-[600px] overflow-y-auto border-l border-border/50">
        <SheetHeader className="sticky top-0 bg-background z-10 pb-4 border-b border-border/50">
          <SheetTitle className="flex items-center gap-2 text-foreground">
            <FilterIcon className="h-5 w-5 text-primary" />
            Filtros Avançados
          </SheetTitle>
          <SheetDescription className="text-muted-foreground">
            {totalProducts > 0 ? `${totalProducts} produtos encontrados` : "Aplique filtros para encontrar produtos específicos"}
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6 pb-20">
          {/* Barra de Busca com Debounce */}
          <div className="space-y-3">
            <Label htmlFor="search" className="text-sm font-medium text-foreground">Buscar produtos</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Nome, descrição, tags..."
                value={localSearchTerm}
                onChange={(e) => setLocalSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearchSubmit()}
                className="pl-10 pr-20 border-border/50 focus:border-primary focus:ring-primary/20"
              />
              <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
                {localSearchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-muted/50 dark:hover:bg-muted/30"
                    onClick={() => {
                      setLocalSearchTerm("");
                      handleFilterChange('searchTerm', "");
                    }}
                  >
                    <XIcon className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  variant="primary"
                  size="sm"
                  className="h-6 px-2 text-xs shadow-sm shadow-primary/20"
                  onClick={handleSearchSubmit}
                >
                  Buscar
                </Button>
              </div>
            </div>
          </div>

          {/* Filtros de Status */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2 text-foreground">
              <CheckCircleIcon className="h-4 w-4" />
              Status
            </Label>
            <div className="grid grid-cols-2 gap-2">
              {statusOptions.map((status) => {
                const Icon = status.icon;
                const isActive = filters.status.includes(status.id);
                return (
                  <div
                    key={status.id}
                    className={`flex items-center space-x-2 p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                      isActive
                        ? `${status.bgColor} border-primary/50 shadow-sm`
                        : 'border-border/50 hover:bg-muted/50 dark:hover:bg-muted/30'
                    }`}
                    onClick={() => handleStatusToggle(status.id)}
                  >
                    <Checkbox
                      id={`status-${status.id}`}
                      checked={isActive}
                      onChange={() => {}}
                      className="border-border/50 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                    />
                    <Label htmlFor={`status-${status.id}`} className="flex items-center gap-2 cursor-pointer text-sm text-foreground">
                      <Icon className={`h-4 w-4 ${status.color}`} />
                      {status.name}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Filtros de Tipo */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <PackageIcon className="h-4 w-4" />
              Tipo de Produto
            </Label>
            <div className="space-y-2">
              {typeOptions.map((type) => {
                const Icon = type.icon;
                const isActive = filters.type.includes(type.id);
                return (
                  <div
                    key={type.id}
                    className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer transition-all duration-200 ${
                      isActive ? 'bg-primary/5 border-primary' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleTypeToggle(type.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type.id}`}
                        checked={isActive}
                        onChange={() => {}}
                      />
                      <Label htmlFor={`type-${type.id}`} className="flex items-center gap-2 cursor-pointer">
                        <Icon className="h-4 w-4 text-muted-foreground" />
                        {type.name}
                      </Label>
                    </div>
                                         <Badge className="text-xs bg-muted/50 text-muted-foreground">
                       {type.count}
                     </Badge>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Filtros de Visibilidade */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <EyeIcon className="h-4 w-4" />
              Visibilidade
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {visibilityOptions.map((visibility) => {
                const Icon = visibility.icon;
                const isActive = filters.visibility.includes(visibility.id);
                return (
                  <div
                    key={visibility.id}
                    className={`flex items-center space-x-2 p-2 rounded-lg border cursor-pointer transition-all duration-200 ${
                      isActive ? 'bg-primary/5 border-primary' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleVisibilityToggle(visibility.id)}
                  >
                    <Checkbox
                      id={`visibility-${visibility.id}`}
                      checked={isActive}
                      onChange={() => {}}
                    />
                    <Label htmlFor={`visibility-${visibility.id}`} className="flex items-center gap-1 cursor-pointer text-xs">
                      <Icon className={`h-3 w-3 ${visibility.color}`} />
                      {visibility.name}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Filtros de Preço */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <DollarSignIcon className="h-4 w-4" />
              Faixa de Preço
            </Label>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="min-price" className="text-xs text-muted-foreground">Mínimo</Label>
                  <Input
                    id="min-price"
                    type="number"
                    placeholder="0,00"
                    value={filters.priceRange.min || ''}
                    onChange={(e) => handleFilterChange('priceRange', {
                      ...filters.priceRange,
                      min: e.target.value ? parseFloat(e.target.value) : null
                    })}
                    className="text-sm"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-price" className="text-xs text-muted-foreground">Máximo</Label>
                  <Input
                    id="max-price"
                    type="number"
                    placeholder="999,99"
                    value={filters.priceRange.max || ''}
                    onChange={(e) => handleFilterChange('priceRange', {
                      ...filters.priceRange,
                      max: e.target.value ? parseFloat(e.target.value) : null
                    })}
                    className="text-sm"
                  />
                </div>
              </div>
              {filters.priceRange.min !== null || filters.priceRange.max !== null ? (
                <div className="text-xs text-muted-foreground">
                  {filters.priceRange.min !== null && filters.priceRange.max !== null
                    ? `${formatPrice(filters.priceRange.min)} - ${formatPrice(filters.priceRange.max)}`
                    : filters.priceRange.min !== null
                    ? `A partir de ${formatPrice(filters.priceRange.min)}`
                    : `Até ${formatPrice(filters.priceRange.max!)}`
                  }
                </div>
              ) : null}
            </div>
          </div>

          <Separator />

          {/* Filtros de Idioma e Nível */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Idioma
              </Label>
              <div className="space-y-2">
                {languageOptions.map((language) => {
                  const isActive = filters.language.includes(language.id);
                  return (
                    <div
                      key={language.id}
                      className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer transition-all duration-200 ${
                        isActive ? 'bg-primary/5 border-primary' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => handleLanguageToggle(language.id)}
                    >
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`language-${language.id}`}
                          checked={isActive}
                          onChange={() => {}}
                        />
                        <Label htmlFor={`language-${language.id}`} className="cursor-pointer text-sm">
                          {language.name}
                        </Label>
                      </div>
                                             <Badge className="text-xs bg-muted/50 text-muted-foreground">
                         {language.count}
                       </Badge>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="space-y-3">
              <Label className="text-sm font-medium flex items-center gap-2">
                <StarIcon className="h-4 w-4" />
                Nível
              </Label>
              <div className="space-y-2">
                {levelOptions.map((level) => {
                  const isActive = filters.level.includes(level.id);
                  return (
                    <div
                      key={level.id}
                      className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer transition-all duration-200 ${
                        isActive ? 'bg-primary/5 border-primary' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => handleLevelToggle(level.id)}
                    >
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`level-${level.id}`}
                          checked={isActive}
                          onChange={() => {}}
                        />
                        <Label htmlFor={`level-${level.id}`} className="cursor-pointer text-sm">
                          {level.name}
                        </Label>
                      </div>
                                             <Badge className="text-xs bg-muted/50 text-muted-foreground">
                         {level.count}
                       </Badge>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <Separator />

          {/* Filtros de Características */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <ZapIcon className="h-4 w-4" />
              Características
            </Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="has-commission" className="cursor-pointer text-sm">
                  Apenas produtos com comissão
                </Label>
                <Switch
                  id="has-commission"
                  checked={filters.hasCommission === true}
                  onCheckedChange={(checked) => handleFilterChange('hasCommission', checked ? true : null)}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="has-certificate" className="cursor-pointer text-sm">
                  Apenas produtos com certificado
                </Label>
                <Switch
                  id="has-certificate"
                  checked={filters.hasCertificate === true}
                  onCheckedChange={(checked) => handleFilterChange('hasCertificate', checked ? true : null)}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="is-downloadable" className="cursor-pointer text-sm">
                  Apenas produtos baixáveis
                </Label>
                <Switch
                  id="is-downloadable"
                  checked={filters.isDownloadable === true}
                  onCheckedChange={(checked) => handleFilterChange('isDownloadable', checked ? true : null)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Ordenação */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <TrendingUpIcon className="h-4 w-4" />
              Ordenar por
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <Select
                value={filters.sortBy}
                onValueChange={(value) => handleFilterChange('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.id} value={option.id}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={filters.sortOrder}
                onValueChange={(value: "asc" | "desc") => handleFilterChange('sortOrder', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Decrescente</SelectItem>
                  <SelectItem value="asc">Crescente</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Botões de Ação */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={clearAllFilters}
              className="flex-1"
              disabled={activeFiltersCount === 0}
            >
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Limpar Filtros
            </Button>
            <Button
              onClick={() => setIsOpen(false)}
              className="flex-1"
              disabled={loading}
            >
              {loading ? (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                  Aplicando...
                </>
              ) : (
                <>
                  <FilterIcon className="h-4 w-4 mr-2" />
                  Aplicar Filtros
                </>
              )}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
