"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import {
  PackageIcon,
  EditIcon,
  MoreHorizontalIcon,
  EyeIcon,
  PlusIcon,
  LinkIcon,
  BarChart3,
  CopyIcon,
  ExternalLinkIcon,
  FilterIcon,
  Grid3X3Icon,
  ListIcon,
  SearchIcon,
  SortAscIcon,
  SortDescIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon,
  UsersIcon,
  TrendingUpIcon,
  ZapIcon,
  GlobeIcon,
  BookOpenIcon,
  VideoIcon,
  FileTextIcon,
  Users2Icon,
  CreditCardIcon,
  ArchiveIcon,
} from "lucide-react";
import { ProductFilters } from "./ProductFiltersSheet";
import { Product } from "../hooks/useProducts";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@ui/components/dropdown-menu";
import { toast } from "sonner";
import { cn } from "@ui/lib";

interface ProductsGridProps {
  organizationId: string;
  filters?: ProductFilters;
  products?: Product[];
  loading?: boolean;
  onQuickAction?: (product: Product, action: string) => void;
  linkLoading?: boolean;
  onFiltersChange?: (filters: ProductFilters) => void;
  onViewModeChange?: (mode: "grid" | "list") => void;
  viewMode?: "grid" | "list";
}

export function ProductsGrid({
  organizationId,
  filters,
  products = [],
  loading = false,
  onQuickAction,
  linkLoading = false,
  onFiltersChange,
  onViewModeChange,
  viewMode = "grid"
}: ProductsGridProps) {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<"name" | "price" | "createdAt" | "updatedAt">("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const getStatusBadge = (status: Product["status"]) => {
    const statusConfig = {
      "PUBLISHED": {
        label: "PUBLICADO",
        className: "bg-emerald-500/10 text-emerald-600 border-emerald-500/20 dark:bg-emerald-400/10 dark:text-emerald-400 dark:border-emerald-400/20"
      },
      "DRAFT": {
        label: "RASCUNHO",
        className: "bg-amber-500/10 text-amber-600 border-amber-500/20 dark:bg-amber-400/10 dark:text-amber-400 dark:border-amber-400/20"
      },
      "ARCHIVED": {
        label: "ARQUIVADO",
        className: "bg-slate-500/10 text-slate-600 border-slate-500/20 dark:bg-slate-400/10 dark:text-slate-400 dark:border-slate-400/20"
      },
      "SUSPENDED": {
        label: "SUSPENSO",
        className: "bg-red-500/10 text-red-600 border-red-500/20 dark:bg-red-400/10 dark:text-red-400 dark:border-red-400/20"
      },
    };

    const config = statusConfig[status] || statusConfig["DRAFT"];

    return (
      <Badge className={cn("text-xs font-medium border", config.className)}>
        {config.label}
      </Badge>
    );
  };

  const getTypeBadge = (type: Product["type"]) => {
    const typeConfig = {
      "COURSE": {
        label: "CURSO",
        icon: BookOpenIcon,
        color: "bg-blue-500/10 text-blue-600 border-blue-500/20 dark:bg-blue-400/10 dark:text-blue-400 dark:border-blue-400/20"
      },
      "EBOOK": {
        label: "E-BOOK",
        icon: FileTextIcon,
        color: "bg-purple-500/10 text-purple-600 border-purple-500/20 dark:bg-purple-400/10 dark:text-purple-400 dark:border-purple-400/20"
      },
      		"MENTORSHIP": {
        label: "MENTORIA",
        icon: Users2Icon,
        color: "bg-orange-500/10 text-orange-600 border-orange-500/20 dark:bg-orange-400/10 dark:text-orange-400 dark:border-orange-400/20"
      },
      "SUBSCRIPTION": {
        label: "ASSINATURA",
        icon: CreditCardIcon,
        color: "bg-emerald-500/10 text-emerald-600 border-emerald-500/20 dark:bg-emerald-400/10 dark:text-emerald-400 dark:border-emerald-400/20"
      },
      "BUNDLE": {
        label: "PACOTE",
        icon: PackageIcon,
        color: "bg-indigo-500/10 text-indigo-600 border-indigo-500/20 dark:bg-indigo-400/10 dark:text-indigo-400 dark:border-indigo-400/20"
      },
    };

    const config = typeConfig[type] || typeConfig["COURSE"];
    const Icon = config.icon;

    return (
      <Badge className={cn("text-xs font-medium border flex items-center gap-1", config.color)}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getVisibilityBadge = (visibility: Product["visibility"]) => {
    const visibilityConfig = {
      "PUBLIC": {
        label: "PÚBLICO",
        className: "bg-emerald-500/10 text-emerald-600 border-emerald-500/20 dark:bg-emerald-400/10 dark:text-emerald-400 dark:border-emerald-400/20"
      },
      "PRIVATE": {
        label: "PRIVADO",
        className: "bg-blue-500/10 text-blue-600 border-blue-500/20 dark:bg-blue-400/10 dark:text-blue-400 dark:border-blue-400/20"
      },
      "UNLISTED": {
        label: "NÃO LISTADO",
        className: "bg-amber-500/10 text-amber-600 border-amber-500/20 dark:bg-amber-400/10 dark:text-amber-400 dark:border-amber-400/20"
      },
    };

    const config = visibilityConfig[visibility] || visibilityConfig["PRIVATE"];

    return (
      <Badge className={cn("text-xs font-medium border", config.className)}>
        {config.label}
      </Badge>
    );
  };

  const formatPrice = (priceCents: number, currency: string = "BRL") => {
    const price = priceCents / 100;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  };

  const handleQuickAction = (product: Product, action: string) => {
    if (onQuickAction) {
      onQuickAction(product, action);
    }
  };

  const handleProductSelect = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };

  const sortedProducts = [...products].sort((a, b) => {
    let aValue: any, bValue: any;

    switch (sortBy) {
      case "name":
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case "price":
        aValue = a.priceCents;
        bValue = b.priceCents;
        break;
      case "createdAt":
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
      case "updatedAt":
        aValue = new Date(a.updatedAt).getTime();
        bValue = new Date(b.updatedAt).getTime();
        break;
      default:
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
    }

    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header com Skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-32" />
          </div>
        </div>

        {/* Grid de Skeleton */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="space-y-4 animate-pulse">
              <CardHeader className="pb-3">
                <Skeleton className="aspect-square w-full rounded-lg" />
                <Skeleton className="h-6 w-3/4" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                </div>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sorting Controls - Simplified */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {selectedProducts.length > 0 && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{selectedProducts.length} selecionado{selectedProducts.length !== 1 ? 's' : ''}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedProducts([])}
              >
                Limpar seleção
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Ordenação */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                {sortOrder === "asc" ? <SortAscIcon className="h-4 w-4 mr-2" /> : <SortDescIcon className="h-4 w-4 mr-2" />}
                Ordenar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ordenar por</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setSortBy("name")}>
                Nome {sortBy === "name" && (sortOrder === "asc" ? "↑" : "↓")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("price")}>
                Preço {sortBy === "price" && (sortOrder === "asc" ? "↑" : "↓")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("createdAt")}>
                Data de Criação {sortBy === "createdAt" && (sortOrder === "asc" ? "↑" : "↓")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("updatedAt")}>
                Última Atualização {sortBy === "updatedAt" && (sortOrder === "asc" ? "↑" : "↓")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                {sortOrder === "asc" ? "Ordem Decrescente" : "Ordem Crescente"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Seleção em Lote */}
      {products.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-muted/30 dark:bg-muted/20 rounded-lg border border-border/50">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={selectedProducts.length === products.length}
              onChange={handleSelectAll}
              className="rounded border-border bg-background text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background"
            />
            <span className="text-sm text-muted-foreground">
              {selectedProducts.length === 0
                ? "Selecionar todos"
                : `${selectedProducts.length} de ${products.length} selecionados`
              }
            </span>
          </div>

          {selectedProducts.length > 0 && (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="h-8">
                <CopyIcon className="h-3 w-3 mr-1" />
                Copiar
              </Button>
              <Button variant="outline" size="sm" className="h-8">
                <ArchiveIcon className="h-3 w-3 mr-1" />
                Arquivar
              </Button>
              <Button variant="error" size="sm" className="h-8">
                Deletar
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Grid de Produtos */}
      {viewMode === "grid" ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {sortedProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              isSelected={selectedProducts.includes(product.id)}
              onSelect={() => handleProductSelect(product.id)}
              onQuickAction={handleQuickAction}
              linkLoading={linkLoading}
              getStatusBadge={getStatusBadge}
              getTypeBadge={getTypeBadge}
              getVisibilityBadge={getVisibilityBadge}
              formatPrice={formatPrice}
              formatDate={formatDate}
              formatDuration={formatDuration}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {sortedProducts.map((product) => (
            <ProductListItem
              key={product.id}
              product={product}
              isSelected={selectedProducts.includes(product.id)}
              onSelect={() => handleProductSelect(product.id)}
              onQuickAction={handleQuickAction}
              linkLoading={linkLoading}
              getStatusBadge={getStatusBadge}
              getTypeBadge={getTypeBadge}
              getVisibilityBadge={getVisibilityBadge}
              formatPrice={formatPrice}
              formatDate={formatDate}
              formatDuration={formatDuration}
            />
          ))}
        </div>
      )}

      {/* Estado Vazio */}
      {products.length === 0 && !loading && (
        <div className="text-center py-16">
          <div className="w-20 h-20 bg-muted/50 dark:bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <PackageIcon className="h-10 w-10 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Nenhum produto encontrado</h3>
          <p className="text-muted-foreground max-w-md mx-auto">
            Comece criando seu primeiro produto usando o botão "Novo Produto" acima.
          </p>
        </div>
      )}
    </div>
  );
}

// Componente de Card de Produto
function ProductCard({
  product,
  isSelected,
  onSelect,
  onQuickAction,
  linkLoading,
  getStatusBadge,
  getTypeBadge,
  getVisibilityBadge,
  formatPrice,
  formatDate,
  formatDuration,
}: {
  product: Product;
  isSelected: boolean;
  onSelect: () => void;
  onQuickAction: (product: Product, action: string) => void;
  linkLoading: boolean;
  getStatusBadge: (status: Product["status"]) => JSX.Element;
  getTypeBadge: (type: Product["type"]) => JSX.Element;
  getVisibilityBadge: (visibility: Product["visibility"]) => JSX.Element;
  formatPrice: (price: number, currency: string) => string;
  formatDate: (date: string) => string;
  formatDuration: (minutes: number) => string;
}) {
  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary/5 dark:hover:shadow-primary/10 hover:-translate-y-1 border-border/50 hover:border-border",
      isSelected && "ring-2 ring-primary ring-offset-2 ring-offset-background"
    )}>
      {/* Checkbox de Seleção */}
      <div className="absolute top-3 left-3 z-10">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onSelect}
          className="rounded border-border bg-background text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background shadow-sm"
        />
      </div>

      <CardHeader className="pb-3">
        {/* Imagem do Produto */}
        <div className="aspect-square bg-muted/50 dark:bg-muted/30 rounded-lg mb-3 flex items-center justify-center overflow-hidden relative">
          {product.thumbnail ? (
            <img
              src={product.thumbnail}
              alt={product.name}
              className="w-full h-full object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 flex items-center justify-center">
              <PackageIcon className="h-16 w-16 text-blue-400 dark:text-blue-300" />
            </div>
          )}

          {/* Overlay com ações rápidas */}
          <div className="absolute inset-0 bg-black/60 dark:bg-black/70 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onQuickAction(product, "view")}
              className="h-8 w-8 p-0 bg-background/90 dark:bg-background/80 text-foreground hover:bg-background"
            >
              <EyeIcon className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onQuickAction(product, "edit")}
              className="h-8 w-8 p-0 bg-background/90 dark:bg-background/80 text-foreground hover:bg-background"
            >
              <EditIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Header do Card */}
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg font-semibold leading-tight line-clamp-2 text-foreground pr-2">
              {product.name}
            </CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-all duration-200 h-8 w-8 p-0 hover:bg-muted/50"
                >
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onQuickAction(product, "view")}>
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onQuickAction(product, "edit")}>
                  <EditIcon className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onQuickAction(product, "checkout")}>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Gerar Link de Checkout
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Tags */}
          <div className="flex items-center gap-2 flex-wrap">
            {getTypeBadge(product.type)}
            {getStatusBadge(product.status)}
            {getVisibilityBadge(product.visibility)}
            {product.category && (
              <Badge className="bg-purple-500/10 text-purple-600 border-purple-500/20 dark:bg-purple-400/10 dark:text-purple-400 dark:border-purple-400/20 text-xs font-medium">
                {product.category.name}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Descrição */}
          <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
            {product.shortDescription || product.description || "Sem descrição"}
          </p>

          {/* Preço e Informações */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="text-xl font-bold text-foreground">
                {formatPrice(product.priceCents, product.currency)}
              </div>
              {product.comparePriceCents && product.comparePriceCents > product.priceCents && (
                <Badge className="bg-red-500/10 text-red-600 border-red-500/20 dark:bg-red-400/10 dark:text-red-400 dark:border-red-400/20 text-xs font-medium">
                  {Math.round(((product.comparePriceCents - product.priceCents) / product.comparePriceCents) * 100)}% OFF
                </Badge>
              )}
            </div>

            {product.comparePriceCents && product.comparePriceCents > product.priceCents && (
              <div className="text-sm text-muted-foreground line-through">
                {formatPrice(product.comparePriceCents, product.currency)}
              </div>
            )}
          </div>

          {/* Informações adicionais */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {product.duration && (
              <span className="flex items-center gap-1">
                <ClockIcon className="h-3 w-3" />
                {formatDuration(product.duration)}
              </span>
            )}
            {product.level && (
              <span className="flex items-center gap-1">
                <StarIcon className="h-3 w-3" />
                {product.level}
              </span>
            )}
            {product.certificate && (
              <span className="flex items-center gap-1">
                <ZapIcon className="h-3 w-3" />
                Certificado
              </span>
            )}
          </div>

          {/* Estatísticas */}
          {product._count && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground border-t border-border/50 pt-3">
              <span className="flex items-center gap-1">
                <TrendingUpIcon className="h-3 w-3" />
                {product._count.orders} vendas
              </span>
              <span className="flex items-center gap-1">
                <UsersIcon className="h-3 w-3" />
                {product._count.enrollments} alunos
              </span>
              <span className="flex items-center gap-1">
                <StarIcon className="h-3 w-3" />
                {product._count.reviews} avaliações
              </span>
            </div>
          )}

          {/* Botões de Ação */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 hover:bg-muted/50 dark:hover:bg-muted/30"
              onClick={() => onQuickAction(product, "view")}
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              Visualizar
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 hover:bg-muted/50 dark:hover:bg-muted/30"
              onClick={() => onQuickAction(product, "edit")}
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Editar
            </Button>
          </div>

          {/* Botão de Checkout */}
          <Button
            variant="primary"
            size="sm"
            className="w-full shadow-sm shadow-primary/20 hover:shadow-primary/30 transition-shadow duration-200"
            onClick={() => onQuickAction(product, "checkout")}
            disabled={linkLoading}
          >
            {linkLoading ? (
              <>
                <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <LinkIcon className="h-4 w-4 mr-2" />
                Gerar Link de Checkout
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Componente de Lista de Produto
function ProductListItem({
  product,
  isSelected,
  onSelect,
  onQuickAction,
  linkLoading,
  getStatusBadge,
  getTypeBadge,
  getVisibilityBadge,
  formatPrice,
  formatDate,
  formatDuration,
}: {
  product: Product;
  isSelected: boolean;
  onSelect: () => void;
  onQuickAction: (product: Product, action: string) => void;
  linkLoading: boolean;
  getStatusBadge: (status: Product["status"]) => JSX.Element;
  getTypeBadge: (type: Product["type"]) => JSX.Element;
  getVisibilityBadge: (visibility: Product["visibility"]) => JSX.Element;
  formatPrice: (price: number, currency: string) => string;
  formatDate: (date: string) => string;
  formatDuration: (minutes: number) => string;
}) {
  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 dark:hover:shadow-primary/10 border-border/50 hover:border-border",
      isSelected && "ring-2 ring-primary ring-offset-2 ring-offset-background"
    )}>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Checkbox */}
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="rounded border-border bg-background text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background"
          />

          {/* Imagem */}
          <div className="w-16 h-16 bg-muted/50 dark:bg-muted/30 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
            {product.thumbnail ? (
              <img
                src={product.thumbnail}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <PackageIcon className="h-8 w-8 text-muted-foreground" />
            )}
          </div>

          {/* Informações */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-foreground truncate">{product.name}</h3>
                <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                  {product.shortDescription || product.description || "Sem descrição"}
                </p>
              </div>
              <div className="text-right ml-4">
                <div className="text-lg font-bold text-foreground">
                  {formatPrice(product.priceCents, product.currency)}
                </div>
                {product.comparePriceCents && product.comparePriceCents > product.priceCents && (
                  <div className="text-sm text-muted-foreground line-through">
                    {formatPrice(product.comparePriceCents, product.currency)}
                  </div>
                )}
              </div>
            </div>

            {/* Tags e Metadados */}
            <div className="flex items-center gap-2 mt-2 flex-wrap">
              {getTypeBadge(product.type)}
              {getStatusBadge(product.status)}
              {getVisibilityBadge(product.visibility)}
              {product.category && (
                <Badge className="bg-purple-500/10 text-purple-600 border-purple-500/20 dark:bg-purple-400/10 dark:text-purple-400 dark:border-purple-400/20 text-xs">
                  {product.category.name}
                </Badge>
            )}
            </div>

            {/* Informações adicionais */}
            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <CalendarIcon className="h-3 w-3" />
                Criado em {formatDate(product.createdAt)}
              </span>
              {product.duration && (
                <span className="flex items-center gap-1">
                  <ClockIcon className="h-3 w-3" />
                  {formatDuration(product.duration)}
                </span>
              )}
              {product.language && (
                <span className="flex items-center gap-1">
                  <GlobeIcon className="h-3 w-3" />
                  {product.language}
                </span>
              )}
            </div>
          </div>

          {/* Ações */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQuickAction(product, "view")}
              className="hover:bg-muted/50 dark:hover:bg-muted/30"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              Visualizar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQuickAction(product, "edit")}
              className="hover:bg-muted/50 dark:hover:bg-muted/30"
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Editar
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={() => onQuickAction(product, "checkout")}
              disabled={linkLoading}
              className="shadow-sm shadow-primary/20 hover:shadow-primary/30 transition-shadow duration-200"
            >
              {linkLoading ? (
                <>
                  <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Checkout
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}


