"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Sheet, SheetContent, SheetHeader, She<PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger, SheetFooter } from "@ui/components/sheet";
import { ScrollArea } from "@ui/components/scroll-area";
import { PlusIcon } from "lucide-react";
import { useProducts } from "@saas/products/hooks/useProducts";
import { CreateProductData } from "@saas/products/hooks/useProducts";

const createProductSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  slug: z.string().min(1, "Slug é obrigatório").regex(/^[a-z0-9-]+$/, "Slug deve conter apenas letras minúsculas, números e hífens"),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  priceCents: z.number().min(0, "Preço deve ser maior ou igual a 0"),
  comparePriceCents: z.number().min(0).optional(),
  currency: z.string().default("BRL"),
  		type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
  visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  duration: z.number().min(0).optional(),
  level: z.string().optional(),
  language: z.string().default("pt-BR"),
  certificate: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
});

type CreateProductFormData = z.infer<typeof createProductSchema>;

interface CreateProductSheetProps {
  organization: any;
  onProductCreated?: () => void;
}

export function CreateProductSheet({ organization, onProductCreated }: CreateProductSheetProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [features, setFeatures] = useState<string[]>([]);
  const [requirements, setRequirements] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [newFeature, setNewFeature] = useState("");
  const [newRequirement, setNewRequirement] = useState("");

  const { createProduct } = useProducts(organization.id);

  const form = useForm<CreateProductFormData>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      currency: "BRL",
      status: "DRAFT",
      visibility: "PRIVATE",
      type: "COURSE",
      language: "pt-BR",
      certificate: false,
      downloadable: false,
      checkoutType: "DEFAULT",
    },
  });

  const onSubmit = async (data: CreateProductFormData) => {
    try {
      setLoading(true);

      const productData: CreateProductData = {
        ...data,
        tags,
        features,
        requirements,
        settings: {},
      };

      const product = await createProduct(productData);

      if (product) {
        toast.success("Produto criado com sucesso!");
        setOpen(false);
        form.reset();
        setTags([]);
        setFeatures([]);
        setRequirements([]);
        onProductCreated?.();
      }
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Erro ao criar produto");
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setFeatures(features.filter(feature => feature !== featureToRemove));
  };

  const addRequirement = () => {
    if (newRequirement.trim() && !requirements.includes(newRequirement.trim())) {
      setRequirements([...requirements, newRequirement.trim()]);
      setNewRequirement("");
    }
  };

  const removeRequirement = (requirementToRemove: string) => {
    setRequirements(requirements.filter(requirement => requirement !== requirementToRemove));
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="primary">
          <PlusIcon className="h-4 w-4 mr-2" />
          Novo Produto
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[600px] sm:w-[800px]">
        <SheetHeader className="pb-4 border-b">
          <SheetTitle className="text-xl font-semibold text-foreground">Criar Novo Produto</SheetTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Preencha as informações básicas para criar seu produto
          </p>
        </SheetHeader>

        <ScrollArea className="h-[calc(100vh-120px)] pr-4">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 border-b border-border/50">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <h3 className="text-lg font-semibold text-foreground">Informações Básicas</h3>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome do Produto *</Label>
                  <Input
                    id="name"
                    placeholder="Ex: Curso de Marketing Digital"
                    {...form.register("name")}
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <span className="text-xs">⚠️</span>
                      {form.formState.errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug *</Label>
                  <Input
                    id="slug"
                    placeholder="curso-marketing-digital"
                    {...form.register("slug")}
                  />
                  {form.formState.errors.slug && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <span className="text-xs">⚠️</span>
                      {form.formState.errors.slug.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Tipo de Produto *</Label>
                <Select onValueChange={(value) => form.setValue("type", value as any)} defaultValue={form.getValues("type")}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="COURSE">Curso</SelectItem>
                    <SelectItem value="EBOOK">E-book</SelectItem>
                    						<SelectItem value="MENTORSHIP">Mentoria</SelectItem>
                    <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
                    <SelectItem value="BUNDLE">Pacote</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shortDescription">Descrição Curta</Label>
                <Textarea
                  id="shortDescription"
                  placeholder="Breve descrição do produto"
                  rows={2}
                  {...form.register("shortDescription")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição Completa</Label>
                <Textarea
                  id="description"
                  placeholder="Descrição detalhada do produto"
                  rows={4}
                  {...form.register("description")}
                />
              </div>
            </div>

            {/* Pricing */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pb-2 border-b border-border/50">
                <div className="w-2 h-2 bg-success rounded-full"></div>
                <h3 className="text-lg font-semibold text-foreground">Preços</h3>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="priceCents">Preço (em centavos) *</Label>
                  <Input
                    id="priceCents"
                    type="number"
                    placeholder="Ex: 29700 (R$ 297,00)"
                    {...form.register("priceCents", { valueAsNumber: true })}
                  />
                  {form.formState.errors.priceCents && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <span className="text-xs">⚠️</span>
                      {form.formState.errors.priceCents.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="comparePriceCents">Preço Comparativo (em centavos)</Label>
                  <Input
                    id="comparePriceCents"
                    type="number"
                    placeholder="Ex: 49700 (R$ 497,00)"
                    {...form.register("comparePriceCents", { valueAsNumber: true })}
                  />
                </div>
              </div>
            </div>

            {/* Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configurações</h3>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select onValueChange={(value) => form.setValue("status", value as any)} defaultValue={form.getValues("status")}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DRAFT">Rascunho</SelectItem>
                      <SelectItem value="PUBLISHED">Publicado</SelectItem>
                      <SelectItem value="ARCHIVED">Arquivado</SelectItem>
                      <SelectItem value="SUSPENDED">Suspenso</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="visibility">Visibilidade</Label>
                  <Select onValueChange={(value) => form.setValue("visibility", value as any)} defaultValue={form.getValues("visibility")}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PUBLIC">Público</SelectItem>
                      <SelectItem value="PRIVATE">Privado</SelectItem>
                      <SelectItem value="UNLISTED">Não Listado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="certificate"
                    checked={form.watch("certificate")}
                    onCheckedChange={(checked) => form.setValue("certificate", checked)}
                  />
                  <Label htmlFor="certificate">Certificado</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="downloadable"
                    checked={form.watch("downloadable")}
                    onCheckedChange={(checked) => form.setValue("downloadable", checked)}
                  />
                  <Label htmlFor="downloadable">Download</Label>
                </div>
              </div>
            </div>
          </form>
        </ScrollArea>

        <SheetFooter className="border-t border-border/50 pt-6 bg-muted/20 dark:bg-muted/10">
          <div className="flex items-center justify-between w-full">
            <p className="text-sm text-muted-foreground">
              💡 Você pode editar essas informações depois
            </p>
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={loading}
                className="min-w-[100px] shadow-sm border-border/50 hover:bg-muted/50 dark:hover:bg-muted/30"
              >
                Cancelar
              </Button>
              <Button
                variant="primary"
                onClick={form.handleSubmit(onSubmit)}
                disabled={loading}
                className="min-w-[120px] shadow-sm shadow-primary/20 hover:shadow-primary/30 transition-shadow duration-200"
              >
                {loading ? "Criando..." : "Criar Produto"}
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
