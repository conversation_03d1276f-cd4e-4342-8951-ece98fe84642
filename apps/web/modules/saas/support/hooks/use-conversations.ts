'use client'

import { useState, useEffect, useCallback } from "react";

interface Conversation {
  id: string;
  contact: {
    id: string;
    name: string;
    avatar: string;
    email?: string;
    phone?: string;
  };
  lastMessage: string;
  timestamp: string;
  unread: boolean;
  starred: boolean;
  channel: "whatsapp" | "sms" | "email";
  status: "open" | "pending" | "resolved";
}

interface UseConversationsOptions {
  page?: number;
  limit?: number;
  status?: string;
  channel?: string;
}

export function useConversations(options: UseConversationsOptions = {}) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: options.page || 1,
    limit: options.limit || 20,
    total: 0,
  });

  const fetchConversations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (options.status) {
        queryParams.append("status", options.status);
      }

      if (options.channel) {
        queryParams.append("channel", options.channel);
      }

      const response = await fetch(`/api/support/conversations?${queryParams}`);

      if (!response.ok) {
        throw new Error("Erro ao buscar conversas");
      }

      const data = await response.json();

      if (data.success) {
        setConversations(data.data);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
        }));
      } else {
        throw new Error(data.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, options.status, options.channel]);

  const createConversation = useCallback(async (data: {
    contactId: string;
    message?: string;
    channel: "whatsapp" | "sms" | "email";
  }) => {
    try {
      const response = await fetch("/api/support/conversations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Erro ao criar conversa");
      }

      const result = await response.json();

      if (result.success) {
        // Recarregar conversas após criar uma nova
        await fetchConversations();
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, [fetchConversations]);

  const updateConversation = useCallback(async (
    id: string,
    data: {
      status?: "open" | "pending" | "resolved" | "closed";
      priority?: "low" | "medium" | "high" | "urgent";
      assigneeId?: string;
      tags?: string[];
    }
  ) => {
    try {
      const response = await fetch(`/api/support/conversations/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Erro ao atualizar conversa");
      }

      const result = await response.json();

      if (result.success) {
        // Atualizar conversa local
        setConversations(prev =>
          prev.map(conv =>
            conv.id === id
              ? { ...conv, ...data }
              : conv
          )
        );
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, []);

  const closeConversation = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/support/conversations/${id}/close`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Erro ao fechar conversa");
      }

      const result = await response.json();

      if (result.success) {
        // Atualizar status local
        setConversations(prev =>
          prev.map(conv =>
            conv.id === id
              ? { ...conv, status: "closed" as const }
              : conv
          )
        );
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, []);

  const assignConversation = useCallback(async (id: string, assigneeId: string) => {
    try {
      const response = await fetch(`/api/support/conversations/${id}/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ assigneeId }),
      });

      if (!response.ok) {
        throw new Error("Erro ao atribuir conversa");
      }

      const result = await response.json();

      if (result.success) {
        // Atualizar conversa local
        setConversations(prev =>
          prev.map(conv =>
            conv.id === id
              ? { ...conv, assigneeId }
              : conv
          )
        );
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, []);

  const starConversation = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/support/conversations/${id}/star`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Erro ao marcar conversa como favorita");
      }

      const result = await response.json();

      if (result.success) {
        // Atualizar conversa local
        setConversations(prev =>
          prev.map(conv =>
            conv.id === id
              ? { ...conv, starred: true }
              : conv
          )
        );
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, []);

  const unstarConversation = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/support/conversations/${id}/star`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Erro ao remover favorita da conversa");
      }

      const result = await response.json();

      if (result.success) {
        // Atualizar conversa local
        setConversations(prev =>
          prev.map(conv =>
            conv.id === id
              ? { ...conv, starred: false }
              : conv
          )
        );
        return result.data;
      } else {
        throw new Error(result.error || "Erro desconhecido");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      throw err;
    }
  }, []);

  const setPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const setLimit = useCallback((limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  }, []);

  // Carregar conversas na montagem e quando as dependências mudarem
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  return {
    conversations,
    loading,
    error,
    pagination,
    fetchConversations,
    createConversation,
    updateConversation,
    closeConversation,
    assignConversation,
    starConversation,
    unstarConversation,
    setPage,
    setLimit,
  };
}
