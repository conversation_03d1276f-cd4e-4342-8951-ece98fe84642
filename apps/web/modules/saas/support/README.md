# Módulo de Suporte - Interface WhatsApp

Este módulo implementa um sistema de atendimento omnichannel com interface similar ao WhatsApp, integrado ao Chatwoot para gerenciar conversas de WhatsApp, SMS e email.

## 🚀 Funcionalidades

### Core Features
- **Interface WhatsApp**: Design familiar e intuitivo
- **Lista de Conversas**: Visualização de todas as conversas ativas
- **Chat Individual**: Interface de chat completa com histórico
- **Filtros Inteligentes**: Todas, Não lidas, Favoritas
- **Busca Rápida**: Pesquisa por nome ou mensagem
- **Status de Mensagens**: Enviado, Entregue, Lido

### UX/UI WhatsApp
- **Header Limpo**: Título e ações principais
- **Filtros Visuais**: Botões de filtro estilo WhatsApp
- **Lista de Conversas**: Avatares, status online, timestamps
- **Chat Individual**: <PERSON><PERSON><PERSON> de mensagem, status de leitura
- **Input Intuitivo**: Campo de mensagem com anexos e emojis

### Integração Chatwoot
- **API REST**: Comunicação bidirecional com o Chatwoot
- **Webhooks**: Recebimento de eventos em tempo real
- **Multi-canal**: Suporte a WhatsApp, SMS e Email

## 🏗️ Arquitetura

### Frontend
```
apps/web/modules/saas/support/
├── page.tsx                    # Página principal com interface WhatsApp
├── hooks/
│   └── use-conversations.ts    # Hook para gerenciar conversas
└── index.ts                    # Exportações do módulo
```

### Backend
```
packages/api/src/
├── routes/support/
│   ├── router.ts               # Router principal
│   ├── conversations.ts        # Rotas de conversas
│   ├── contacts.ts             # Rotas de contatos
│   ├── messages.ts             # Rotas de mensagens
│   └── webhooks.ts             # Webhooks do Chatwoot
└── services/
    └── chatwoot.ts             # Serviço de integração
```

## 🎨 Interface WhatsApp

### Lista de Conversas
- **Header**: Título "Suporte" com filtros e menu
- **Filtros**: Todas, Não lidas, Favoritas
- **Busca**: Campo de pesquisa com ícone
- **Conversas**: Lista com avatares, nomes, mensagens e timestamps
- **Status**: Indicadores visuais de online/offline/away

### Chat Individual
- **Header**: Nome do contato, status, botões de ação (telefone, vídeo)
- **Mensagens**: Bolhas estilo WhatsApp com timestamps e status
- **Input**: Campo de mensagem com anexos, emojis e botão de envio
- **Navegação**: Botão de voltar para lista de conversas

## 🔧 Configuração

### Variáveis de Ambiente

```bash
# Chatwoot Configuration
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_ACCOUNT_ID=your_account_id
CHATWOOT_API_ACCESS_TOKEN=your_api_token

# Inbox IDs
CHATWOOT_WHATSAPP_INBOX_ID=1
CHATWOOT_SMS_INBOX_ID=2
CHATWOOT_EMAIL_INBOX_ID=3

# Webhooks
CHATWOOT_WEBHOOKS_ENABLED=true
CHATWOOT_WEBHOOK_SECRET=your_webhook_secret
```

## 📱 Uso

### Acessar o Suporte
1. Navegar para `/app/{organization}/support`
2. Interface similar ao WhatsApp
3. Lista de conversas com filtros
4. Clique em uma conversa para abrir o chat

### Gerenciar Conversas
- **Filtros**: Todas, Não lidas, Favoritas
- **Busca**: Por nome do contato ou mensagem
- **Status**: Visualização de conversas não lidas
- **Navegação**: Entre lista e chat individual

### Responder Mensagens
- **Input**: Campo de mensagem com anexos
- **Envio**: Enter ou botão de envio
- **Status**: Indicadores de envio/entrega/leitura
- **Histórico**: Visualização completa da conversa

## 🔌 API Endpoints

### Conversas
```
GET    /api/support/conversations     # Listar conversas
GET    /api/support/conversations/:id # Obter conversa
POST   /api/support/conversations     # Criar conversa
PATCH  /api/support/conversations/:id # Atualizar conversa
POST   /api/support/conversations/:id/close    # Fechar conversa
POST   /api/support/conversations/:id/reopen   # Reabrir conversa
POST   /api/support/conversations/:id/assign   # Atribuir conversa
POST   /api/support/conversations/:id/star     # Marcar favorita
DELETE /api/support/conversations/:id/star     # Remover favorita
```

### Mensagens
```
POST   /api/support/conversations/:id/messages # Enviar mensagem
GET    /api/support/conversations/:id/messages # Listar mensagens
POST   /api/support/conversations/:id/private  # Nota privada
POST   /api/support/messages/:id/read         # Marcar como lida
DELETE /api/support/messages/:id              # Deletar mensagem
```

### Webhooks
```
POST   /api/support/webhooks/conversations    # Webhook de conversas
POST   /api/support/webhooks/messages         # Webhook de mensagens
POST   /api/support/webhooks/contacts         # Webhook de contatos
GET    /api/support/webhooks/health           # Status dos webhooks
```

## 🚀 Roadmap

### Fase 1 (Atual)
- ✅ Interface estilo WhatsApp
- ✅ Lista de conversas com filtros
- ✅ Chat individual completo
- ✅ Integração com API Chatwoot

### Fase 2 (Próxima)
- 🔄 WebSockets para real-time
- 🔄 Notificações push
- 🔄 Upload de arquivos
- 🔄 Emojis e stickers

### Fase 3 (Futura)
- 📋 Chatbot IA integrado
- 📋 Métricas e relatórios
- 📋 Automações avançadas
- 📋 Integração com CRM

## 🛠️ Desenvolvimento

### Instalar Dependências
```bash
pnpm install
```

### Executar em Desenvolvimento
```bash
pnpm dev
```

### Testes
```bash
pnpm test
```

### Build
```bash
pnpm build
```

## 📚 Recursos

- [Documentação Chatwoot](https://developers.chatwoot.com/)
- [API Reference](https://developers.chatwoot.com/api)
- [Webhooks Guide](https://developers.chatwoot.com/webhooks)
- [Componentes UI](https://ui.shadcn.com/)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para mais detalhes.
