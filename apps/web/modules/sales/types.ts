export interface Sale {
  id: string;
  organizationId: string;
  productId: string;
  customerId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'approved' | 'failed' | 'refunded' | 'cancelled';
  paymentMethod: 'pix' | 'card' | 'boleto';
  paymentProvider: string;
  transactionId: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
  refundedAt?: Date;
}

export interface SaleAnalytics {
  totalSales: number;
  totalTransactions: number;
  averageTicket: number;
  conversionRate: number;
  refundRate: number;
  topProducts: Array<{
    productId: string;
    productName: string;
    sales: number;
    revenue: number;
  }>;
  salesByMethod: Array<{
    method: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
  salesByPeriod: Array<{
    period: string;
    sales: number;
    revenue: number;
  }>;
}

export interface SaleFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  status?: Sale['status'][];
  paymentMethod?: Sale['paymentMethod'][];
  productId?: string[];
  customerId?: string[];
  minAmount?: number;
  maxAmount?: number;
}

export interface RefundRequest {
  saleId: string;
  amount: number;
  reason: string;
  description?: string;
}
