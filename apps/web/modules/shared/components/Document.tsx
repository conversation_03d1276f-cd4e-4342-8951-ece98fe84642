import { ClientProviders } from "@shared/components/ClientProviders";
import { ConsentProvider } from "@shared/components/ConsentProvider";
import { cn } from "@ui/lib";
import { GeistSans } from "geist/font/sans";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import type { PropsWithChildren } from "react";
import { safeBuildTimeAsync } from "@shared/lib/build-time";

export async function Document({
	children,
	locale,
}: PropsWithChildren<{ locale: string }>) {
	const consentValue = await safeBuildTimeAsync(false, async () => {
		const { cookies } = await import("next/headers");
		const cookieStore = await cookies();
		const consentCookie = cookieStore.get("consent");
		return consentCookie?.value === "true";
	});

	return (
		<html
			lang={locale}
			suppressHydrationWarning
			className={GeistSans.variable}
		>
			<body
				className={cn(
					"min-h-screen bg-background text-foreground antialiased",
				)}
			>
				<NuqsAdapter>
					<ConsentProvider
						initialConsent={consentValue}
					>
						<ClientProviders>{children}</ClientProviders>
					</ConsentProvider>
				</NuqsAdapter>
			</body>
		</html>
	);
}
