"use client";

import { formatCurrencyFromCents } from "../../../../lib/utils";
import { useLocaleCurrency } from "../../hooks/use-locale-currency";
import { cn } from "../../../../lib/utils";

interface PriceDisplayProps {
  priceInCents: number;
  originalPriceInCents?: number;
  currency?: string;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  showDiscount?: boolean;
}

const sizeClasses = {
  sm: "text-sm",
  md: "text-base",
  lg: "text-lg",
  xl: "text-xl font-bold",
};

export function PriceDisplay({
  priceInCents,
  originalPriceInCents,
  currency,
  className,
  size = "md",
  showDiscount = true,
}: PriceDisplayProps) {
  const localeCurrency = useLocaleCurrency();
  const displayCurrency = currency || localeCurrency;

  const currentPrice = formatCurrencyFromCents(priceInCents, displayCurrency);
  const originalPrice = originalPriceInCents
    ? formatCurrencyFromCents(originalPriceInCents, displayCurrency)
    : null;

  const hasDiscount = originalPriceInCents && originalPriceInCents > priceInCents;
  const discountPercentage = hasDiscount
    ? Math.round(((originalPriceInCents - priceInCents) / originalPriceInCents) * 100)
    : 0;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className={cn("font-semibold text-green-600", sizeClasses[size])}>
        {currentPrice}
      </span>
      
      {hasDiscount && originalPrice && (
        <>
          <span className={cn("text-gray-500 line-through", sizeClasses.sm)}>
            {originalPrice}
          </span>
          {showDiscount && (
            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
              -{discountPercentage}%
            </span>
          )}
        </>
      )}
    </div>
  );
}

export function SimplePriceDisplay({
  priceInCents,
  currency,
  className,
  size = "md",
}: Pick<PriceDisplayProps, "priceInCents" | "currency" | "className" | "size">) {
  const localeCurrency = useLocaleCurrency();
  const displayCurrency = currency || localeCurrency;
  const formattedPrice = formatCurrencyFromCents(priceInCents, displayCurrency);

  return (
    <span className={cn("font-medium", sizeClasses[size], className)}>
      {formattedPrice}
    </span>
  );
}