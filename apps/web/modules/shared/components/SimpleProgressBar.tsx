"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

export function SimpleProgressBar() {
	const [isLoading, setIsLoading] = useState(false);
	const pathname = usePathname();

	useEffect(() => {
		setIsLoading(true);
		const timer = setTimeout(() => setIsLoading(false), 500);
		return () => clearTimeout(timer);
	}, [pathname]);

	if (!isLoading) return null;

	return (
		<div className="fixed top-0 left-0 right-0 z-50">
			<div className="h-1 bg-primary animate-pulse" />
		</div>
	);
}
