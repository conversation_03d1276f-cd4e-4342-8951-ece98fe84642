"use client";

import { AnalyticsScript } from "@analytics";
import { config } from "@repo/config";
import { ApiClientProvider } from "@shared/components/ApiClientProvider";
import { ConsentBanner } from "@shared/components/ConsentBanner";
import { Toaster } from "@ui/components/toast";
import { Provider as <PERSON><PERSON>P<PERSON>ider } from "jotai";
import { ThemeProvider } from "next-themes";
import { SimpleProgressBar } from "./SimpleProgressBar";
import type { PropsWithChildren } from "react";

export function ClientProviders({ children }: PropsWithChildren) {
	return (
		<ApiClientProvider>
			<SimpleProgressBar />
			<ThemeProvider
				attribute="class"
				disableTransitionOnChange
				enableSystem
				defaultTheme={config.ui.defaultTheme}
				themes={config.ui.enabledThemes}
			>
				<ApiClientProvider>
					<JotaiProvider>
						{children}

						<Toaster position="top-right" />
						<ConsentBanner />
						<AnalyticsScript />
					</JotaiProvider>
				</ApiClientProvider>
			</ThemeProvider>
		</ApiClientProvider>
	);
}
