"use client";

import { useLocale } from "next-intl";

const LOCALE_CURRENCY_MAP = {
  pt: "BRL",
  en: "USD",
} as const;

const CURRENCY_LOCALE_MAP = {
  BRL: "pt-BR",
  USD: "en-US",
} as const;

export function useLocaleCurrency(): string {
  const locale = useLocale();
  return LOCALE_CURRENCY_MAP[locale as keyof typeof LOCALE_CURRENCY_MAP] || "BRL";
}

export function useLocaleInfo() {
  const locale = useLocale();
  const currency = useLocaleCurrency();
  
  return {
    locale,
    currency,
    isDefaultLocale: locale === "pt",
    isDefaultCurrency: currency === "BRL",
  };
}

export function getLocaleForCurrency(currency: string): string {
  return CURRENCY_LOCALE_MAP[currency as keyof typeof CURRENCY_LOCALE_MAP] || "pt-BR";
}