"use client";

import { useState, useCallback } from "react";
import { validateCurrencyInput, validateMonetaryValue, validateTransactionData, type CurrencyValidationResult } from "../lib/currency-validation";

export function useCurrencyValidation() {
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateCurrency = useCallback((currency: string, fieldName = "currency"): boolean => {
    const result = validateCurrencyInput(currency);
    
    setValidationErrors(prev => ({
      ...prev,
      [fieldName]: result.isValid ? "" : result.error || "",
    }));
    
    return result.isValid;
  }, []);

  const validateAmount = useCallback((amountInCents: number, currency: string, fieldName = "amount"): boolean => {
    const result = validateMonetaryValue(amountInCents, currency);
    
    setValidationErrors(prev => ({
      ...prev,
      [fieldName]: result.isValid ? "" : result.error || "",
    }));
    
    return result.isValid;
  }, []);

  const validateTransaction = useCallback((data: {
    amountInCents: number;
    currency: string;
    description?: string;
  }, fieldPrefix = ""): boolean => {
    const result = validateTransactionData(data);
    
    const errorKey = fieldPrefix ? `${fieldPrefix}_transaction` : "transaction";
    
    setValidationErrors(prev => ({
      ...prev,
      [errorKey]: result.isValid ? "" : result.error || "",
    }));
    
    return result.isValid;
  }, []);

  const clearValidationError = useCallback((fieldName: string) => {
    setValidationErrors(prev => ({
      ...prev,
      [fieldName]: "",
    }));
  }, []);

  const clearAllValidationErrors = useCallback(() => {
    setValidationErrors({});
  }, []);

  const hasValidationErrors = Object.values(validationErrors).some(error => error !== "");

  return {
    validationErrors,
    validateCurrency,
    validateAmount,
    validateTransaction,
    clearValidationError,
    clearAllValidationErrors,
    hasValidationErrors,
  };
}

export function useFormCurrencyValidation<T extends Record<string, any>>(initialData: T) {
  const [formData, setFormData] = useState<T>(initialData);
  const { validationErrors, validateCurrency, validateAmount, validateTransaction, clearValidationError, hasValidationErrors } = useCurrencyValidation();

  const updateField = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    clearValidationError(field as string);
  }, [clearValidationError]);

  const validateField = useCallback((field: keyof T): boolean => {
    const value = formData[field];
    
    if (field === "currency" && typeof value === "string") {
      return validateCurrency(value, field as string);
    }
    
    if (field === "amountInCents" && typeof value === "number" && formData.currency) {
      return validateAmount(value, formData.currency as string, field as string);
    }
    
    return true;
  }, [formData, validateCurrency, validateAmount]);

  const validateForm = useCallback((): boolean => {
    let isValid = true;
    
    Object.keys(formData).forEach(field => {
      if (!validateField(field as keyof T)) {
        isValid = false;
      }
    });
    
    return isValid;
  }, [formData, validateField]);

  return {
    formData,
    validationErrors,
    updateField,
    validateField,
    validateForm,
    hasValidationErrors,
  };
}