import { useState, useCallback, useMemo } from 'react';
import {
  convertCurrency,
  convertToBRL,
  convertToUSD,
  convertToUserCurrency,
  getExchangeRate,
  updateExchangeRate,
  getAvailableCurrencyPairs,
  formatConversionResult,
  type ConversionResult,
  type ExchangeRate
} from '../lib/currency-conversion';
import { useLocaleCurrency } from './use-locale-currency';

export interface CurrencyConversionState {
  isLoading: boolean;
  error: string | null;
  lastConversion: ConversionResult | null;
}

export function useCurrencyConversion() {
  const userCurrency = useLocaleCurrency();
  const [state, setState] = useState<CurrencyConversionState>({
    isLoading: false,
    error: null,
    lastConversion: null
  });

  const convert = useCallback(async (
    amountCents: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<ConversionResult | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = convertCurrency(amountCents, fromCurrency, toCurrency);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastConversion: result 
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Conversion failed';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, []);

  const convertToBrazilianReal = useCallback(async (
    amountCents: number,
    fromCurrency: string
  ): Promise<ConversionResult | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = convertToBRL(amountCents, fromCurrency);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastConversion: result 
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Conversion to BRL failed';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, []);

  const convertToUSDollar = useCallback(async (
    amountCents: number,
    fromCurrency: string
  ): Promise<ConversionResult | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = convertToUSD(amountCents, fromCurrency);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastConversion: result 
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Conversion to USD failed';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, []);

  const convertToUserPreferredCurrency = useCallback(async (
    amountCents: number,
    fromCurrency: string
  ): Promise<ConversionResult | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = convertToUserCurrency(amountCents, fromCurrency, userCurrency);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastConversion: result 
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Conversion to user currency failed';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return null;
    }
  }, [userCurrency]);

  const getRate = useCallback((from: string, to: string): number => {
    return getExchangeRate(from, to);
  }, []);

  const updateRate = useCallback((from: string, to: string, rate: number): void => {
    updateExchangeRate(from, to, rate);
  }, []);

  const formatResult = useCallback((result: ConversionResult): string => {
    return formatConversionResult(result);
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const availablePairs = useMemo(() => {
    return getAvailableCurrencyPairs();
  }, []);

  return {
    // State
    isLoading: state.isLoading,
    error: state.error,
    lastConversion: state.lastConversion,
    userCurrency,
    availablePairs,
    
    // Actions
    convert,
    convertToBrazilianReal,
    convertToUSDollar,
    convertToUserPreferredCurrency,
    getRate,
    updateRate,
    formatResult,
    clearError
  };
}

export function useExchangeRates() {
  const [rates, setRates] = useState<Record<string, ExchangeRate>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateRates = useCallback((newRates: Record<string, ExchangeRate>) => {
    setRates(newRates);
    
    Object.values(newRates).forEach(rate => {
      updateExchangeRate(rate.from, rate.to, rate.rate);
    });
  }, []);

  const getRateForPair = useCallback((from: string, to: string): ExchangeRate | null => {
    const key = `${from}_${to}`;
    return rates[key] || null;
  }, [rates]);

  const refreshRates = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const pairs = getAvailableCurrencyPairs();
      const newRates: Record<string, ExchangeRate> = {};
      
      pairs.forEach(({ from, to }) => {
        const rate = getExchangeRate(from, to);
        const key = `${from}_${to}`;
        newRates[key] = {
          from,
          to,
          rate,
          timestamp: Date.now()
        };
      });
      
      setRates(newRates);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh rates';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    rates,
    isLoading,
    error,
    updateRates,
    getRateForPair,
    refreshRates
  };
}