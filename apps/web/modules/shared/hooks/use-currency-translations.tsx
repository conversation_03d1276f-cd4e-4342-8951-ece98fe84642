"use client";

import { useTranslations } from 'next-intl';
import { useLocaleCurrency } from './use-locale-currency';

export function useCurrencyTranslations() {
  const t = useTranslations('currency');
  const userCurrency = useLocaleCurrency();

  const getCurrencyName = (currencyCode: string): string => {
    const code = currencyCode.toLowerCase();
    return t(`${code}.name`);
  };

  const getCurrencySymbol = (currencyCode: string): string => {
    const code = currencyCode.toLowerCase();
    return t(`${code}.symbol`);
  };

  const getCurrencyCode = (currencyCode: string): string => {
    const code = currencyCode.toLowerCase();
    return t(`${code}.code`);
  };

  const getValidationMessage = (key: string, values?: Record<string, any>): string => {
    return t(`validation.${key}`, values);
  };

  const getConversionMessage = (key: string, values?: Record<string, any>): string => {
    return t(`conversion.${key}`, values);
  };

  const getCompactFormatSuffix = (key: 'thousand' | 'million' | 'billion'): string => {
    return t(`format.compact.${key}`);
  };

  const getUserCurrencyInfo = () => {
    return {
      code: userCurrency,
      name: getCurrencyName(userCurrency),
      symbol: getCurrencySymbol(userCurrency)
    };
  };

  return {
    getCurrencyName,
    getCurrencySymbol,
    getCurrencyCode,
    getValidationMessage,
    getConversionMessage,
    getCompactFormatSuffix,
    getUserCurrencyInfo,
    userCurrency
  };
}

export function useCurrencyValidationMessages() {
  const { getValidationMessage } = useCurrencyTranslations();

  return {
    unsupported: (currency: string) => getValidationMessage('unsupported', { currency }),
    invalid: () => getValidationMessage('invalid'),
    required: () => getValidationMessage('required')
  };
}

export function useCurrencyConversionMessages() {
  const { getConversionMessage } = useCurrencyTranslations();

  return {
    failed: () => getConversionMessage('failed'),
    unavailable: (from: string, to: string) => getConversionMessage('unavailable', { from, to }),
    loading: () => getConversionMessage('loading'),
    updated: () => getConversionMessage('updated')
  };
}