/**
 * Utility functions to handle build-time scenarios and avoid static generation issues
 */

/**
 * Checks if the current environment is during build time
 */
export function isBuildTime(): boolean {
	return process.env.NODE_ENV === "production" && !process.env.VERCEL_ENV;
}

/**
 * Safe wrapper for functions that should not run during build time
 */
export function safeBuildTime<T>(
	buildTimeValue: T,
	runtimeFunction: () => Promise<T> | T,
): Promise<T> | T {
	if (isBuildTime()) {
		return buildTimeValue;
	}

	try {
		return runtimeFunction();
	} catch (error) {
		console.warn("Runtime function failed, falling back to build-time value:", error);
		return buildTimeValue;
	}
}

/**
 * Safe wrapper for async functions that should not run during build time
 */
export async function safeBuildTimeAsync<T>(
	buildTimeValue: T,
	runtimeFunction: () => Promise<T>,
): Promise<T> {
	if (isBuildTime()) {
		return buildTimeValue;
	}

	try {
		return await runtimeFunction();
	} catch (error) {
		console.warn("Runtime function failed, falling back to build-time value:", error);
		return buildTimeValue;
	}
}
