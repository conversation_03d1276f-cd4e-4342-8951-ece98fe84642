import { validateCurrency, SUPPORTED_CURRENCIES } from "@repo/utils";

export interface CurrencyValidationResult {
  isValid: boolean;
  currency?: string;
  error?: string;
}

export function validateCurrencyInput(currency: string): CurrencyValidationResult {
  if (!currency) {
    return {
      isValid: false,
      error: "Moeda é obrigatória",
    };
  }

  const upperCurrency = currency.toUpperCase();
  
  if (!validateCurrency(upperCurrency)) {
    return {
      isValid: false,
      error: `Moeda '${currency}' não é suportada. Moedas suportadas: ${Object.keys(SUPPORTED_CURRENCIES).join(", ")}\`,
    };
  }

  return {
    isValid: true,
    currency: upperCurrency,
  };
}

export function validateMonetaryValue(amountInCents: number, currency: string): CurrencyValidationResult {
  const currencyValidation = validateCurrencyInput(currency);
  
  if (!currencyValidation.isValid) {
    return currencyValidation;
  }

  if (typeof amountInCents !== "number" || isNaN(amountInCents)) {
    return {
      isValid: false,
      error: "Valor monetário deve ser um número válido",
    };
  }

  if (amountInCents < 0) {
    return {
      isValid: false,
      error: "Valor monetário não pode ser negativo",
    };
  }

  if (!Number.isInteger(amountInCents)) {
    return {
      isValid: false,
      error: "Valor monetário deve ser em centavos (número inteiro)",
    };
  }

  return {
    isValid: true,
    currency: currencyValidation.currency,
  };
}

export function validateTransactionData(data: {
  amountInCents: number;
  currency: string;
  description?: string;
}): CurrencyValidationResult {
  const monetaryValidation = validateMonetaryValue(data.amountInCents, data.currency);
  
  if (!monetaryValidation.isValid) {
    return monetaryValidation;
  }

  if (data.description && data.description.length > 255) {
    return {
      isValid: false,
      error: "Descrição da transação não pode exceder 255 caracteres",
    };
  }

  return monetaryValidation;
}

export function getSupportedCurrencies(): string[] {
  return Object.keys(SUPPORTED_CURRENCIES);
}

export function isCurrencySupported(currency: string): boolean {
  return validateCurrency(currency.toUpperCase());
}