import { SUPPORTED_CURRENCIES } from '@repo/utils';

export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  timestamp: number;
}

export interface ConversionResult {
  originalAmountCents: number;
  originalCurrency: string;
  convertedAmountCents: number;
  convertedCurrency: string;
  exchangeRate: number;
  timestamp: number;
}

const EXCHANGE_RATES_CACHE = new Map<string, ExchangeRate>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const DEFAULT_RATES: Record<string, Record<string, number>> = {
  BRL: {
    USD: 0.20, // 1 BRL = 0.20 USD (aproximado)
    BRL: 1
  },
  USD: {
    BRL: 5.00, // 1 USD = 5.00 BRL (aproximado)
    USD: 1
  }
};

export function getCacheKey(from: string, to: string): string {
  return `${from}_${to}`;
}

export function isRateCacheValid(rate: ExchangeRate): boolean {
  return Date.now() - rate.timestamp < CACHE_DURATION;
}

export function getExchangeRate(from: string, to: string): number {
  if (from === to) return 1;
  
  const cacheKey = getCacheKey(from, to);
  const cachedRate = EXCHANGE_RATES_CACHE.get(cacheKey);
  
  if (cachedRate && isRateCacheValid(cachedRate)) {
    return cachedRate.rate;
  }
  
  const rate = DEFAULT_RATES[from]?.[to] || 1;
  
  const exchangeRate: ExchangeRate = {
    from,
    to,
    rate,
    timestamp: Date.now()
  };
  
  EXCHANGE_RATES_CACHE.set(cacheKey, exchangeRate);
  return rate;
}

export function convertCurrency(
  amountCents: number,
  fromCurrency: string,
  toCurrency: string
): ConversionResult {
  if (!(fromCurrency in SUPPORTED_CURRENCIES)) {
    throw new Error(`Unsupported source currency: ${fromCurrency}`);
  }
  
  if (!(toCurrency in SUPPORTED_CURRENCIES)) {
    throw new Error(`Unsupported target currency: ${toCurrency}`);
  }
  
  const exchangeRate = getExchangeRate(fromCurrency, toCurrency);
  const convertedAmountCents = Math.round(amountCents * exchangeRate);
  
  return {
    originalAmountCents: amountCents,
    originalCurrency: fromCurrency,
    convertedAmountCents,
    convertedCurrency: toCurrency,
    exchangeRate,
    timestamp: Date.now()
  };
}

export function convertToBRL(amountCents: number, fromCurrency: string): ConversionResult {
  return convertCurrency(amountCents, fromCurrency, 'BRL');
}

export function convertToUSD(amountCents: number, fromCurrency: string): ConversionResult {
  return convertCurrency(amountCents, fromCurrency, 'USD');
}

export function convertToUserCurrency(
  amountCents: number,
  fromCurrency: string,
  userCurrency: string
): ConversionResult {
  return convertCurrency(amountCents, fromCurrency, userCurrency);
}

export function updateExchangeRate(from: string, to: string, rate: number): void {
  const cacheKey = getCacheKey(from, to);
  const exchangeRate: ExchangeRate = {
    from,
    to,
    rate,
    timestamp: Date.now()
  };
  
  EXCHANGE_RATES_CACHE.set(cacheKey, exchangeRate);
}

export function clearExchangeRateCache(): void {
  EXCHANGE_RATES_CACHE.clear();
}

export function getAvailableCurrencyPairs(): Array<{ from: string; to: string }> {
  const pairs: Array<{ from: string; to: string }> = [];
  const currencies = Object.keys(SUPPORTED_CURRENCIES);
  
  for (const from of currencies) {
    for (const to of currencies) {
      if (from !== to) {
        pairs.push({ from, to });
      }
    }
  }
  
  return pairs;
}

export function formatConversionResult(result: ConversionResult): string {
  const { originalAmountCents, originalCurrency, convertedAmountCents, convertedCurrency, exchangeRate } = result;
  
  const originalAmount = (originalAmountCents / 100).toFixed(2);
  const convertedAmount = (convertedAmountCents / 100).toFixed(2);
  
  return `${originalAmount} ${originalCurrency} = ${convertedAmount} ${convertedCurrency} (Rate: ${exchangeRate})`;
}