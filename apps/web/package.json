{"dependencies": {"@aws-sdk/client-s3": "3.437.0", "@fumadocs/content-collections": "^1.2.0", "@next/third-parties": "^15.3.5", "@prisma/nextjs-monorepo-workaround-plugin": "^6.11.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "ai": "^4.3.16", "better-auth": "1.2.12", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "1.6.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "framer-motion": "^12.23.6", "fumadocs-core": "^15.6.1", "fumadocs-ui": "^15.6.1", "geist": "^1.4.2", "hono": "^4.8.4", "input-otp": "^1.4.2", "jotai": "2.12.5", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "next": "15.3.5", "next-intl": "4.3.4", "next-themes": "^0.4.6", "nprogress": "^0.2.0", "nuqs": "^2.4.3", "oslo": "^1.2.1", "prettier": "3.6.2", "react": "19.1.0", "react-cropper": "^2.3.3", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.59.0", "react-qr-code": "^2.0.17", "server-only": "^0.0.1", "sharp": "^0.34.2", "slugify": "^1.6.6", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "ufo": "^1.6.1", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.1.0", "@content-collections/core": "^0.9.0", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@mdx-js/mdx": "^3.1.0", "@playwright/test": "^1.53.2", "@repo/auth": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@shikijs/rehype": "^3.6.0", "@tailwindcss/postcss": "^4.1.8", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "24.0.10", "@types/nprogress": "^0.2.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.21", "dotenv": "^17.1.0", "dotenv-cli": "^8.0.0", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.5.6", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.12", "tailwindcss": "4.1.11"}, "name": "@repo/web", "private": true, "scripts": {"build": "next build", "build:local": "./build-local.sh", "dev": "next dev --turbo", "e2e": "pnpm exec playwright test --ui", "e2e:ci": "pnpm exec playwright install && pnpm exec playwright test", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.0.0"}