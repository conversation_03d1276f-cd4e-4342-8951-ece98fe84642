import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database/prisma/client";
import { generateCssVariables, getBrandingConfig } from "@/modules/branding/lib/branding";

export async function applyBrandingMiddleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const hostname = request.headers.get('host') || '';

  // Check if this is a custom domain
  const organization = await db.organization.findFirst({
    where: {
      OR: [
        { customDomain: hostname },
        { domain: hostname }
      ]
    },
    select: {
      id: true,
      slug: true,
      branding: true,
      enableCustomBranding: true,
      enableCustomDomain: true
    }
  });

  if (!organization) {
    return NextResponse.next();
  }

  // If custom domain, rewrite to organization slug
  if (organization.enableCustomDomain && organization?.customDomain === hostname) {
    url.pathname = `/${organization.slug}${url.pathname}`;
    return NextResponse.rewrite(url);
  }

  return NextResponse.next();
}

export async function injectBrandingStyles(organizationId: string): Promise<string> {
  try {
    const brandingConfig = await getBrandingConfig(organizationId);

    if (!brandingConfig) {
      return '';
    }

    const cssVariables = generateCssVariables(brandingConfig);

    let styles = cssVariables;

    // Add custom CSS if provided
    if (brandingConfig.customCss) {
      styles += `\n${brandingConfig.customCss}`;
    }

    // Add font family if specified
    if (brandingConfig.fontFamily) {
      styles += `\nbody { font-family: ${brandingConfig.fontFamily}, sans-serif; }`;
    }

    // Add dark mode styles if enabled
    if (brandingConfig.darkMode) {
      styles += `\n@media (prefers-color-scheme: dark) { :root { color-scheme: dark; } }`;
    }

    return `<style id="branding-styles">${styles}</style>`;
  } catch (error) {
    console.error('Error injecting branding styles:', error);
    return '';
  }
}

export async function getBrandingMetadata(organizationId: string) {
  try {
    const brandingConfig = await getBrandingConfig(organizationId);
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: {
        name: true,
        customDomain: true
      }
    });

    if (!brandingConfig || !organization) {
      return null;
    }

    return {
      title: brandingConfig.companyName || organization.name,
      description: brandingConfig.description || `Plataforma ${organization.name}`,
      favicon: brandingConfig.faviconUrl || '/favicon.ico',
      logo: brandingConfig.logoUrl || null,
      tagline: brandingConfig.tagline || null,
      hideSystemBranding: brandingConfig.hideSystemBranding || false,
    };
  } catch (error) {
    console.error('Error getting branding metadata:', error);
    return null;
  }
}

export function createBrandingContext(organizationId: string) {
  return {
    organizationId,
    getBrandingConfig: () => getBrandingConfig(organizationId),
    getBrandingMetadata: () => getBrandingMetadata(organizationId),
    injectStyles: () => injectBrandingStyles(organizationId),
  };
}
