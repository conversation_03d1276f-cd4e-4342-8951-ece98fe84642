# Dockerfile otimizado para build
FROM node:20-alpine AS base

# Instalar pnpm
RUN npm install -g pnpm@9.3.0

# Configurar diretório de trabalho
WORKDIR /app

# Copiar arquivos de configuração
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/web/package.json ./apps/web/
COPY packages/*/package.json ./packages/*/
COPY tooling/*/package.json ./tooling/*/

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Copiar código fonte
COPY . .

# Configurar variáveis de ambiente para otimizar memória
ENV NODE_OPTIONS="--max-old-space-size=8192"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_IGNORE_TYPECHECK_DURING_BUILD=1

# Gerar tipos do Prisma
RUN cd packages/database && pnpm prisma generate

# Build da aplicação
RUN cd apps/web && pnpm build

# Imagem final
FROM node:20-alpine AS runner

WORKDIR /app

# Copiar apenas os arquivos necessários
COPY --from=base /app/apps/web/.next ./apps/web/.next
COPY --from=base /app/apps/web/public ./apps/web/public
COPY --from=base /app/apps/web/package.json ./apps/web/
COPY --from=base /app/package.json ./
COPY --from=base /app/pnpm-lock.yaml ./
COPY --from=base /app/pnpm-workspace.yaml ./

# Instalar apenas dependências de produção
RUN pnpm install --prod --frozen-lockfile

# Expor porta
EXPOSE 3000

# Comando de inicialização
CMD ["pnpm", "start"]
