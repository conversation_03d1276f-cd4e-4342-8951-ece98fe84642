#!/bin/bash

# Script para testar Dockerfile localmente
# Uso: ./test-docker-local.sh [DOCKERFILE_NAME]

set -e

# Configurações
DOCKERFILE_NAME=${1:-"Dockerfile"}
DOCKERFILE_PATH="apps/web/${DOCKERFILE_NAME}"
IMAGE_NAME="super-gateway-test"
CONTAINER_NAME="super-gateway-test-container"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Teste Local do Dockerfile${NC}"
echo -e "${BLUE}Dockerfile: ${DOCKERFILE_PATH}${NC}"
echo ""

# Verificar se Docker está rodando
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker não está rodando${NC}"
    echo "Inicie o Docker Desktop e tente novamente"
    exit 1
fi

# Verificar se Dockerfile existe
if [ ! -f "$DOCKERFILE_PATH" ]; then
    echo -e "${RED}❌ Dockerfile não encontrado: ${DOCKERFILE_PATH}${NC}"
    echo "Arquivos disponíveis em apps/web/:"
    ls -la apps/web/Dockerfile*
    exit 1
fi

# Limpar containers e imagens anteriores
echo -e "${BLUE}🧹 Limpando containers e imagens anteriores...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build da imagem
echo -e "${BLUE}🏗️  Fazendo build da imagem...${NC}"
echo "Comando: docker build -f ${DOCKERFILE_PATH} . --no-cache -t ${IMAGE_NAME}"
docker build -f $DOCKERFILE_PATH . --no-cache -t $IMAGE_NAME

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build concluído com sucesso!${NC}"
else
    echo -e "${RED}❌ Build falhou${NC}"
    exit 1
fi

# Verificar tamanho da imagem
echo -e "${BLUE}📏 Tamanho da imagem:${NC}"
docker images $IMAGE_NAME --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Executar container
echo -e "${BLUE}🚀 Executando container...${NC}"
echo "Comando: docker run -d --name ${CONTAINER_NAME} -p 3000:3000 ${IMAGE_NAME}"
docker run -d --name $CONTAINER_NAME -p 3000:3000 $IMAGE_NAME

# Aguardar container inicializar
echo -e "${BLUE}⏳ Aguardando container inicializar...${NC}"
sleep 10

# Verificar status do container
echo -e "${BLUE}📊 Status do container:${NC}"
docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Testar health check
echo -e "${BLUE}🏥 Testando health check...${NC}"
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo -e "${GREEN}✅ Health check funcionando${NC}"
    curl -s http://localhost:3000/api/health | jq . 2>/dev/null || curl -s http://localhost:3000/api/health
else
    echo -e "${YELLOW}⚠️  Health check não respondeu, verificando logs...${NC}"
    docker logs $CONTAINER_NAME --tail 20
fi

# Verificar logs
echo -e "${BLUE}📋 Últimos logs do container:${NC}"
docker logs $CONTAINER_NAME --tail 10

echo ""
echo -e "${GREEN}✅ Teste concluído!${NC}"
echo -e "${BLUE}🌐 Aplicação rodando em: http://localhost:3000${NC}"
echo -e "${BLUE}📋 Logs: docker logs -f ${CONTAINER_NAME}${NC}"
echo -e "${BLUE}🛑 Parar: docker stop ${CONTAINER_NAME}${NC}"
echo -e "${BLUE}🗑️  Limpar: docker rm ${CONTAINER_NAME} && docker rmi ${IMAGE_NAME}${NC}"
