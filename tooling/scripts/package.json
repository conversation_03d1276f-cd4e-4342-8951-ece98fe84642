{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@biomejs/biome": "2.1.0", "@repo/tsconfig": "workspace:*", "@types/node": "^24.0.10", "nanoid": "^5.1.5", "tsx": "^4.19.4"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "seed:db": "dotenv -c -e ../../.env -- tsx ./src/seed.ts", "reset:db": "dotenv -c -e ../../.env -- tsx ./src/reset-db.ts", "test:seed": "dotenv -c -e ../../.env -- tsx ./src/test-seed.ts", "fix:profiles": "dotenv -c -e ../../.env -- tsx ./src/fix-profiles.ts", "type-check": "tsc --noEmit"}, "version": "0.0.0"}