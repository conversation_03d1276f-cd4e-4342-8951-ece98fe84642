import { auth } from "@repo/auth";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";
import { db } from "@repo/database/prisma/client";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";

// Função para gerar slug
function generateSlug(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "")
		.replace(/[^a-z0-9\s-]/g, "")
		.trim()
		.replace(/\s+/g, "-")
		.replace(/-+/g, "-");
}

// Função para criar organização
async function createOrganization(name: string, userId: string) {
	const slug = generateSlug(name);

	// Verifica se já existe
	const existingOrg = await db.organization.findUnique({
		where: { slug }
	});

	if (existingOrg) {
		logger.warn(`Organização com slug "${slug}" já existe!`);
		return existingOrg;
	}

	const organization = await db.organization.create({
		data: {
			name,
			slug,
			currency: "BRL",
			language: "pt",
			timezone: "America/Sao_Paulo",
			subscriptionPlan: "basic",
			subscriptionStatus: "active",
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	});

	// Adicionar usuário como owner da organização
	await db.member.create({
		data: {
			organizationId: organization.id,
			userId: userId,
			role: "owner",
			createdAt: new Date(),
		},
	});

	return organization;
}

async function main() {
	logger.info("Vamos criar um novo usuário para sua aplicação!");

	const email = await logger.prompt("Digite o email:", {
		required: true,
		placeholder: "<EMAIL>",
		type: "text",
	});

	const name = await logger.prompt("Digite o nome:", {
		required: true,
		placeholder: "João Silva",
		type: "text",
	});

	// Opções de roles baseadas no schema
	const roleOptions = [
		{ value: "USER", label: "USER - Usuário comum" },
		{ value: "TEACHER", label: "TEACHER - Professor/Instrutor" },
		{ value: "AFFILIATE", label: "AFFILIATE - Afiliado" },
		{ value: "ADMIN", label: "ADMIN - Administrador" },
		{ value: "SUPER_ADMIN", label: "SUPER_ADMIN - Super Administrador" },
	];

	logger.info("Selecione o tipo de usuário:");
	roleOptions.forEach((option, index) => {
		logger.info(`${index + 1}. ${option.label}`);
	});

	const roleIndex = await logger.prompt("Digite o número da opção:", {
		required: true,
		type: "text",
	});

	const selectedRole = roleOptions[parseInt(roleIndex) - 1];
	if (!selectedRole) {
		logger.error("Opção inválida!");
		return;
	}

	// Pergunta se deve criar uma organização
	const shouldCreateOrg = await logger.prompt("Deseja criar uma organização para este usuário?", {
		required: true,
		type: "confirm",
		default: false,
	});

	let organizationName = "";
	if (shouldCreateOrg) {
		organizationName = await logger.prompt("Digite o nome da organização:", {
			required: true,
			placeholder: "Minha Academia",
			type: "text",
		});
	}

	// Gerar senha aleatória
	const userPassword = nanoid(16);

	const authContext = await auth.$context;
	const hashedPassword = await authContext.password.hash(userPassword);

	// Verificar se usuário já existe
	const user = await getUserByEmail(email);

	if (user) {
		logger.error("Usuário com este email já existe!");
		return;
	}

	// Criar usuário
	const newUser = await createUser({
		email,
		name,
		role: selectedRole.value.toLowerCase() as "admin" | "user",
		emailVerified: true,
		onboardingComplete: true,
	});

	if (!newUser) {
		logger.error("Falha ao criar usuário!");
		return;
	}

	// Atualizar role no banco diretamente (pois a função createUser não suporta todos os roles)
	await db.user.update({
		where: { id: newUser.id },
		data: { role: selectedRole.value as any }
	});

	// Criar conta de acesso
	await createUserAccount({
		userId: newUser.id,
		providerId: "credential",
		accountId: newUser.id,
		hashedPassword,
	});

	// Criar perfis específicos
	if (selectedRole.value === "TEACHER") {
		await db.teacherProfile.create({
			data: {
				userId: newUser.id,
				bio: "Professor criado via CLI",
				expertise: ["Ensino"],
				isVerified: false,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		logger.info("Perfil de professor criado!");
	} else if (selectedRole.value === "AFFILIATE") {
		await db.affiliateProfile.create({
			data: {
				userId: newUser.id,
				commissionCents: 1000, // 10% padrão
				totalEarningsCents: 0,
				currency: "BRL",
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});
		logger.info("Perfil de afiliado criado!");
	}

	// Criar organização se solicitado
	let organization = null;
	if (shouldCreateOrg && organizationName) {
		organization = await createOrganization(organizationName, newUser.id);
		logger.success(`Organização "${organization.name}" criada com sucesso!`);
		logger.info(`Slug da organização: ${organization.slug}`);
	}

	logger.success("Usuário criado com sucesso!");
	logger.info(`
🎉 Usuário criado:
📧 Email: ${email}
👤 Nome: ${name}
🔑 Senha: ${userPassword}
👥 Role: ${selectedRole.value}
${organization ? `🏢 Organização: ${organization.name} (${organization.slug})` : ""}
	`);
}

main();
