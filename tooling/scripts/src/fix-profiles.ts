import { db } from "@repo/database/prisma/client";
import { logger } from "@repo/logs";

async function fixProfiles() {
	logger.info("🔧 Verificando e corrigindo perfis...");

	try {
		// Verificar usuários que deveriam ter perfis específicos
		const users = await db.user.findMany({
			where: {
				OR: [
					{ role: "TEACHER" },
					{ role: "AFFILIATE" }
				]
			},
			include: {
				teacherProfile: true,
				affiliateProfile: true
			}
		});

		logger.info(`📊 Usuários encontrados: ${users.length}`);

		for (const user of users) {
			logger.info(`\n👤 Verificando usuário: ${user.name} (${user.email}) - Role: ${user.role}`);

			if (user.role === "TEACHER" && !user.teacherProfile) {
				logger.info("  ❌ Perfil de professor não encontrado, criando...");

				await db.teacherProfile.create({
					data: {
						userId: user.id,
						bio: "Professor experiente em tecnologia e educação digital.",
						expertise: ["JavaScript", "React", "Node.js", "Python"],
						experience: "5+ anos de experiência em desenvolvimento web e ensino.",
						education: "Graduação em Ciência da Computação",
						website: "https://joaoprofessor.com",
						isVerified: true,
						createdAt: new Date(),
						updatedAt: new Date(),
					},
				});

				logger.success("  ✅ Perfil de professor criado!");
			}

			if (user.role === "AFFILIATE" && !user.affiliateProfile) {
				logger.info("  ❌ Perfil de afiliado não encontrado, criando...");

				await db.affiliateProfile.create({
					data: {
						userId: user.id,
						commissionCents: 1500, // 15%
						totalEarningsCents: 0,
						currency: "BRL",
						isActive: true,
						createdAt: new Date(),
						updatedAt: new Date(),
					},
				});

				logger.success("  ✅ Perfil de afiliado criado!");
			}

			if (user.teacherProfile) {
				logger.info("  ✅ Perfil de professor já existe");
			}

			if (user.affiliateProfile) {
				logger.info("  ✅ Perfil de afiliado já existe");
			}
		}

		// Verificar se há perfis órfãos
		const orphanedTeacherProfiles = await db.teacherProfile.findMany({
			include: {
				user: true
			}
		});

		logger.info(`\n👨‍🏫 Perfis de professor: ${orphanedTeacherProfiles.length}`);
		orphanedTeacherProfiles.forEach(profile => {
			logger.info(`  - ${profile.user.name} (${profile.user.email})`);
		});

		const orphanedAffiliateProfiles = await db.affiliateProfile.findMany({
			include: {
				user: true
			}
		});

		logger.info(`\n🤝 Perfis de afiliado: ${orphanedAffiliateProfiles.length}`);
		orphanedAffiliateProfiles.forEach(profile => {
			logger.info(`  - ${profile.user.name} (${profile.user.email})`);
		});

		logger.success("✅ Verificação e correção de perfis concluída!");

	} catch (error) {
		logger.error("❌ Erro durante a correção:", error);
		process.exit(1);
	}
}

fixProfiles();
