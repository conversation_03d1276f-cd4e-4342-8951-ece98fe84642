import { auth } from "@repo/auth";
import { db } from "@repo/database/prisma/client";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";
import { createId as cuid } from "@paralleldrive/cuid2";

// Função para gerar slug
function generateSlug(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "")
		.replace(/[^a-z0-9\s-]/g, "")
		.trim()
		.replace(/\s+/g, "-")
		.replace(/-+/g, "-");
}

// Função para criar usuário admin
async function createAdminUser() {
	logger.info("Criando usuário administrador...");

	const adminEmail = "<EMAIL>";
	const adminPassword = "admin123";

	// Verifica se já existe
	const existingAdmin = await db.user.findUnique({
		where: { email: adminEmail }
	});

	if (existingAdmin) {
		logger.info("Usuário admin já existe, pulando criação.");
		return existingAdmin;
	}

	const authContext = await auth.$context;
	const hashedPassword = await authContext.password.hash(adminPassword);

	const adminUser = await db.user.create({
		data: {
			email: adminEmail,
			name: "Super Admin",
			role: "SUPER_ADMIN",
			emailVerified: true,
			onboardingComplete: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	});

	// Criar conta de acesso
	await db.account.create({
		data: {
			userId: adminUser.id,
			providerId: "credential",
			accountId: adminUser.id,
			password: hashedPassword,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	});

	logger.success(`Usuário admin criado! Email: ${adminEmail}, Senha: ${adminPassword}`);
	return adminUser;
}

// Função para criar organização
async function createOrganization(name: string, adminUserId: string) {
	logger.info(`Criando organização: ${name}...`);

	const slug = generateSlug(name);

	// Verifica se já existe
	const existingOrg = await db.organization.findUnique({
		where: { slug }
	});

	if (existingOrg) {
		logger.info(`Organização ${name} já existe, pulando criação.`);
		return existingOrg;
	}

	const organization = await db.organization.create({
		data: {
			name,
			slug,
			currency: "BRL",
			language: "pt",
			timezone: "America/Sao_Paulo",
			subscriptionPlan: "premium",
			subscriptionStatus: "active",
			enableAffiliatePogram: true,
			enableDigitalProducts: true,
			enableCertificates: true,
			enableCustomBranding: true,
			enableCustomDomain: false,
			enableAdvancedPayments: true,
			maxProducts: 100,
			maxUsers: 1000,
			maxStorageGB: 50,
			maxTransactions: 10000,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	});

	// Adicionar admin como membro owner
	await db.member.create({
		data: {
			organizationId: organization.id,
			userId: adminUserId,
			role: "owner",
			createdAt: new Date(),
		},
	});

	logger.success(`Organização ${name} criada com sucesso!`);
	return organization;
}

// Função para criar usuários de exemplo
async function createSampleUsers(organizationId: string) {
	logger.info("Criando usuários de exemplo...");

	const users = [
		{
			email: "<EMAIL>",
			name: "João Professor",
			role: "TEACHER",
			password: "teacher123"
		},
		{
			email: "<EMAIL>",
			name: "Maria Afiliada",
			role: "AFFILIATE",
			password: "affiliate123"
		},
		{
			email: "<EMAIL>",
			name: "Carlos Cliente",
			role: "USER",
			password: "user123"
		}
	];

	const authContext = await auth.$context;
	const createdUsers = [];

	for (const userData of users) {
		// Verifica se já existe
		const existingUser = await db.user.findUnique({
			where: { email: userData.email }
		});

		if (existingUser) {
			logger.info(`Usuário ${userData.email} já existe, pulando criação.`);
			createdUsers.push(existingUser);
			continue;
		}

		const hashedPassword = await authContext.password.hash(userData.password);

		const user = await db.user.create({
			data: {
				email: userData.email,
				name: userData.name,
				role: userData.role as any,
				emailVerified: true,
				onboardingComplete: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		// Criar conta de acesso
		await db.account.create({
			data: {
				userId: user.id,
				providerId: "credential",
				accountId: user.id,
				password: hashedPassword,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		// Adicionar como membro da organização
		await db.member.create({
			data: {
				organizationId: organizationId,
				userId: user.id,
				role: userData.role === "TEACHER" ? "admin" : "member",
				createdAt: new Date(),
			},
		});

		// Criar perfis específicos
		if (userData.role === "TEACHER") {
			await db.teacherProfile.create({
				data: {
					userId: user.id,
					bio: "Professor experiente em tecnologia e educação digital.",
					expertise: ["JavaScript", "React", "Node.js", "Python"],
					experience: "5+ anos de experiência em desenvolvimento web e ensino.",
					education: "Graduação em Ciência da Computação",
					website: "https://joaoprofessor.com",
					isVerified: true,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});
		} else if (userData.role === "AFFILIATE") {
			await db.affiliateProfile.create({
				data: {
					userId: user.id,
					commissionCents: 1500, // 15%
					totalEarningsCents: 0,
					currency: "BRL",
					isActive: true,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});
		}

		createdUsers.push(user);
		logger.success(`Usuário ${userData.email} criado! Senha: ${userData.password}`);
	}

	return createdUsers;
}

// Função para criar categorias
async function createCategories(organizationId: string) {
	logger.info("Criando categorias...");

	const categories = [
		{
			name: "Programação",
			description: "Cursos de desenvolvimento de software",
			icon: "💻",
			color: "#3B82F6"
		},
		{
			name: "Design",
			description: "Cursos de design gráfico e UX/UI",
			icon: "🎨",
			color: "#EF4444"
		},
		{
			name: "Marketing Digital",
			description: "Estratégias de marketing online",
			icon: "📱",
			color: "#10B981"
		},
		{
			name: "E-books",
			description: "Livros digitais educativos",
			icon: "📚",
			color: "#F59E0B"
		}
	];

	const createdCategories = [];

	for (const categoryData of categories) {
		const slug = generateSlug(categoryData.name);

		// Verifica se já existe
		const existingCategory = await db.category.findUnique({
			where: {
				organizationId_slug: {
					organizationId,
					slug
				}
			}
		});

		if (existingCategory) {
			logger.info(`Categoria ${categoryData.name} já existe, pulando criação.`);
			createdCategories.push(existingCategory);
			continue;
		}

		const category = await db.category.create({
			data: {
				organizationId,
				name: categoryData.name,
				slug,
				description: categoryData.description,
				icon: categoryData.icon,
				color: categoryData.color,
				sortOrder: createdCategories.length,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		createdCategories.push(category);
		logger.success(`Categoria ${categoryData.name} criada!`);
	}

	return createdCategories;
}

// Função para criar produtos de exemplo
async function createSampleProducts(organizationId: string, teacherId: string, categories: any[]) {
	logger.info("Criando produtos de exemplo...");

	const products = [
		{
			name: "React do Zero ao Avançado",
			shortDescription: "Aprenda React.js do básico ao avançado",
			description: "Curso completo de React.js com projetos práticos e tudo que você precisa para se tornar um desenvolvedor React profissional.",
			priceCents: 29900, // R$ 299.00
			comparePriceCents: 49900, // R$ 499.00
			type: "COURSE",
			level: "BEGINNER",
			duration: 2400, // 40 horas
			features: [
				"40 horas de conteúdo",
				"Projetos práticos",
				"Certificado de conclusão",
				"Suporte vitalício"
			],
			requirements: [
				"Conhecimento básico de HTML/CSS",
				"Conhecimento básico de JavaScript"
			],
			categoryName: "Programação"
		},
		{
			name: "Design System Completo",
			shortDescription: "Crie design systems profissionais",
			description: "Aprenda a criar design systems escaláveis e consistentes para produtos digitais.",
			priceCents: 19900, // R$ 199.00
			comparePriceCents: 39900, // R$ 399.00
			type: "COURSE",
			level: "INTERMEDIATE",
			duration: 1800, // 30 horas
			features: [
				"30 horas de conteúdo",
				"Ferramentas profissionais",
				"Templates inclusos",
				"Comunidade exclusiva"
			],
			requirements: [
				"Conhecimento básico de design",
				"Figma instalado"
			],
			categoryName: "Design"
		},
		{
			name: "Marketing Digital 2024",
			shortDescription: "Estratégias atualizadas de marketing",
			description: "E-book com as principais estratégias de marketing digital para 2024.",
			priceCents: 4900, // R$ 49.00
			comparePriceCents: 9900, // R$ 99.00
			type: "EBOOK",
			features: [
				"150 páginas",
				"Casos práticos",
				"Templates de campanhas",
				"Atualizações gratuitas"
			],
			categoryName: "E-books"
		}
	];

	const createdProducts = [];

	for (const productData of products) {
		const slug = generateSlug(productData.name);
		const category = categories.find(cat => cat.name === productData.categoryName);

		// Verifica se já existe
		const existingProduct = await db.product.findUnique({
			where: {
				organizationId_slug: {
					organizationId,
					slug
				}
			}
		});

		if (existingProduct) {
			logger.info(`Produto ${productData.name} já existe, pulando criação.`);
			createdProducts.push(existingProduct);
			continue;
		}

		const product = await db.product.create({
			data: {
				organizationId,
				creatorId: teacherId,
				name: productData.name,
				slug,
				description: productData.description,
				shortDescription: productData.shortDescription,
				priceCents: productData.priceCents,
				comparePriceCents: productData.comparePriceCents,
				currency: "BRL",
				type: productData.type as any,
				status: "PUBLISHED",
				visibility: "PUBLIC",
				categoryId: category?.id,
				features: productData.features,
				requirements: productData.requirements || [],
				duration: productData.duration || null,
				level: productData.level || "BEGINNER",
				language: "pt-BR",
				certificate: true,
				downloadable: productData.type === "EBOOK",
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		// Criar dados específicos do tipo de produto
		if (productData.type === "COURSE") {
			await db.course.create({
				data: {
					productId: product.id,
					totalLessons: 40,
					totalDuration: productData.duration || 2400,
					level: productData.level || "BEGINNER",
					certificate: true,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});
		} else if (productData.type === "EBOOK") {
			await db.ebook.create({
				data: {
					productId: product.id,
					fileUrl: "/files/marketing-digital-2024.pdf",
					fileSize: 1024000, // 1MB
					pages: 150,
					format: "PDF",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			});
		}

		createdProducts.push(product);
		logger.success(`Produto ${productData.name} criado!`);
	}

	return createdProducts;
}

// Função para criar cupons
async function createCoupons(organizationId: string, creatorId: string) {
	logger.info("Criando cupons de exemplo...");

	const coupons = [
		{
			code: "BEMVINDO10",
			type: "percentage",
			valueCents: 1000, // 10%
			minAmountCents: 5000, // R$ 50.00 mínimo
			maxUses: 100,
			startsAt: new Date(),
			expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
		},
		{
			code: "DESCONTO50",
			type: "fixed",
			valueCents: 5000, // R$ 50.00
			minAmountCents: 10000, // R$ 100.00 mínimo
			maxUses: 50,
			startsAt: new Date(),
			expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 dias
		}
	];

	for (const couponData of coupons) {
		// Verifica se já existe
		const existingCoupon = await db.coupon.findUnique({
			where: {
				organizationId_code: {
					organizationId,
					code: couponData.code
				}
			}
		});

		if (existingCoupon) {
			logger.info(`Cupom ${couponData.code} já existe, pulando criação.`);
			continue;
		}

		await db.coupon.create({
			data: {
				organizationId,
				creatorId,
				code: couponData.code,
				type: couponData.type,
				valueCents: couponData.valueCents,
				minAmountCents: couponData.minAmountCents,
				currency: "BRL",
				maxUses: couponData.maxUses,
				usedCount: 0,
				isActive: true,
				startsAt: couponData.startsAt,
				expiresAt: couponData.expiresAt,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		logger.success(`Cupom ${couponData.code} criado!`);
	}
}

// Função principal
async function main() {
	logger.info("🚀 Iniciando seed do banco de dados...");

	try {
		// 1. Criar usuário admin
		const adminUser = await createAdminUser();

		// 2. Criar organização principal
		const organization = await createOrganization("SupGateway Academy", adminUser.id);

		// 3. Criar usuários de exemplo
		const sampleUsers = await createSampleUsers(organization.id);
		const teacherUser = sampleUsers.find(user => user.role === "TEACHER");

		// 4. Criar categorias
		const categories = await createCategories(organization.id);

		// 5. Criar produtos de exemplo
		const products = await createSampleProducts(
			organization.id,
			teacherUser?.id || adminUser.id,
			categories
		);

		// 6. Criar cupons
		await createCoupons(organization.id, adminUser.id);

		logger.success("✅ Seed concluído com sucesso!");
		logger.info(`
📊 Resumo do seed:
- 1 organização criada: ${organization.name}
- ${sampleUsers.length + 1} usuários criados
- ${categories.length} categorias criadas
- ${products.length} produtos criados
- 2 cupons criados

🔑 Credenciais de acesso:
- Admin: <EMAIL> / admin123
- Professor: <EMAIL> / teacher123
- Afiliado: <EMAIL> / affiliate123
- Cliente: <EMAIL> / user123
		`);

	} catch (error) {
		logger.error("Erro durante o seed:", error);
		process.exit(1);
	}
}

main();
