import { db } from "@repo/database/prisma/client";
import { logger } from "@repo/logs";

async function testSeed() {
	logger.info("🧪 Testando dados do seed...");

	try {
		// Verificar usuários
		const users = await db.user.findMany({
			include: {
				organizationMembers: {
					include: {
						organization: true
					}
				}
			}
		});

		logger.info(`📊 Total de usuários: ${users.length}`);
		users.forEach(user => {
			logger.info(`  - ${user.name} (${user.email}) - Role: ${user.role}`);
			user.organizationMembers.forEach(member => {
				logger.info(`    🏢 Membro de: ${member.organization.name} (${member.role})`);
			});
		});

		// Verificar organizações
		const organizations = await db.organization.findMany({
			include: {
				_count: {
					select: {
						members: true,
						products: true,
						categories: true
					}
				}
			}
		});

		logger.info(`\n🏢 Total de organizações: ${organizations.length}`);
		organizations.forEach(org => {
			logger.info(`  - ${org.name} (${org.slug})`);
			logger.info(`    👥 Membros: ${org._count.members}`);
			logger.info(`    📦 Produtos: ${org._count.products}`);
			logger.info(`    🏷️  Categorias: ${org._count.categories}`);
		});

		// Verificar produtos
		const products = await db.product.findMany({
			include: {
				category: true,
				creator: true
			}
		});

		logger.info(`\n📦 Total de produtos: ${products.length}`);
		products.forEach(product => {
			logger.info(`  - ${product.name} (${product.type})`);
			logger.info(`    💰 Preço: R$ ${(product.priceCents / 100).toFixed(2)}`);
			logger.info(`    🏷️  Categoria: ${product.category?.name || 'Sem categoria'}`);
			logger.info(`    👤 Criador: ${product.creator.name}`);
		});

		// Verificar categorias
		const categories = await db.category.findMany();
		logger.info(`\n🏷️  Total de categorias: ${categories.length}`);
		categories.forEach(category => {
			logger.info(`  - ${category.name} ${category.icon} (${category.slug})`);
		});

		// Verificar cupons
		const coupons = await db.coupon.findMany();
		logger.info(`\n🎫 Total de cupons: ${coupons.length}`);
		coupons.forEach(coupon => {
			const value = coupon.type === 'percentage'
				? `${coupon.valueCents / 100}%`
				: `R$ ${(coupon.valueCents / 100).toFixed(2)}`;
			logger.info(`  - ${coupon.code}: ${value} desconto`);
		});

		// Verificar perfis específicos
		const teacherProfiles = await db.teacherProfile.findMany({
			include: {
				user: true
			}
		});

		logger.info(`\n👨‍🏫 Total de perfis de professor: ${teacherProfiles.length}`);
		teacherProfiles.forEach(profile => {
			logger.info(`  - ${profile.user.name} (${profile.user.email})`);
			logger.info(`    📚 Expertise: ${profile.expertise.join(', ')}`);
		});

		const affiliateProfiles = await db.affiliateProfile.findMany({
			include: {
				user: true
			}
		});

		logger.info(`\n🤝 Total de perfis de afiliado: ${affiliateProfiles.length}`);
		affiliateProfiles.forEach(profile => {
			logger.info(`  - ${profile.user.name} (${profile.user.email})`);
			logger.info(`    💰 Comissão: ${(profile.commissionCents / 100).toFixed(2)}%`);
		});

		logger.success("✅ Teste do seed concluído com sucesso!");

	} catch (error) {
		logger.error("❌ Erro durante o teste:", error);
		process.exit(1);
	}
}

testSeed();
