import { db } from "@repo/database/prisma/client";
import { logger } from "@repo/logs";

async function resetDatabase() {
	logger.info("🗑️  Iniciando reset do banco de dados...");

	try {
		logger.info("Deletando dados existentes...");

		// Delete em ordem para respeitar as foreign keys
		await db.offerInteraction.deleteMany();
		await db.abandonedCheckout.deleteMany();
		await db.affiliateLink.deleteMany();
		await db.offer.deleteMany();
		await db.mentoring.deleteMany();
		await db.course.deleteMany();
		await db.ebook.deleteMany();
		await db.emailTemplate.deleteMany();
		await db.customPage.deleteMany();
		await db.webhook.deleteMany();
		await db.paymentProvider.deleteMany();
		await db.gatewayConfiguration.deleteMany();
		await db.mentoringSession.deleteMany();
		await db.coProducer.deleteMany();
		await db.coProducerInvitation.deleteMany();
		await db.teacherInvitation.deleteMany();
		await db.affiliateInvitation.deleteMany();
		await db.coupon.deleteMany();
		await db.withdraw.deleteMany();
		await db.ledgerEntry.deleteMany();
		await db.teacherProfile.deleteMany();
		await db.affiliateProfile.deleteMany();
		await db.lessonProgress.deleteMany();
		await db.asset.deleteMany();
		await db.certificate.deleteMany();
		await db.productReview.deleteMany();
		await db.courseEnrollment.deleteMany();
		await db.orderItem.deleteMany();
		await db.reconciliationEntry.deleteMany();
		await db.balanceSnapshot.deleteMany();
		await db.cashFlowEntry.deleteMany();
		await db.financialReport.deleteMany();
		await db.transactionFee.deleteMany();
		await db.transaction.deleteMany();
		await db.order.deleteMany();
		await db.productLesson.deleteMany();
		await db.productModule.deleteMany();
		await db.product.deleteMany();
		await db.category.deleteMany();
		await db.aiChat.deleteMany();
		await db.purchase.deleteMany();
		await db.invitation.deleteMany();
		await db.member.deleteMany();
		await db.organization.deleteMany();
		await db.twoFactor.deleteMany();
		await db.passkey.deleteMany();
		await db.account.deleteMany();
		await db.session.deleteMany();
		await db.user.deleteMany();
		await db.verification.deleteMany();

		logger.success("✅ Todos os dados foram deletados!");

	} catch (error) {
		logger.error("Erro ao resetar o banco de dados:", error);
		process.exit(1);
	}
}

async function main() {
	const confirmation = await logger.prompt("⚠️  Tem certeza que deseja resetar o banco de dados? Todos os dados serão perdidos! (digite 'RESET' para confirmar):", {
		required: true,
		type: "text",
	});

	if (confirmation !== "RESET") {
		logger.info("Reset cancelado.");
		return;
	}

	await resetDatabase();

	const shouldSeed = await logger.prompt("Deseja executar o seed após o reset?", {
		required: true,
		type: "confirm",
		default: true,
	});

	if (shouldSeed) {
		logger.info("Executando seed...");
		// Importar e executar o seed
		const { exec } = await import("child_process");
		const { promisify } = await import("util");
		const execAsync = promisify(exec);

		try {
			await execAsync("pnpm run seed:db", { cwd: process.cwd() });
			logger.success("✅ Reset e seed concluídos!");
		} catch (error) {
			logger.error("Erro ao executar seed:", error);
		}
	} else {
		logger.success("✅ Reset concluído!");
	}
}

main();
