# 🐳 Deploy Docker - SupGateway

Este projeto está configurado para deploy via Docker, otimizado para Google Cloud Run.

## 📁 Arquivos Criados

- `apps/web/Dockerfile` - Dockerfile multi-stage otimizado
- `.dockerignore` - Exclusão de arquivos desnecessários
- `cloud-run-deploy.md` - Guia completo de deploy
- `deploy-cloud-run.sh` - Script automatizado de deploy
- `cloud-run-service.yaml` - Configuração YAML do Cloud Run
- `apps/web/app/api/health/route.ts` - Endpoint de health check

## 🚀 Deploy Rápido

### 1. Teste Local
```bash
# Build da imagem
docker build -f apps/web/Dockerfile . --no-cache -t super-gateway:latest

# Executar container
docker run -p 3000:3000 \
  -e DATABASE_URL="sua_url_do_banco" \
  -e NEXTAUTH_SECRET="seu_secret" \
  super-gateway:latest
```

### 2. Deploy no Google Cloud Run
```bash
# Usar script automatizado
./deploy-cloud-run.sh SEU_PROJECT_ID us-central1 super-gateway

# Ou deploy manual
gcloud run deploy super-gateway \
  --image gcr.io/SEU_PROJECT_ID/super-gateway:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 🔧 Configurações

### Next.js
- ✅ `output: "standalone"` habilitado
- ✅ Build otimizado para Docker
- ✅ Suporte a monorepo com pnpm

### Docker
- ✅ Multi-stage build
- ✅ Usuário não-root (nextjs:1001)
- ✅ Otimização de camadas
- ✅ Alpine Linux para menor tamanho

### Cloud Run
- ✅ Health checks configurados
- ✅ Auto-scaling habilitado
- ✅ CPU throttling para economia
- ✅ Timeout configurado (300s)

## 🌍 Variáveis de Ambiente

Configure estas variáveis no Cloud Run:

```bash
# Obrigatórias
DATABASE_URL=sua_url_do_banco
NEXTAUTH_SECRET=seu_secret
NEXTAUTH_URL=https://sua-app.run.app

# OAuth (se usar)
GOOGLE_CLIENT_ID=seu_client_id
GOOGLE_CLIENT_SECRET=seu_client_secret

# AWS (se usar S3)
AWS_ACCESS_KEY_ID=sua_access_key
AWS_SECRET_ACCESS_KEY=sua_secret_key
AWS_REGION=sua_regiao
AWS_S3_BUCKET=seu_bucket
```

## 📊 Monitoramento

### Health Check
- Endpoint: `/api/health`
- Status: 200 (healthy) ou 503 (unhealthy)
- Métricas: uptime, environment, version

### Logs
```bash
gcloud logs tail --service=super-gateway
```

### Métricas
```bash
gcloud run services describe super-gateway
```

## 🚨 Troubleshooting

### Erro de Build
- Verifique se `turbo prune` está funcionando
- Confirme dependências no `package.json`
- Verifique versão do Node.js (22+)

### Erro de Runtime
- Verifique variáveis de ambiente
- Confirme conectividade com banco
- Verifique logs do Cloud Run

### Performance
- Ajuste `--memory` e `--cpu`
- Configure `--max-instances`
- Use `--cpu-throttling`

## 💰 Custos

- **Cloud Run**: Por requisição + tempo de execução
- **Otimizações**:
  - `--min-instances 0` (escala para zero)
  - `--cpu-throttling` (CPU sob demanda)
  - `--max-instances` (limite de escalabilidade)

## 🔒 Segurança

- ✅ Usuário não-root no container
- ✅ Porta 3000 exposta
- ✅ Variáveis de ambiente seguras
- ✅ Health checks configurados
- ✅ Timeout de requisições

## 📚 Documentação

- [Guia Completo](cloud-run-deploy.md)
- [Configuração YAML](cloud-run-service.yaml)
- [Script de Deploy](deploy-cloud-run.sh)
- [Supastarter Docker](https://supastarter.dev/docs/nextjs/deployment/docker)

## 🎯 Próximos Passos

1. **Domínio Personalizado**: Configure CNAME para Cloud Run
2. **HTTPS**: Automático com Cloud Run
3. **CDN**: Configure Cloud CDN para assets
4. **Monitoramento**: Implemente alertas e dashboards
5. **Backup**: Configure backup automático do banco
6. **CI/CD**: Integre com GitHub Actions ou Cloud Build

## 🤝 Suporte

Para dúvidas ou problemas:
1. Verifique os logs do Cloud Run
2. Consulte o guia de deploy
3. Verifique a documentação do Supastarter
4. Abra uma issue no repositório
