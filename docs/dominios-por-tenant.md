# Gerenciamento de Domínios por Tenant - SupGateway

## Visão Geral

Este documento descreve como implementar e gerenciar domínios por tenant no projeto SupGateway, permitindo que cada organização tenha:
- Subdomínios personalizados (ex: `empresa1.supgateway.com`)
- Domínios próprios (ex: `empresa1.com.br`)
- Configurações de branding e white-label por domínio

## Arquitetura Atual

### Estrutura do Banco de Dados

O projeto já possui uma estrutura base para organizações com suporte a domínios:

```prisma
model Organization {
    id                 String   @id @default(cuid())
    name               String
    slug               String?  @unique

    // Configuração de domínios
    domain             String? @unique        // Subdomínio (empresa1.supgateway.com)
    customDomain       String? @unique        // Domínio próprio (empresa1.com.br)

    // Configurações de white-label
    branding           Json?                  // Cores, fontes, etc.
    settings           Json?                  // Configurações da plataforma
    enableCustomDomain <PERSON> @default(false) // Flag para habilitar domínio próprio

    // ... outros campos
}
```

### Estrutura de Rotas Atual

```
/app/(saas)/app/(organizations)/[organizationSlug]/
```

## Implementação Proposta

### 1. Middleware de Roteamento por Domínio

#### 1.1 Atualizar o Middleware Principal

```typescript:apps/web/middleware.ts
import { routing } from "@i18n/routing";
import { config as appConfig } from "@repo/config";
import { getSessionCookie } from "better-auth/cookies";
import { type NextRequest, NextResponse } from "next/server";
import createMiddleware from "next-intl/middleware";
import { withQuery } from "ufo";
import { getOrganizationByDomain } from "@repo/database";

const intlMiddleware = createMiddleware(routing);

export default async function middleware(req: NextRequest) {
	const { pathname, origin, hostname } = req.nextUrl;

	// Extrair informações do domínio
	const domainInfo = await extractDomainInfo(hostname);

	// Se for um domínio de tenant, redirecionar para a rota correta
	if (domainInfo.isTenantDomain) {
		return handleTenantDomain(req, domainInfo);
	}

	// ... resto do middleware existente
}

async function extractDomainInfo(hostname: string) {
	// Verificar se é um subdomínio do SupGateway
	if (hostname.endsWith('.supgateway.com')) {
		const subdomain = hostname.replace('.supgateway.com', '');
		const organization = await getOrganizationByDomain(subdomain);

		if (organization) {
			return {
				isTenantDomain: true,
				type: 'subdomain',
				organization,
				subdomain
			};
		}
	}

	// Verificar se é um domínio próprio
	const organization = await getOrganizationByCustomDomain(hostname);
	if (organization) {
		return {
			isTenantDomain: true,
			type: 'custom',
			organization,
			customDomain: hostname
		};
	}

	return { isTenantDomain: false };
}

function handleTenantDomain(req: NextRequest, domainInfo: any) {
	const { pathname } = req.nextUrl;

	// Redirecionar para a rota da organização
	const newUrl = new URL(`/app/${domainInfo.organization.slug}${pathname}`, req.url);
	return NextResponse.rewrite(newUrl);
}
```

### 2. Banco de Dados - Extensões Necessárias

#### 2.1 Atualizar Schema do Prisma

```prisma:packages/database/prisma/schema.prisma
model Organization {
    // ... campos existentes ...

    // Configuração de domínios
    domain             String? @unique        // Subdomínio (empresa1)
    customDomain       String? @unique        // Domínio completo (empresa1.com.br)

    // Status e validação de domínios
    domainStatus       DomainStatus @default(PENDING)
    domainVerifiedAt   DateTime?
    domainVerificationToken String?

    // Configurações de DNS
    dnsRecords        DnsRecord[]

    // ... resto dos campos
}

enum DomainStatus {
    PENDING      // Aguardando verificação
    VERIFIED     // Domínio verificado e ativo
    FAILED       // Falha na verificação
    SUSPENDED    // Domínio suspenso
}

model DnsRecord {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    type           String       // A, CNAME, TXT, etc.
    name           String       // Nome do registro
    value          String       // Valor do registro
    ttl            Int          @default(300)

    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("dns_record")
}
```

#### 2.2 Funções de Banco de Dados

```typescript:packages/database/src/organizations.ts
import { db } from "./index";
import { eq, or } from "drizzle-orm";

export async function getOrganizationByDomain(domain: string) {
    const result = await db
        .select()
        .from(organizations)
        .where(
            or(
                eq(organizations.domain, domain),
                eq(organizations.customDomain, domain)
            )
        )
        .limit(1);

    return result[0] || null;
}

export async function getOrganizationBySubdomain(subdomain: string) {
    const result = await db
        .select()
        .from(organizations)
        .where(eq(organizations.domain, subdomain))
        .limit(1);

    return result[0] || null;
}

export async function getOrganizationByCustomDomain(customDomain: string) {
    const result = await db
        .select()
        .from(organizations)
        .where(eq(organizations.customDomain, customDomain))
        .limit(1);

    return result[0] || null;
}

export async function updateDomainStatus(
    organizationId: string,
    status: DomainStatus,
    verifiedAt?: Date
) {
    return await db
        .update(organizations)
        .set({
            domainStatus: status,
            domainVerifiedAt: verifiedAt || null,
            updatedAt: new Date()
        })
        .where(eq(organizations.id, organizationId));
}
```

### 3. API Routes para Gerenciamento de Domínios

#### 3.1 Router de Domínios

```typescript:packages/api/src/routes/domains/router.ts
import { Hono } from "hono";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import {
    getOrganizationByDomain,
    updateDomainStatus,
    createDnsRecord,
    deleteDnsRecord
} from "@repo/database";

export const domainsRouter = new Hono().basePath("/domains");

// Verificar disponibilidade de domínio
domainsRouter.get(
    "/check-availability",
    validator(
        "query",
        z.object({
            domain: z.string(),
            type: z.enum(["subdomain", "custom"])
        })
    ),
    async (c) => {
        const { domain, type } = c.req.valid("query");

        const existing = await getOrganizationByDomain(domain);

        return c.json({
            available: !existing,
            domain,
            type
        });
    }
);

// Verificar domínio
domainsRouter.post(
    "/verify",
    validator(
        "json",
        z.object({
            organizationId: z.string(),
            domain: z.string(),
            type: z.enum(["subdomain", "custom"])
        })
    ),
    async (c) => {
        const { organizationId, domain, type } = c.req.valid("json");

        try {
            const isVerified = await verifyDomain(domain, type);

            if (isVerified) {
                await updateDomainStatus(organizationId, "VERIFIED", new Date());

                return c.json({
                    success: true,
                    message: "Domínio verificado com sucesso"
                });
            } else {
                await updateDomainStatus(organizationId, "FAILED");

                return c.json({
                    success: false,
                    message: "Falha na verificação do domínio"
                }, 400);
            }
        } catch (error) {
            return c.json({
                success: false,
                message: "Erro na verificação do domínio"
            }, 500);
        }
    }
);

// Gerar registros DNS necessários
domainsRouter.get(
    "/dns-records/:organizationId",
    async (c) => {
        const { organizationId } = c.req.param();

        const dnsRecords = await generateDnsRecords(organizationId);

        return c.json({
            records: dnsRecords
        });
    }
);

// ... outras rotas
```

#### 3.2 Funções de Verificação de Domínio

```typescript:packages/api/src/routes/domains/lib/verification.ts
import dns from "dns";
import { promisify } from "util";

const resolveTxt = promisify(dns.resolveTxt);
const resolveCname = promisify(dns.resolveCname);

export async function verifyDomain(domain: string, type: "subdomain" | "custom") {
    if (type === "subdomain") {
        return await verifySubdomain(domain);
    } else {
        return await verifyCustomDomain(domain);
    }
}

async function verifySubdomain(subdomain: string) {
    try {
        const fullDomain = `${subdomain}.supgateway.com`;

        // Verificar se o subdomínio resolve para nosso servidor
        const cnameRecords = await resolveCname(fullDomain);

        // Verificar se aponta para o domínio principal
        return cnameRecords.some(record =>
            record.includes('supgateway.com') ||
            record.includes(process.env.VERCEL_URL || '')
        );
    } catch (error) {
        return false;
    }
}

async function verifyCustomDomain(customDomain: string) {
    try {
        // Verificar registro TXT para verificação
        const txtRecords = await resolveTxt(customDomain);
        const verificationToken = process.env.DOMAIN_VERIFICATION_TOKEN;

        // Verificar se existe o token de verificação
        const hasVerificationToken = txtRecords.some(records =>
            records.some(record => record.includes(verificationToken))
        );

        if (!hasVerificationToken) {
            return false;
        }

        // Verificar se o domínio resolve para nosso servidor
        const cnameRecords = await resolveCname(customDomain);

        return cnameRecords.some(record =>
            record.includes('supgateway.com') ||
            record.includes(process.env.VERCEL_URL || '')
        );
    } catch (error) {
        return false;
    }
}

export function generateDnsRecords(organizationId: string) {
    return [
        {
            type: "CNAME",
            name: "@",
            value: "supgateway.com",
            ttl: 300,
            description: "Aponta o domínio para o SupGateway"
        },
        {
            type: "TXT",
            name: "@",
            value: `supgateway-verification=${organizationId}`,
            ttl: 300,
            description: "Token de verificação do domínio"
        }
    ];
}
```

### 4. Interface de Usuário para Gerenciamento

#### 4.1 Componente de Configuração de Domínios

```typescript:apps/web/modules/organizations/components/DomainSettings.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

interface DomainSettingsProps {
    organization: {
        id: string;
        slug: string;
        domain?: string;
        customDomain?: string;
        domainStatus: string;
        enableCustomDomain: boolean;
    };
}

export function DomainSettings({ organization }: DomainSettingsProps) {
    const [subdomain, setSubdomain] = useState(organization.domain || "");
    const [customDomain, setCustomDomain] = useState(organization.customDomain || "");
    const [isLoading, setIsLoading] = useState(false);
    const { toast } = useToast();

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "VERIFIED":
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case "PENDING":
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case "FAILED":
                return <XCircle className="h-4 w-4 text-red-500" />;
            default:
                return <AlertCircle className="h-4 w-4 text-gray-500" />;
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case "VERIFIED":
                return "Verificado";
            case "PENDING":
                return "Aguardando Verificação";
            case "FAILED":
                return "Falha na Verificação";
            default:
                return "Desconhecido";
        }
    };

    const handleSubdomainSubmit = async () => {
        if (!subdomain) return;

        setIsLoading(true);
        try {
            const response = await fetch("/api/organizations/domains", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    organizationId: organization.id,
                    domain: subdomain,
                    type: "subdomain"
                })
            });

            if (response.ok) {
                toast({
                    title: "Subdomínio configurado",
                    description: `Seu subdomínio ${subdomain}.supgateway.com foi configurado com sucesso.`
                });
            }
        } catch (error) {
            toast({
                title: "Erro",
                description: "Erro ao configurar subdomínio.",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleCustomDomainSubmit = async () => {
        if (!customDomain) return;

        setIsLoading(true);
        try {
            const response = await fetch("/api/organizations/domains", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    organizationId: organization.id,
                    domain: customDomain,
                    type: "custom"
                })
            });

            if (response.ok) {
                toast({
                    title: "Domínio configurado",
                    description: `Seu domínio ${customDomain} foi configurado. Aguarde a verificação.`
                });
            }
        } catch (error) {
            toast({
                title: "Erro",
                description: "Erro ao configurar domínio.",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Subdomínio */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        Subdomínio
                        {organization.domain && (
                            <Badge variant="secondary">
                                {organization.domain}.supgateway.com
                            </Badge>
                        )}
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex gap-2">
                        <Input
                            placeholder="empresa1"
                            value={subdomain}
                            onChange={(e) => setSubdomain(e.target.value)}
                            className="flex-1"
                        />
                        <span className="text-sm text-muted-foreground self-center">
                            .supgateway.com
                        </span>
                        <Button
                            onClick={handleSubdomainSubmit}
                            disabled={isLoading || !subdomain}
                        >
                            {isLoading ? "Configurando..." : "Configurar"}
                        </Button>
                    </div>

                    {organization.domain && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            {getStatusIcon(organization.domainStatus)}
                            {getStatusText(organization.domainStatus)}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Domínio Próprio */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        Domínio Próprio
                        {organization.customDomain && (
                            <Badge variant="secondary">
                                {organization.customDomain}
                            </Badge>
                        )}
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex gap-2">
                        <Input
                            placeholder="empresa1.com.br"
                            value={customDomain}
                            onChange={(e) => setCustomDomain(e.target.value)}
                            className="flex-1"
                        />
                        <Button
                            onClick={handleCustomDomainSubmit}
                            disabled={isLoading || !customDomain}
                        >
                            {isLoading ? "Configurando..." : "Configurar"}
                        </Button>
                    </div>

                    {organization.customDomain && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            {getStatusIcon(organization.domainStatus)}
                            {getStatusText(organization.domainStatus)}
                        </div>
                    )}

                    <div className="text-sm text-muted-foreground">
                        <p>Para usar seu domínio próprio, você precisa:</p>
                        <ol className="list-decimal list-inside mt-2 space-y-1">
                            <li>Configurar os registros DNS conforme indicado</li>
                            <li>Aguardar a verificação automática (pode levar até 24h)</li>
                            <li>Após verificação, seu domínio estará ativo</li>
                        </ol>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
```

#### 4.2 Página de Configurações da Organização

```typescript:apps/web/app/(saas)/app/(organizations)/[organizationSlug]/settings/domains/page.tsx
import { DomainSettings } from "@/modules/organizations/components/DomainSettings";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";

export default async function DomainSettingsPage({
    params,
}: {
    params: Promise<{ organizationSlug: string }>;
}) {
    const { organizationSlug } = await params;
    const organization = await getActiveOrganization(organizationSlug);

    if (!organization) {
        return notFound();
    }

    return (
        <div className="container mx-auto py-6">
            <div className="mb-6">
                <h1 className="text-3xl font-bold">Configurações de Domínio</h1>
                <p className="text-muted-foreground">
                    Configure subdomínios e domínios próprios para sua organização
                </p>
            </div>

            <DomainSettings organization={organization} />
        </div>
    );
}
```

### 5. Configuração do Next.js

#### 5.1 Atualizar next.config.ts

```typescript:apps/web/next.config.ts
import { withContentlayer } from "next-contentlayer";
import { i18n } from "./i18n";

/** @type {import('next').NextConfig} */
const nextConfig = {
    // ... configurações existentes ...

    // Configuração para múltiplos domínios
    async rewrites() {
        return [
            // Redirecionar subdomínios para a rota correta
            {
                source: "/:path*",
                has: [
                    {
                        type: "host",
                        value: "(?<subdomain>[^.]+)\.supgateway\.com",
                    },
                ],
                destination: "/app/:subdomain/:path*",
            },
        ];
    },

    // Configuração de domínios permitidos
    async headers() {
        return [
            {
                source: "/(.*)",
                headers: [
                    {
                        key: "X-Frame-Options",
                        value: "SAMEORIGIN",
                    },
                    {
                        key: "X-Content-Type-Options",
                        value: "nosniff",
                    },
                ],
            },
        ];
    },
};

export default withContentlayer(nextConfig);
```

### 6. Configuração de DNS e Infraestrutura

#### 6.1 Configuração do Vercel (ou similar)

```json
{
  "domains": [
    "supgateway.com",
    "*.supgateway.com"
  ],
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/app/$1"
    }
  ]
}
```

#### 6.2 Configuração de Wildcard DNS

Para suportar subdomínios dinâmicos, configure um registro wildcard:

```
*.supgateway.com CNAME supgateway.com
```

### 7. Segurança e Validação

#### 7.1 Validação de Domínios

```typescript:packages/api/src/routes/domains/lib/validation.ts
import { z } from "zod";

export const domainValidationSchema = z.object({
    domain: z.string()
        .min(3, "Domínio deve ter pelo menos 3 caracteres")
        .max(63, "Domínio deve ter no máximo 63 caracteres")
        .regex(/^[a-z0-9-]+$/, "Domínio deve conter apenas letras minúsculas, números e hífens")
        .refine(domain => !domain.startsWith("-") && !domain.endsWith("-"), {
            message: "Domínio não pode começar ou terminar com hífen"
        }),
    type: z.enum(["subdomain", "custom"]),
    organizationId: z.string().cuid()
});

export const forbiddenDomains = [
    "www", "api", "admin", "app", "mail", "ftp", "smtp", "pop", "imap",
    "ns1", "ns2", "dns", "webmail", "cpanel", "whm", "mail", "ftp"
];

export function validateDomain(domain: string, type: "subdomain" | "custom") {
    // Verificar domínios proibidos
    if (type === "subdomain" && forbiddenDomains.includes(domain)) {
        return {
            valid: false,
            error: "Este subdomínio não está disponível"
        };
    }

    // Verificar formato
    if (type === "custom") {
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(domain)) {
            return {
                valid: false,
                error: "Formato de domínio inválido"
            };
        }
    }

    return { valid: true };
}
```

### 8. Monitoramento e Logs

#### 8.1 Logs de Domínios

```typescript:packages/api/src/routes/domains/lib/logging.ts
import { logger } from "@repo/logs";

export function logDomainAction(action: string, data: any) {
    logger.info("Domain action", {
        action,
        organizationId: data.organizationId,
        domain: data.domain,
        type: data.type,
        timestamp: new Date().toISOString(),
        userId: data.userId
    });
}

export function logDomainError(error: Error, context: any) {
    logger.error("Domain error", {
        error: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString()
    });
}
```

### 9. Testes

#### 9.1 Testes de Integração

```typescript:apps/web/tests/domains.spec.ts
import { test, expect } from "@playwright/test";

test.describe("Domain Management", () => {
    test("should allow setting subdomain", async ({ page }) => {
        // Teste de configuração de subdomínio
    });

    test("should allow setting custom domain", async ({ page }) => {
        // Teste de configuração de domínio próprio
    });

    test("should validate domain format", async ({ page }) => {
        // Teste de validação de formato
    });

    test("should handle domain conflicts", async ({ page }) => {
        // Teste de conflitos de domínio
    });
});
```

## Fluxo de Implementação

### Fase 1: Estrutura Base
1. Atualizar schema do banco de dados
2. Implementar funções de banco de dados
3. Criar API routes básicas

### Fase 2: Middleware e Roteamento
1. Implementar middleware de domínio
2. Configurar roteamento dinâmico
3. Testar redirecionamentos

### Fase 3: Interface de Usuário
1. Criar componentes de configuração
2. Implementar validações
3. Adicionar feedback visual

### Fase 4: Verificação e DNS
1. Implementar verificação automática
2. Criar sistema de registros DNS
3. Configurar infraestrutura

### Fase 5: Testes e Polimento
1. Testes de integração
2. Testes de segurança
3. Documentação e treinamento

## Considerações de Segurança

1. **Validação de Domínios**: Implementar validação rigorosa de formatos
2. **Rate Limiting**: Limitar tentativas de configuração de domínios
3. **Verificação de Propriedade**: Confirmar propriedade do domínio antes da ativação
4. **Monitoramento**: Logs de todas as ações relacionadas a domínios
5. **Sanitização**: Limpar e validar entradas de usuário

## Monitoramento e Manutenção

1. **Verificação Automática**: Verificar status dos domínios periodicamente
2. **Alertas**: Notificar sobre domínios com problemas
3. **Backup**: Manter backup das configurações de domínio
4. **Métricas**: Acompanhar uso e performance dos domínios

## Conclusão

Esta implementação fornece uma solução robusta para gerenciamento de domínios por tenant, permitindo que cada organização tenha sua identidade digital personalizada enquanto mantém a segurança e escalabilidade da plataforma.

A solução é modular e pode ser implementada em fases, permitindo testes incrementais e ajustes baseados no feedback dos usuários.
