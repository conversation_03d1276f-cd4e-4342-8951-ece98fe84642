# Conversations Component Architecture Plan

## Overview
This document outlines the component architecture for the Conversations feature, following established platform patterns and best practices from the finance dashboard implementation.

## Architecture Principles

1. **Consistency**: Follow established patterns (PageHeader, PageTabs, DataTable, ActionBar)
2. **Modularity**: Each component has a single responsibility
3. **Reusability**: Components can be used across different contexts
4. **Scalability**: Architecture supports future enhancements
5. **Performance**: Optimized rendering and state management

## File Structure

```
apps/web/
├── app/(saas)/app/(organizations)/[organizationSlug]/(header)/
│   └── conversations/
│       └── page.tsx                    # Main conversations page
├── modules/saas/conversations/
│   ├── components/
│   │   ├── index.ts                    # Export all components
│   │   ├── ConversationsOverview.tsx   # Overview tab component
│   │   ├── ConversationsList.tsx       # Main conversations interface
│   │   ├── ConversationsReports.tsx    # Analytics and reports
│   │   ├── ConversationsSettings.tsx   # Configuration settings
│   │   ├── chat/
│   │   │   ├── ChatArea.tsx            # Main chat interface
│   │   │   ├── MessageList.tsx         # Messages display
│   │   │   ├── MessageInput.tsx        # Message composition
│   │   │   ├── MessageBubble.tsx       # Individual message
│   │   │   └── AttachmentPreview.tsx   # File attachments
│   │   ├── contacts/
│   │   │   ├── ContactPanel.tsx        # Contact information sidebar
│   │   │   ├── ContactDetails.tsx      # Detailed contact view
│   │   │   ├── ContactForm.tsx         # Contact editing form
│   │   │   └── ContactTags.tsx         # Contact tagging system
│   │   ├── sidebar/
│   │   │   ├── ConversationSidebar.tsx # Left sidebar with filters
│   │   │   ├── ConversationItem.tsx    # Individual conversation item
│   │   │   ├── ConversationFilters.tsx # Filter controls
│   │   │   └── ConversationSearch.tsx  # Search functionality
│   │   └── shared/
│   │       ├── StatusBadge.tsx         # Status indicators
│   │       ├── ChannelIcon.tsx         # Communication channel icons
│   │       ├── AssignmentSelect.tsx    # Agent assignment
│   │       ├── TagManager.tsx          # Tag management
│   │       └── EmptyState.tsx          # Empty state component
│   ├── hooks/
│   │   ├── useConversations.ts         # Conversations data fetching
│   │   ├── useMessages.ts              # Messages management
│   │   ├── useContacts.ts              # Contacts data
│   │   ├── useRealtime.ts              # WebSocket/real-time updates
│   │   └── useConversationFilters.ts   # Filter state management
│   ├── types/
│   │   ├── conversation.ts             # Conversation type definitions
│   │   ├── message.ts                  # Message type definitions
│   │   ├── contact.ts                  # Contact type definitions
│   │   └── index.ts                    # Export all types
│   └── utils/
│       ├── messageFormatting.ts        # Message formatting utilities
│       ├── timeFormatting.ts           # Time/date formatting
│       ├── channelHelpers.ts           # Channel-specific utilities
│       └── conversationHelpers.ts      # Conversation utilities
```

## Main Page Structure

### 1. Main Conversations Page
```tsx
// apps/web/app/(saas)/app/(organizations)/[organizationSlug]/(header)/conversations/page.tsx

export default async function ConversationsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const tabs = [
    {
      value: "conversations",
      label: "Conversas",
      badge: "23",
      content: <ConversationsList organizationId={organization.id} />,
    },
    {
      value: "overview",
      label: "Visão Geral",
      content: <ConversationsOverview organizationId={organization.id} />,
    },
    {
      value: "reports",
      label: "Relatórios",
      content: <ConversationsReports organizationId={organization.id} />,
    },
    {
      value: "settings",
      label: "Configurações",
      content: <ConversationsSettings organizationId={organization.id} />,
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Conversas"
        subtitle="Gerencie todas as conversas com seus clientes"
        actions={
          <>
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <UsersIcon className="h-4 w-4 mr-2" />
              Contatos
            </Button>
            <Button>
              <MessageSquareIcon className="h-4 w-4 mr-2" />
              Nova Conversa
            </Button>
          </>
        }
      />

      <PageTabs tabs={tabs} defaultValue="conversations" />
    </div>
  );
}
```

## Core Components

### 2. ConversationsList (Main Interface)
```tsx
// Three-column layout inspired by Go High Level
export function ConversationsList({ organizationId }: { organizationId: string }) {
  return (
    <div className="flex h-[calc(100vh-200px)] bg-background border rounded-lg overflow-hidden">
      {/* Left Sidebar - Conversation List */}
      <div className="w-80 border-r flex flex-col">
        <ConversationSidebar organizationId={organizationId} />
      </div>
      
      {/* Center - Chat Area */}
      <div className="flex-1 flex flex-col">
        <ChatArea />
      </div>
      
      {/* Right Sidebar - Contact Panel */}
      <div className="w-80 border-l">
        <ContactPanel />
      </div>
    </div>
  );
}
```

### 3. ConversationSidebar
```tsx
export function ConversationSidebar({ organizationId }: { organizationId: string }) {
  return (
    <>
      {/* Search and Filters */}
      <div className="p-4 border-b">
        <ConversationSearch />
        <ConversationFilters />
      </div>
      
      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {conversations.map((conversation) => (
          <ConversationItem
            key={conversation.id}
            conversation={conversation}
            isSelected={selectedId === conversation.id}
            onClick={() => setSelectedId(conversation.id)}
          />
        ))}
      </div>
    </>
  );
}
```

### 4. ChatArea
```tsx
export function ChatArea() {
  return (
    <>
      {/* Chat Header */}
      <div className="p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar />
            <div>
              <h3 className="font-semibold">Contact Name</h3>
              <p className="text-sm text-muted-foreground">Online</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <PhoneIcon className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <VideoIcon className="h-4 w-4" />
            </Button>
            <AssignmentSelect />
          </div>
        </div>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto">
        <MessageList />
      </div>
      
      {/* Message Input */}
      <div className="border-t bg-background">
        <MessageInput />
      </div>
    </>
  );
}
```

### 5. ContactPanel
```tsx
export function ContactPanel() {
  return (
    <div className="h-full flex flex-col">
      {/* Contact Header */}
      <div className="p-4 border-b">
        <div className="text-center">
          <Avatar className="w-16 h-16 mx-auto mb-2" />
          <h3 className="font-semibold">Contact Name</h3>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
      </div>
      
      {/* Contact Details */}
      <div className="flex-1 overflow-y-auto p-4">
        <ContactDetails />
        <ContactTags />
        <ConversationNotes />
      </div>
    </div>
  );
}
```

## Tab Components

### 6. ConversationsOverview
```tsx
export function ConversationsOverview({ organizationId }: { organizationId: string }) {
  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total de Conversas"
          value="1,234"
          change="+12%"
          icon={MessageSquareIcon}
        />
        <MetricCard
          title="Conversas Ativas"
          value="89"
          change="+5%"
          icon={ActivityIcon}
        />
        <MetricCard
          title="Tempo Médio de Resposta"
          value="2.5min"
          change="-15%"
          icon={ClockIcon}
        />
        <MetricCard
          title="Taxa de Resolução"
          value="94%"
          change="+3%"
          icon={CheckCircleIcon}
        />
      </div>
      
      {/* Recent Activity */}
      <div className="grid gap-6 md:grid-cols-2">
        <RecentConversations />
        <TeamPerformance />
      </div>
    </div>
  );
}
```

### 7. ConversationsReports
```tsx
export function ConversationsReports({ organizationId }: { organizationId: string }) {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="performance">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="channels">Canais</TabsTrigger>
          <TabsTrigger value="agents">Agentes</TabsTrigger>
          <TabsTrigger value="satisfaction">Satisfação</TabsTrigger>
        </TabsList>
        
        <TabsContent value="performance">
          <PerformanceReports />
        </TabsContent>
        
        <TabsContent value="channels">
          <ChannelReports />
        </TabsContent>
        
        <TabsContent value="agents">
          <AgentReports />
        </TabsContent>
        
        <TabsContent value="satisfaction">
          <SatisfactionReports />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

### 8. ConversationsSettings
```tsx
export function ConversationsSettings({ organizationId }: { organizationId: string }) {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="channels">
        <TabsList>
          <TabsTrigger value="channels">Canais</TabsTrigger>
          <TabsTrigger value="automation">Automação</TabsTrigger>
          <TabsTrigger value="teams">Equipes</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
        </TabsList>
        
        <TabsContent value="channels">
          <ChannelSettings />
        </TabsContent>
        
        <TabsContent value="automation">
          <AutomationSettings />
        </TabsContent>
        
        <TabsContent value="teams">
          <TeamSettings />
        </TabsContent>
        
        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## Key Features

1. **Responsive Design**: Adapts to different screen sizes
2. **Real-time Updates**: WebSocket integration for live messages
3. **Keyboard Shortcuts**: Power user features
4. **Drag & Drop**: File attachments
5. **Emoji Support**: Rich text messaging
6. **Search & Filter**: Advanced conversation filtering
7. **Assignment System**: Team collaboration
8. **Tag Management**: Organization and categorization
9. **Contact Management**: Comprehensive customer profiles
10. **Analytics**: Performance metrics and reporting

This architecture provides a solid foundation for a professional conversations system that follows established patterns while providing advanced functionality for customer communication management.
