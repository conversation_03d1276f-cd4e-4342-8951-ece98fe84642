# Conversations Database Schema Design

## Overview
This document outlines the database schema design for the Conversations feature, supporting multi-channel customer communication with advanced features like contact management, message tracking, assignments, and automation.

## Core Models

### 1. Contact Model
```prisma
model Contact {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Basic Information
  name           String
  email          String?
  phone          String?
  avatar         String?
  
  // Contact Details
  company        String?
  jobTitle       String?
  website        String?
  address        String?
  city           String?
  state          String?
  country        String?
  timezone       String?
  
  // Status & Metadata
  status         ContactStatus @default(ACTIVE) // ACTIVE, BLOCKED, ARCHIVED
  source         String? // How they were acquired
  lastSeenAt     DateTime?
  
  // Relationships
  conversations  Conversation[]
  messages       Message[]
  tags           ContactTag[]
  customFields   ContactCustomField[]
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("contacts")
}

enum ContactStatus {
  ACTIVE
  BLOCKED
  ARCHIVED
}
```

### 2. Conversation Model
```prisma
model Conversation {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  contactId      String
  contact        Contact      @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  // Conversation Details
  title          String?
  channel        ConversationChannel
  status         ConversationStatus @default(OPEN)
  priority       ConversationPriority @default(NORMAL)
  
  // Assignment & Management
  assignedToId   String?
  assignedTo     User?        @relation("AssignedConversations", fields: [assignedToId], references: [id])
  teamId         String?
  
  // Tracking
  isUnread       Boolean      @default(true)
  isStarred      Boolean      @default(false)
  lastMessageAt  DateTime?
  lastActivityAt DateTime?
  
  // External Integration
  externalId     String? // WhatsApp conversation ID, etc.
  externalData   Json? // Channel-specific metadata
  
  // Relationships
  messages       Message[]
  participants   ConversationParticipant[]
  tags           ConversationTag[]
  notes          ConversationNote[]
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("conversations")
}

enum ConversationChannel {
  WHATSAPP
  SMS
  EMAIL
  WEBCHAT
  FACEBOOK
  INSTAGRAM
  TELEGRAM
  INTERNAL
}

enum ConversationStatus {
  OPEN
  PENDING
  RESOLVED
  CLOSED
  SNOOZED
}

enum ConversationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
```

### 3. Message Model
```prisma
model Message {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  contactId      String?
  contact        Contact?     @relation(fields: [contactId], references: [id])
  senderId       String?
  sender         User?        @relation("SentMessages", fields: [senderId], references: [id])
  
  // Message Content
  content        String
  messageType    MessageType  @default(TEXT)
  direction      MessageDirection
  
  // Message Status
  status         MessageStatus @default(SENT)
  deliveredAt    DateTime?
  readAt         DateTime?
  
  // Attachments & Media
  attachments    MessageAttachment[]
  
  // External Integration
  externalId     String? // WhatsApp message ID, etc.
  externalData   Json? // Channel-specific metadata
  
  // Threading
  replyToId      String?
  replyTo        Message? @relation("MessageReplies", fields: [replyToId], references: [id])
  replies        Message[] @relation("MessageReplies")
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("messages")
}

enum MessageType {
  TEXT
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  LOCATION
  CONTACT
  STICKER
  TEMPLATE
  SYSTEM
}

enum MessageDirection {
  INBOUND
  OUTBOUND
}

enum MessageStatus {
  SENT
  DELIVERED
  READ
  FAILED
  PENDING
}
```

### 4. Supporting Models

#### Message Attachments
```prisma
model MessageAttachment {
  id        String      @id @default(cuid())
  messageId String
  message   Message     @relation(fields: [messageId], references: [id], onDelete: Cascade)
  
  fileName  String
  fileSize  Int
  mimeType  String
  url       String
  
  createdAt DateTime @default(now())
  
  @@map("message_attachments")
}
```

#### Conversation Participants
```prisma
model ConversationParticipant {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation("ConversationParticipants", fields: [userId], references: [id])
  
  role           ParticipantRole @default(AGENT)
  joinedAt       DateTime     @default(now())
  leftAt         DateTime?
  
  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

enum ParticipantRole {
  AGENT
  SUPERVISOR
  OBSERVER
}
```

#### Tags System
```prisma
model Tag {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  name           String
  color          String       @default("#3B82F6")
  description    String?
  
  // Relationships
  contactTags       ContactTag[]
  conversationTags  ConversationTag[]
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@unique([organizationId, name])
  @@map("tags")
}

model ContactTag {
  id        String  @id @default(cuid())
  contactId String
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  tagId     String
  tag       Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([contactId, tagId])
  @@map("contact_tags")
}

model ConversationTag {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  tagId          String
  tag            Tag          @relation(fields: [tagId], references: [id], onDelete: Cascade)
  
  createdAt      DateTime @default(now())
  
  @@unique([conversationId, tagId])
  @@map("conversation_tags")
}
```

#### Notes System
```prisma
model ConversationNote {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  authorId       String
  author         User         @relation("ConversationNotes", fields: [authorId], references: [id])
  
  content        String
  isPrivate      Boolean      @default(false)
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("conversation_notes")
}
```

#### Custom Fields
```prisma
model ContactCustomField {
  id        String  @id @default(cuid())
  contactId String
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  fieldName String
  fieldValue String
  fieldType String @default("text") // text, number, date, boolean, select
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([contactId, fieldName])
  @@map("contact_custom_fields")
}
```

## User Model Extensions

```prisma
// Add to existing User model
model User {
  // ... existing fields
  
  // Conversations relationships
  assignedConversations    Conversation[] @relation("AssignedConversations")
  sentMessages            Message[]      @relation("SentMessages")
  conversationParticipants ConversationParticipant[] @relation("ConversationParticipants")
  conversationNotes       ConversationNote[] @relation("ConversationNotes")
}
```

## Organization Model Extensions

```prisma
// Add to existing Organization model
model Organization {
  // ... existing fields
  
  // Conversations relationships
  contacts      Contact[]
  conversations Conversation[]
  tags          Tag[]
}
```

## Key Features Supported

1. **Multi-channel Communication**: WhatsApp, SMS, Email, Web Chat, Social Media
2. **Contact Management**: Comprehensive contact profiles with custom fields
3. **Assignment System**: Assign conversations to team members
4. **Tagging System**: Organize contacts and conversations with tags
5. **Message Tracking**: Delivery status, read receipts, timestamps
6. **Notes System**: Private and public notes on conversations
7. **Attachment Support**: Images, documents, audio, video files
8. **Threading**: Reply to specific messages
9. **External Integration**: Support for external platform IDs and metadata
10. **Status Management**: Track conversation and contact status
11. **Priority System**: Prioritize urgent conversations
12. **Team Collaboration**: Multiple agents can participate in conversations

This schema provides a solid foundation for a professional conversations system that can scale and integrate with various communication channels.
