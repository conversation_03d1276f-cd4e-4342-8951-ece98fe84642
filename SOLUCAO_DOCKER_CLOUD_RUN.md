# Solução para Problema do Docker no Google Cloud Run

## Problema Identificado

O erro ocorreu porque:

1. **Contexto de Build Incorreto**: O Dockerfile estava localizado em `apps/web/` mas tentando copiar arquivos do diretório raiz do monorepo
2. **Arquivos Excluídos**: O `.dockerignore` estava excluindo arquivos essenciais como `pnpm-lock.yaml`
3. **Caminhos Relativos**: Os comandos `COPY` no Dockerfile não estavam considerando o contexto de build correto

## Erro Original

```
COPY failed: file not found in build context or excluded by .dockerignore: stat pnpm-lock.yaml: file does not exist
```

## Soluções Implementadas

### 1. Dockerfile Otimizado (`Dockerfile.cloud-run`)

Criamos um novo Dockerfile específico para Cloud Run que:
- Usa multi-stage builds para otimização
- Copia corretamente os arquivos do monorepo
- Inclui health check para Google Cloud Run
- Segue as melhores práticas de segurança

### 2. .dockerignore Corrigido

Atualizamos o `.dockerignore` da raiz para:
- Não excluir arquivos essenciais do monorepo
- Incluir regras de exceção (`!pnpm-lock.yaml`, `!config/`, etc.)
- Manter apenas arquivos desnecessários excluídos

### 3. Script de Deploy Atualizado

O script `deploy-cloud-run.sh` agora:
- Prioriza o novo `Dockerfile.cloud-run`
- Verifica se arquivos essenciais existem
- Valida o `.dockerignore`
- Fornece feedback mais detalhado

### 4. Script de Teste Local

Criamos `test-docker-build.sh` para:
- Testar o build localmente antes do deploy
- Validar se o Dockerfile funciona
- Verificar se o container executa corretamente

## Como Usar

### 1. Teste Local (Recomendado)

```bash
# Testar o build localmente
./test-docker-build.sh

# Ou testar um Dockerfile específico
./test-docker-build.sh Dockerfile.ultra-simple
```

### 2. Deploy no Cloud Run

```bash
# Deploy com configurações padrão
./deploy-cloud-run.sh

# Deploy com parâmetros específicos
./deploy-cloud-run.sh "seu-project-id" "us-central1" "super-gateway"
```

## Estrutura de Arquivos

```
super-gateway/
├── .dockerignore                    # Exclui arquivos desnecessários
├── apps/web/
│   ├── Dockerfile                   # Dockerfile original (corrigido)
│   ├── Dockerfile.cloud-run        # Dockerfile otimizado para Cloud Run
│   ├── Dockerfile.ultra-simple     # Dockerfile simples alternativo
│   └── .dockerignore               # Exclui arquivos específicos da web app
├── deploy-cloud-run.sh             # Script de deploy
├── test-docker-build.sh            # Script de teste local
└── SOLUCAO_DOCKER_CLOUD_RUN.md    # Esta documentação
```

## Verificações Importantes

### Antes do Deploy

1. **Arquivos Essenciais**: Verifique se existem na raiz:
   - `pnpm-lock.yaml`
   - `pnpm-workspace.yaml`
   - `package.json`
   - `turbo.json`

2. **Contexto de Build**: Execute sempre da raiz do monorepo

3. **Dockerfile**: Use `Dockerfile.cloud-run` para melhor performance

### Durante o Build

1. **Logs do Cloud Build**: Monitore os logs para identificar problemas
2. **Tempo de Build**: O primeiro build pode demorar devido ao cache
3. **Dependências**: Verifique se todas as dependências estão sendo copiadas

## Troubleshooting

### Erro: "file not found in build context"

**Causa**: Arquivo não existe ou está sendo excluído pelo `.dockerignore`

**Solução**:
```bash
# Verificar se arquivo existe
ls -la pnpm-lock.yaml

# Verificar .dockerignore
grep -n "pnpm-lock.yaml" .dockerignore

# Adicionar exceção se necessário
echo "!pnpm-lock.yaml" >> .dockerignore
```

### Erro: "COPY failed"

**Causa**: Caminho incorreto no Dockerfile

**Solução**: Verificar se os comandos `COPY` estão corretos:
```dockerfile
# ✅ Correto - copia da raiz do contexto
COPY pnpm-lock.yaml ./

# ❌ Incorreto - caminho relativo incorreto
COPY ../pnpm-lock.yaml ./
```

### Build Lento

**Causa**: Cache não otimizado

**Solução**: Usar multi-stage builds e copiar apenas arquivos necessários:
```dockerfile
# Stage 1: Dependências
COPY package*.json ./
RUN npm install

# Stage 2: Código fonte
COPY . .
RUN npm run build
```

## Próximos Passos

1. **Teste Local**: Execute `./test-docker-build.sh` para validar
2. **Deploy**: Use `./deploy-cloud-run.sh` para deploy no Cloud Run
3. **Monitoramento**: Configure logs e métricas no Cloud Run
4. **Otimização**: Ajuste recursos (CPU, memória) conforme necessário

## Recursos Adicionais

- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Docker Multi-stage Builds](https://docs.docker.com/develop/dev-best-practices/multistage-build/)
- [Next.js Docker Deployment](https://nextjs.org/docs/deployment#docker-image)
- [Monorepo Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/multistage-build/)
