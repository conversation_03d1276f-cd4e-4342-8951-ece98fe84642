{"name": "supgateway", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "start": "turbo start", "lint": "biome lint --assist-enabled . ", "check": "biome check", "clean": "turbo clean", "format": "biome format . --write"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.10.2", "turbo": "^2.5.4", "typescript": "5.7.2"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}, "dependencies": {"decimal.js": "^10.6.0"}}