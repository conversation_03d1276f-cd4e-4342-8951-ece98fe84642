#!/bin/bash

# Script de build Docker otimizado para resolver problemas de memória
echo "🐳 Iniciando build Docker otimizado..."

# Configurar variáveis de ambiente para otimizar memória
export NODE_OPTIONS="--max-old-space-size=8192"
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Limpar containers e imagens antigas
echo "🧹 Limpando containers e imagens antigas..."
docker system prune -f
docker volume prune -f

# Build com configurações otimizadas
echo "🔨 Fazendo build Docker otimizado..."
docker build -f apps/web/Dockerfile.build -t SupGateway:optimized .

if [ $? -eq 0 ]; then
    echo "✅ Build Docker concluído com sucesso!"
    echo "🚀 Para executar: docker run -p 3000:3000 SupGateway:optimized"
else
    echo "❌ Erro no build Docker"
    exit 1
fi
