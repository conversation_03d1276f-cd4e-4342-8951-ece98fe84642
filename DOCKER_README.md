# Docker Setup para Google Cloud Run

Este projeto foi configurado para funcionar corretamente com Google Cloud Run usando um Dockerfile na raiz do monorepo.

## 🐳 Estrutura do Dockerfile

O `Dockerfile` está localizado na **raiz do projeto** (não em `apps/web/`) para garantir que todos os arquivos do monorepo estejam disponíveis durante o build.

### Características principais:

- **Multi-stage build** para otimizar o tamanho da imagem final
- **Suporte a pnpm** com workspace configuration
- **Build do Next.js** com output standalone
- **Usuário não-root** para segurança
- **Health check** para Google Cloud Run
- **Otimizações** para produção

## 🚀 Como usar

### 1. Teste local

```bash
# Testar se o Dockerfile funciona localmente
./test-docker-build.sh
```

### 2. Deploy para Google Cloud Run

```bash
# Deploy automático
./deploy-cloud-run.sh [PROJECT_ID] [REGION] [SERVICE_NAME]

# Exemplo:
./deploy-cloud-run.sh meu-projeto us-central1 super-gateway
```

### 3. Build manual

```bash
# Build local
docker build -t super-gateway .

# Executar localmente
docker run -p 3000:3000 super-gateway
```

## 📁 Arquivos necessários

O Dockerfile requer os seguintes arquivos na raiz do projeto:

- `package.json` - Configuração do projeto
- `pnpm-lock.yaml` - Lock file das dependências
- `pnpm-workspace.yaml` - Configuração dos workspaces
- `turbo.json` - Configuração do Turborepo
- `config/` - Configurações do projeto
- `packages/` - Pacotes compartilhados
- `tooling/` - Ferramentas de build
- `apps/web/` - Aplicação Next.js

## 🔧 Configurações importantes

### Next.js
- `output: "standalone"` habilitado
- Build otimizado para produção
- Suporte a TypeScript

### Docker
- Base: `node:22-alpine`
- Multi-stage build para otimização
- Usuário não-root (`nextjs:nodejs`)
- Health check em `/api/health`

### Google Cloud Run
- Porta: 3000
- Memória: 2Gi (configurável)
- CPU: 2 (configurável)
- Timeout: 300s

## 🐛 Solução de problemas

### Erro: "pnpm-lock.yaml not found"
- ✅ **SOLUCIONADO**: Dockerfile movido para a raiz do projeto
- ✅ **SOLUCIONADO**: Build context inclui todos os arquivos necessários

### Erro: Build falha no Google Cloud Build
- ✅ **SOLUCIONADO**: Dockerfile na raiz com contexto correto
- ✅ **SOLUCIONADO**: Todos os arquivos do monorepo incluídos

### Erro: Dependências não encontradas
- ✅ **SOLUCIONADO**: Workspace pnpm configurado corretamente
- ✅ **SOLUCIONADO**: Build inclui todos os packages

## 📚 Comandos úteis

```bash
# Limpar imagens Docker
docker system prune -a

# Ver logs do container
docker logs [container_name]

# Executar com variáveis de ambiente
docker run -e NODE_ENV=production -p 3000:3000 super-gateway

# Build com cache limpo
docker build --no-cache -t super-gateway .
```

## 🔄 Atualizações

Para atualizar o Dockerfile:

1. Modifique o `Dockerfile` na raiz
2. Teste localmente com `./test-docker-build.sh`
3. Faça commit das mudanças
4. Deploy automático via GitHub Actions ou manual

## 📞 Suporte

Se encontrar problemas:

1. Execute `./test-docker-build.sh` para diagnóstico local
2. Verifique os logs do Google Cloud Build
3. Confirme que todos os arquivos necessários estão presentes
4. Verifique se o `.dockerignore` não está excluindo arquivos essenciais

