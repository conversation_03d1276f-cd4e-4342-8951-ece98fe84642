# Exemplos de Uso de Real (BRL) e Dólar (USD) no SupGateway

## 1. Configuração de Moedas

### Configuração de Internacionalização
```typescript
// config/index.ts
i18n: {
  locales: {
    pt: {
      currency: "BRL",  // Real brasileiro
      label: "Português",
    },
    en: {
      currency: "USD",  // Dólar americano
      label: "English",
    },
  },
  defaultCurrency: "BRL",  // Moeda padrão: Real
}
```

## 2. Formatação de Moedas

### Função de Formatação
```typescript
// apps/web/lib/utils.ts
export function formatCurrency(amount: number, currency: string = "BRL"): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency,
  }).format(amount);
}

// Exemplos de uso:
formatCurrency(1500)        // "R$ 1.500,00"
formatCurrency(1500, "USD") // "US$ 1.500,00"
formatCurrency(29.99, "USD") // "US$ 29,99"
```

## 3. Transações Financeiras

### Criação de Transações
```typescript
// packages/api/src/services/financial.ts
static async createTransaction(input: CreateTransactionInput) {
  const transaction = await db.transaction.create({
    data: {
      organizationId: input.organizationId,
      type: input.type,
      amount: input.amount,
      currency: input.currency || "BRL", // Padrão: Real
      description: input.description,
      // ... outros campos
    }
  });
}

// Exemplos de transações:
// Transação em Real (padrão)
const transacaoBRL = {
  organizationId: "org123",
  type: "PAYMENT",
  amount: 150.00,
  description: "Pagamento de assinatura mensal"
  // currency será "BRL" automaticamente
};

// Transação em Dólar
const transacaoUSD = {
  organizationId: "org123",
  type: "PAYMENT",
  amount: 29.99,
  currency: "USD",
  description: "Monthly subscription payment"
};
```

## 4. Planos de Pagamento

### Configuração de Preços
```typescript
// config/index.ts
payments: {
  plans: {
    pro: {
      prices: [
        {
          type: "recurring",
          interval: "month",
          amount: 29,
          currency: "USD",  // Plano mensal em dólar
        },
        {
          type: "recurring",
          interval: "year",
          amount: 290,
          currency: "USD",  // Plano anual em dólar
        },
      ],
    },
    lifetime: {
      prices: [
        {
          type: "one-time",
          amount: 799,
          currency: "USD",  // Pagamento único em dólar
        },
      ],
    },
  },
}
```

## 5. Componentes de Interface

### Hook para Moeda Local
```typescript
// apps/web/modules/shared/hooks/locale-currency.tsx
export function useLocaleCurrency() {
  const locale = useLocale();
  const localeCurrency = 
    Object.entries(config.i18n.locales).find(([key]) => key === locale)?.[1]
      .currency ?? config.i18n.defaultCurrency;
  
  return localeCurrency; // "BRL" para pt, "USD" para en
}
```

### Componente de Checkout
```typescript
// apps/web/app/checkout/components/checkout-summary-card.tsx
export function CheckoutSummaryCard({ product, offers }) {
  return (
    <div>
      {/* Preço do produto formatado */}
      <p className="font-semibold text-sm mt-1">
        {formatCurrency(product.price)}  {/* R$ 150,00 ou US$ 29,99 */}
      </p>
      
      {/* Ofertas adicionais */}
      {selectedOffers.map((offer) => (
        <span className="font-medium">
          {formatCurrency(offer.price)}  {/* R$ 50,00 ou US$ 9,99 */}
        </span>
      ))}
      
      {/* Total */}
      <div className="flex justify-between font-semibold text-lg">
        <span>Total</span>
        <span className="text-green-600">
          {formatCurrency(total)}  {/* R$ 200,00 ou US$ 39,98 */}
        </span>
      </div>
    </div>
  );
}
```

### Tabela de Preços
```typescript
// apps/web/modules/saas/payments/components/PricingTable.tsx
export function PricingTable() {
  const localeCurrency = useLocaleCurrency();
  
  // Busca preço na moeda do usuário
  let price = prices?.find(
    (price) =>
      price.currency === localeCurrency,  // BRL ou USD
  );
  
  return (
    <div>
      {/* Exibe preço na moeda correta */}
      <span>{formatCurrency(price.amount, price.currency)}</span>
    </div>
  );
}
```

## 6. Modelos de Banco de Dados

### Schema Prisma
```prisma
// packages/database/prisma/schema.prisma
model Transaction {
  id           String   @id @default(cuid())
  amount       Decimal  @db.Decimal(15, 2)  // Precisão para valores monetários
  currency     String   @default("BRL")     // Moeda padrão: Real
  // ... outros campos
}

model LedgerEntry {
  id           String   @id @default(cuid())
  amount       Decimal  @db.Decimal(15, 2)
  currency     String   @default("BRL")
  // ... outros campos
}

model BalanceSnapshot {
  id           String   @id @default(cuid())
  balance      Decimal  @db.Decimal(15, 2)
  currency     String   @default("BRL")
  // ... outros campos
}
```

## 7. Exemplos Práticos de Uso

### Cenário 1: Usuário Brasileiro
```typescript
// Usuário com locale "pt" (português)
const localeCurrency = useLocaleCurrency(); // Retorna "BRL"

// Transação criada
const transacao = {
  amount: 150.00,
  currency: "BRL",
  description: "Assinatura mensal"
};

// Exibição na interface
formatCurrency(150.00, "BRL"); // "R$ 150,00"
```

### Cenário 2: Usuário Internacional
```typescript
// Usuário com locale "en" (inglês)
const localeCurrency = useLocaleCurrency(); // Retorna "USD"

// Transação criada
const transaction = {
  amount: 29.99,
  currency: "USD",
  description: "Monthly subscription"
};

// Exibição na interface
formatCurrency(29.99, "USD"); // "US$ 29,99"
```

### Cenário 3: Conversão de Moedas (Futuro)
```typescript
// Estrutura preparada para conversão
const transactionWithConversion = {
  originalAmount: 29.99,
  originalCurrency: "USD",
  convertedAmount: 150.00,
  convertedCurrency: "BRL",
  exchangeRate: 5.00,
  description: "Converted payment"
};
```

## 8. Validações e Segurança

### Validação de Moedas
```typescript
// Moedas suportadas
const SUPPORTED_CURRENCIES = ["BRL", "USD"];

// Validação
function validateCurrency(currency: string): boolean {
  return SUPPORTED_CURRENCIES.includes(currency);
}

// Uso em transações
if (!validateCurrency(input.currency)) {
  throw new Error("Moeda não suportada");
}
```

### Precisão Decimal
```typescript
// Todos os valores monetários usam Decimal(15,2)
// 15 dígitos totais, 2 casas decimais
// Suporta valores até: 9.999.999.999.999,99

// Exemplos válidos:
// R$ 1.500,50
// US$ 29,99
// R$ 1.000.000,00
```

## Resumo

O sistema SupGateway oferece suporte robusto para múltiplas moedas:

- **BRL (Real)**: Moeda padrão para usuários brasileiros
- **USD (Dólar)**: Moeda para usuários internacionais e planos premium
- **Formatação automática**: Baseada no locale do usuário
- **Precisão decimal**: Decimal(15,2) para todos os valores monetários
- **Configuração flexível**: Suporte a expansão para novas moedas
- **Interface adaptativa**: Exibição correta baseada na moeda selecionada