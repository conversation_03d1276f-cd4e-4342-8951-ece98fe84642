# Deploy no Google Cloud Run

Este guia explica como fazer deploy da sua aplicação supastarter no Google Cloud Run.

## Pré-requisitos

1. Google Cloud SDK instalado
2. Projeto Google Cloud configurado
3. Docker instalado localmente (para testes)

## Configurações Realizadas

### 1. Next.js Config
- ✅ Adicionado `output: "standalone"` no `next.config.ts`
- ✅ Configuração otimizada para Docker

### 2. Dockerfile
- ✅ Multi-stage build otimizado
- ✅ Suporte completo ao pnpm e monorepo
- ✅ Usuário não-root para segurança
- ✅ Otimizado para Google Cloud Run

### 3. .dockerignore
- ✅ Exclusão de arquivos desnecessários
- ✅ Otimização do contexto de build

## Teste Local

Antes de fazer deploy, teste a imagem localmente:

```bash
# Build da imagem
docker build -f apps/web/Dockerfile . --no-cache -t super-gateway:latest

# Executar container
docker run -p 3000:3000 \
  -e DATABASE_URL="sua_url_do_banco" \
  -e NEXTAUTH_SECRET="seu_secret" \
  -e NEXTAUTH_URL="http://localhost:3000" \
  super-gateway:latest
```

## Deploy no Google Cloud Run

### 1. Build e Push da Imagem

```bash
# Configurar projeto
gcloud config set project SEU_PROJECT_ID

# Configurar região
gcloud config set run/region us-central1

# Build e push da imagem
gcloud builds submit --tag gcr.io/SEU_PROJECT_ID/super-gateway:latest

# Ou usando Docker local
docker build -f apps/web/Dockerfile . -t gcr.io/SEU_PROJECT_ID/super-gateway:latest
docker push gcr.io/SEU_PROJECT_ID/super-gateway:latest
```

### 2. Deploy no Cloud Run

```bash
gcloud run deploy super-gateway \
  --image gcr.io/SEU_PROJECT_ID/super-gateway:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 3000 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10 \
  --set-env-vars NODE_ENV=production
```

### 3. Configurar Variáveis de Ambiente

```bash
gcloud run services update super-gateway \
  --set-env-vars \
  DATABASE_URL="sua_url_do_banco",\
  NEXTAUTH_SECRET="seu_secret",\
  NEXTAUTH_URL="https://sua-app.run.app",\
  NEXTAUTH_URL_INTERNAL="https://sua-app.run.app"
```

## Variáveis de Ambiente Necessárias

Certifique-se de configurar estas variáveis no Cloud Run:

- `DATABASE_URL`: URL do seu banco de dados
- `NEXTAUTH_SECRET`: Secret para autenticação
- `NEXTAUTH_URL`: URL pública da aplicação
- `NEXTAUTH_URL_INTERNAL`: URL interna (mesma da pública)
- `GOOGLE_CLIENT_ID`: ID do cliente Google OAuth
- `GOOGLE_CLIENT_SECRET`: Secret do cliente Google OAuth
- `AWS_ACCESS_KEY_ID`: Chave de acesso AWS (se usar S3)
- `AWS_SECRET_ACCESS_KEY`: Chave secreta AWS
- `AWS_REGION`: Região AWS
- `AWS_S3_BUCKET`: Bucket S3

## Otimizações para Produção

### 1. Configurar Auto-scaling
```bash
gcloud run services update super-gateway \
  --min-instances 1 \
  --max-instances 20 \
  --cpu-throttling
```

### 2. Configurar Health Checks
```bash
gcloud run services update super-gateway \
  --health-checks /api/health
```

### 3. Configurar Timeout
```bash
gcloud run services update super-gateway \
  --timeout 300
```

## Monitoramento

### 1. Logs
```bash
gcloud logs tail --service=super-gateway
```

### 2. Métricas
```bash
gcloud run services describe super-gateway
```

## Troubleshooting

### Erro de Build
- Verifique se todas as dependências estão no `package.json`
- Confirme que o `turbo prune` está funcionando

### Erro de Runtime
- Verifique as variáveis de ambiente
- Confirme a conectividade com o banco de dados
- Verifique os logs do Cloud Run

### Performance
- Ajuste `--memory` e `--cpu` conforme necessário
- Configure `--max-instances` baseado no tráfego esperado

## Custos

- Cloud Run cobra por requisição e tempo de execução
- Configure `--min-instances 0` para economizar quando não houver tráfego
- Use `--cpu-throttling` para otimizar custos

## Segurança

- ✅ Usuário não-root no container
- ✅ Porta 3000 exposta
- ✅ Variáveis de ambiente seguras
- ✅ Health checks configurados

## Próximos Passos

1. Configure um domínio personalizado
2. Configure HTTPS automático
3. Configure CDN para assets estáticos
4. Implemente monitoramento avançado
5. Configure backup automático do banco
