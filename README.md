# SupGateway

A modern digital platform built with Next.js, Prisma, and TypeScript.

## Features

- 🔐 Authentication with multiple providers
- 🏢 Multi-tenant organization system
- 💳 Payment processing
- 📚 Digital products and courses
- 👥 User management with roles
- 🎨 Modern UI with Tailwind CSS
- 📊 Analytics and reporting
- 🌍 Internationalization support

## Getting Started

### Prerequisites

- Node.js 20+
- PostgreSQL database
- pnpm package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd super-gateway
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up the database:
```bash
# Run migrations
pnpm --filter database push

# Seed the database with example data
pnpm --filter scripts seed:db
```

5. Start the development server:
```bash
pnpm dev
```

## Development Scripts

### User Management

Create a new user with admin privileges:
```bash
pnpm --filter scripts create:user
```

This interactive script allows you to:
- Create users with different roles (USER, TEACHER, AFFILIATE, ADMI<PERSON>, <PERSON><PERSON><PERSON>_ADMIN)
- Automatically create role-specific profiles
- Optionally create an organization for the user
- Generate secure passwords

### Database Seeding

Populate the database with example data:
```bash
pnpm --filter scripts seed:db
```

This creates:
- Admin user and organization
- Sample users with different roles
- Product categories and sample products
- Coupons and other test data

**Default credentials after seeding:**
- Admin: `<EMAIL>` / `admin123`
- Teacher: `<EMAIL>` / `teacher123`
- Affiliate: `<EMAIL>` / `affiliate123`
- User: `<EMAIL>` / `user123`

### Database Reset

Reset the database and optionally re-seed:
```bash
pnpm --filter scripts reset:db
```

⚠️ **Warning:** This command deletes ALL data from the database!

## Project Structure

```
apps/
├── web/                 # Next.js web application
│   ├── app/            # App router pages
│   └── modules/        # Feature modules
packages/
├── database/           # Prisma schema and client
├── auth/              # Authentication configuration
├── api/               # API routes and handlers
├── payments/          # Payment processing
├── mail/              # Email templates and sending
├── i18n/              # Internationalization
├── logs/              # Logging utilities
├── storage/           # File storage
└── utils/             # Shared utilities
tooling/
├── scripts/           # Development scripts
├── tailwind/          # Tailwind configuration
└── typescript/        # TypeScript configuration
```

## Key Features

### Multi-tenant Organizations
- Organizations with custom branding
- Role-based access control
- Subscription management
- Custom domains support

### Digital Products
- Courses with modules and lessons
- E-books and digital downloads
- Subscription products
- Affiliate program

### Payment System
- Multiple payment providers
- Subscription billing
- Commission tracking
- Financial reporting

### User Roles
- **USER**: Regular customers
- **TEACHER**: Content creators and instructors
- **AFFILIATE**: Partners earning commissions
- **ADMIN**: Organization administrators
- **SUPER_ADMIN**: Platform administrators

## Environment Variables

Key environment variables needed:

```env
# Database
DATABASE_URL="postgresql://..."

# Authentication
AUTH_SECRET="your-secret-key"
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"

# Payments
STRIPE_SECRET_KEY="sk_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Email
RESEND_API_KEY="re_..."

# Storage
CLOUDFLARE_R2_ACCESS_KEY_ID="..."
CLOUDFLARE_R2_SECRET_ACCESS_KEY="..."
```

## Deployment

The application is optimized for deployment on Vercel or similar platforms.

1. Set up your environment variables
2. Connect your database
3. Deploy the application
4. Run database migrations in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
